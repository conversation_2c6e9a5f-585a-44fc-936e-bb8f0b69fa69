import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from '../user'
import { getShoppingCartAdd, getCart, getShoppingCartRemove, getShoppingCartUpdate,getShoppingCartBatchRemove,getShoppingCartApplyCoupon,getShoppingCartSaveShippingMethod, getShoppingCartClear,getShoppingCartPaymentIcons } from '~/api/shop'

// 购物车商品接口 - 根据后端API返回的数据结构定义
interface CartItem {
  cart_item_id: string
  quantity: number
  sku: string
  type: string
  name: string
  price: number
  base_price: number
  total: number
  base_total: number
  tax_percent: number
  weight:string
  tax_amount: number
  base_tax_amount: number
  discount_percent: number
  discount_amount: number
  base_discount_amount: number
  product_id: number
  product: {
    product_id: number
    type: string
    name: string
    url_key: string
    base_image: string
  }
  attributes?: {
    color?: {
      option_label?: string
    }
    brand?: {
      option_label?: string
    }
    device?: {
      option_label?: string
    }
  }
  formatted_price: string
  formatted_base_price: string
  formatted_total: string
  formatted_base_total: string
}

// 购物车数据接口
interface CartData {
  cart_id: number
  customer_id: number
  customer_email: string
  customer_first_name: string
  customer_last_name: string
  shipping_method: string
  coupon_code: string
  is_gift: boolean
  is_guest: boolean
  is_active: boolean
  items_count: number
  items_qty: number
  grand_total: number
  base_grand_total: number
  sub_total: number
  base_sub_total: number
  sub_total_incl_tax: number
  tax_total: number
  base_tax_total: number
  discount_amount: number
  base_discount_amount: number
  shipping_amount: number
  base_shipping_amount: number
  shipping_amount_incl_tax: number
  checkout_method: string
  conversion_time: string
  created_at: string
  updated_at: string
  applied_taxes: Array<{
    rate: number
    amount: string
  }>
  items: CartItem[]
  formatted_prices: {
    grand_total: string
    base_grand_total: string
    sub_total: string
    base_sub_total: string
    sub_total_incl_tax: string
    tax_total: string
    base_tax_total: string
    discount_amount: string
    base_discount_amount: string
    shipping_amount: string
    base_shipping_amount: string
    shipping_amount_incl_tax: string
  }
  billing_address: {
    id: number
    address_type: string
    customer_id: number
    cart_id: number
    order_id: number
    first_name: string
    last_name: string
    gender: string
    company_name: string
    address1: string
    address2: string
    postcode: string
    city: string
    state: string
    country: string
    email: string
    phone: string
    vat_id: string
    default_address: boolean
    created_at: string
    updated_at: string
  } | null
  shipping_address: {
    id: number
    address_type: string
    customer_id: number
    cart_id: number
    order_id: number
    first_name: string
    last_name: string
    gender: string
    company_name: string
    address1: string
    address2: string
    postcode: string
    city: string
    state: string
    country: string
    email: string
    phone: string
    vat_id: string
    default_address: boolean
    created_at: string
    updated_at: string
  } | null
  has_stockable_items: boolean
  have_stockable_items: boolean
  payment_method: string
  payment_method_title: string
}

export const useShoppingCartStore = defineStore('shoppingCart', () => {
  // 购物车商品列表
  const cartItems = ref<CartItem[]>([])
  
  // 购物车完整数据
  const cartData = ref<CartData | null>(null)
  
  // 获取用户store
  const userStore = useUserStore()
  
  // 计算属性：购物车商品总数
  const totalItems = computed(() => {
    return cartItems.value.reduce((total, item) => total + (item.quantity || 0), 0)
  })
  
  // 计算属性：购物车总价
  const totalPrice = computed(() => {
    return cartItems.value.reduce((total, item) => total + ((item.price || 0) * (item.quantity || 0)), 0)
  })
  
  // 检查用户是否已登录
  const isLoggedIn = computed(() => {
    return !!userStore.token && userStore.isLoggedIn
  })
  
  // 添加商品到购物车
  const addToCart = async (product: {
    id: string
    name: string
    base_image: string
    formatted_price: string
    short_description: string
    product_id?: number
    price?: number
    selectedColor?: string
    attributes?: {
      color?: {
        option_label?: string
      }
      brand?: {
        option_label?: string
      }
      device?: {
        option_label?: string
      }
    }
    product?: unknown
  }, quantity: number = 1) => {
    try {
      console.warn('shopping-cart store: addToCart 被调用', product)
      console.warn('shopping-cart store: 登录状态:', isLoggedIn.value)
      
      if (isLoggedIn.value) {
        // 已登录状态：调用接口
        console.warn('shopping-cart store: 用户已登录，调用API')
        const result = await getShoppingCartAdd({
          product_id: product.id,
          quantity: quantity,
          color: product?.selectedColor,
        })
        console.warn('shopping-cart store: API调用完成，购物车数据已更新', result)
        
        // 重新获取购物车数据
        if(result.success){
          await fetchCartData()
          return result
        } else {
          // 如果API调用失败，返回错误信息
          return { success: false, message: result.message || '添加失败' }
        }
      } else {
        // 未登录状态：添加到本地缓存
        console.warn('shopping-cart store: 用户未登录，添加到本地缓存')
        addToLocalCart(product, quantity)
        console.warn('shopping-cart store: 本地缓存更新完成')
        return { success: true, message: '添加成功（本地购物车）' }
      }
    } catch (error) {
      console.error('shopping-cart store: 添加购物车失败:', error)
      return { success: false, message: '添加失败' }
    }
  }
  // 保存配送方式
  const saveShippingMethod = async (shippingMethod: string) => {
    const info:object = {
      shipping_method: shippingMethod
    }
    const result = await getShoppingCartSaveShippingMethod(info)
    return result
  }
  // 本地缓存添加商品
  const addToLocalCart = (product: {
    id: string
    name: string
    base_image: string
    formatted_price: string
    short_description: string
    product_id?: string
    price?: number
    selectedColor?: string
    attributes?: {
      color?: {
        option_label?: string
      }
      brand?: {
        option_label?: string
      }
      device?: {
        option_label?: string
      }
    }
    product?: unknown
  }, quantity: number) => {
    console.warn('shopping-cart store: addToLocalCart 被调用')
    console.warn('shopping-cart store: 当前购物车商品数量:', cartItems.value.length)

    const existingItem = cartItems.value.find(item => 
      String(item.product_id) === String(product.product_id) && 
      item.product?.type === 'configurable' // 可配置产品需要检查颜色和尺寸
    )

    if (existingItem) {
      // 如果商品已存在，增加数量
      console.warn('shopping-cart store: 商品已存在，增加数量')
      existingItem.quantity += quantity
      console.warn('shopping-cart store: 商品数量已更新:', existingItem.quantity)
    } else {
      // 如果商品不存在，添加新商品
      console.warn('shopping-cart store: 商品不存在，添加新商品')
      const newItem: CartItem = {
        cart_item_id: Date.now().toString(), // 临时ID
        quantity: quantity,
        sku: '',
        type: 'simple',
        name: product.name,
        price: product.price ?? 0,
        base_price: product.price ?? 0,
        total: (product.price ?? 0) * quantity,
        base_total: (product.price ?? 0) * quantity,
        tax_percent: 0,
        weight: '0', // 添加weight字段
        tax_amount: 0,
        base_tax_amount: 0,
        discount_percent: 0,
        discount_amount: 0,
        base_discount_amount: 0,
        product_id: Number(product.product_id) || 0, // 确保是number类型
        product: {
          product_id: Number(product.product_id) || 0,
          type: 'simple',
          name: product.name,
          url_key: '',
          base_image: product.base_image // 修正图片字段
        },
        attributes: product.attributes,
        formatted_price: `$${(product.price ?? 0).toFixed(2)}`,
        formatted_base_price: `$${(product.price ?? 0).toFixed(2)}`,
        formatted_total: `$${((product.price ?? 0) * quantity).toFixed(2)}`,
        formatted_base_total: `$${((product.price ?? 0) * quantity).toFixed(2)}`,
      }
      cartItems.value.push(newItem)
      console.warn('shopping-cart store: 新商品已添加:', newItem)
    }

    console.warn('shopping-cart store: 更新后购物车商品数量:', cartItems.value.length)
  }
  // 批量删除购物车商品
  const batchRemoveCart = async (itemIds: string[]) => {
    const info:object = {
      ids : itemIds
    }
    const result = await getShoppingCartBatchRemove(info)
    return result
  }
  // 获取支付方式图标
  const getPaymentMethod = async () => {
    const result = await getShoppingCartPaymentIcons()
    return result
  }
  
  // 从购物车移除商品
  const removeFromCart = async (itemId: string) => {
    try {
      if (isLoggedIn.value) {
        // 已登录状态：调用接口
        await getShoppingCartRemove({
          cart_item_id: itemId
        })
        
        // 重新获取购物车数据
        await fetchCartData()
      } else {
        // 未登录状态：从本地缓存移除
        const index = cartItems.value.findIndex(item => item.cart_item_id === itemId)
        if (index > -1) {
          cartItems.value.splice(index, 1)
        }
      }
      
      return { success: true, message: '移除成功' }
    } catch (error) {
      console.error('移除购物车商品失败:', error)
      return { success: false, message: '移除失败' }
    }
  }
  // 应用优惠券
  const applyCoupon = async (couponCode: string) => {
    const info:object = {
      code: couponCode
    }
    const result = await getShoppingCartApplyCoupon(info)
    console.warn('📄 购物车store: 应用优惠券结果:', result)
    return result
  }
  // 更新商品数量
  const updateQuantity = async (itemId: string, quantity: number) => {
    try {
      if (isLoggedIn.value) {
        console.warn('登录状态下更新商品数量', itemId, quantity)
        const info:object = {
          qty: {
            [itemId]: quantity.toString()
          }
        }
        // 已登录状态：调用接口
        const result = await getShoppingCartUpdate(info)
        console.warn('登录状态下更新商品数量结果', result)
        // 重新获取购物车数据
        if(result.success){
          await fetchCartData()
        }
        return result
      } else {
        console.warn('未登录状态下更新商品数量', itemId, quantity)
        // 未登录状态：更新本地缓存
        const item = cartItems.value.find(item => item.cart_item_id === itemId)
        if (item) {
          item.quantity = quantity
          item.total = item.price * quantity
          item.base_total = item.base_price * quantity
          item.formatted_total = `$${item.total.toFixed(2)}`
          item.formatted_base_total = `$${item.base_total.toFixed(2)}`
        }
        return { success: true, message: '更新成功' }
      }
    } catch (error) {
      console.error('更新购物车商品数量失败:', error)
      return { success: false, message: '更新失败' }
    }
  }
  
  // 获取购物车数据
  const fetchCartData = async () => {
    try {
      console.warn('🛒 购物车store: 开始获取购物车数据')
      console.warn('🛒 购物车store: 用户token:', userStore.token ? '存在' : '不存在')
      console.warn('🛒 购物车store: 登录状态:', userStore.isLoggedIn)
      console.warn('🛒 购物车store: 计算的登录状态:', isLoggedIn.value)
      
      if (isLoggedIn.value) {
        // 已登录状态：从接口获取
        console.warn('🛒 购物车store: 用户已登录，开始调用API')
        const response = await getCart()
        console.warn('🛒 购物车store: API响应:', response)
        
        if (response && response.data) {
          // 更新完整的购物车数据
          cartData.value = response.data
          
          // 更新购物车商品列表
          if (Array.isArray(response.data.items)) {
            cartItems.value = response.data.items
            console.warn('🛒 购物车store: 成功更新购物车数据，商品数量:', response.data.items.length)
          } else {
            console.warn('🛒 购物车store: API返回数据格式错误，期望数组:', response.data.items)
            cartItems.value = []
          }
        } else {
          console.warn('🛒 购物车store: API响应为空或无data字段')
          cartData.value = null
          cartItems.value = []
        }
      } else {
        console.warn('🛒 购物车store: 用户未登录，使用本地缓存数据，当前商品数量:', cartItems.value.length)
      }
    } catch (error) {
      console.error('🛒 购物车store: 获取购物车数据失败:', error)
      // 不清空现有数据，保持用户体验
    }
  }
  
  // 清空购物车
  const clearCart = async () => {
    try {
      if (isLoggedIn.value) {
        // 已登录状态：调用接口清空购物车
        await getShoppingCartClear()
        await fetchCartData()
      } else {
        // 未登录状态：清空本地缓存
        cartItems.value = []
        cartData.value = null
      }
      return { success: true, message: '购物车已清空' }
    } catch (error) {
      console.error('清空购物车失败:', error)
      return { success: false, message: '清空购物车失败' }
    }
  }
  
  // 获取购物车统计信息
  const getCartStats = () => {
    const totalItems = cartItems.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
    const totalPrice = cartItems.value.reduce((sum, item) => sum + ((item.price || 0) * (item.quantity || 0)), 0)
    const totalWeight = cartItems.value.reduce((sum, item) => sum + (parseFloat(item.weight || '0') * (item.quantity || 0)), 0)
    
    return {
      totalItems,
      totalPrice,
      totalWeight: totalWeight.toFixed(3),
      itemCount: cartItems.value.length
    }
  }
  
  // 检查购物车是否为空
  const isEmpty = computed(() => {
    return cartItems.value.length === 0
  })
  
  // 获取购物车中选中的商品（用于批量操作）
  const getSelectedItems = (selectedIds: string[]) => {
    return cartItems.value.filter(item => selectedIds.includes(item.cart_item_id))
  }
  
  // 计算选中商品的总价
  const getSelectedItemsTotal = (selectedIds: string[]) => {
    const selectedItems = getSelectedItems(selectedIds)
    return selectedItems.reduce((total, item) => total + ((item.price || 0) * (item.quantity || 0)), 0)
  }
  
  // 检查商品是否在购物车中
  const isInCart = (productId: number, color?: string, size?: string) => {
    return cartItems.value.some(item => {
      // 基础产品ID匹配
      const productMatch = item.product_id === productId
      
      // 如果是可配置产品，需要检查颜色和尺寸
      if (item.product?.type === 'configurable') {
        const colorMatch = !color || item.attributes?.color?.option_label === color
        const sizeMatch = !size || item.attributes?.device?.option_label === size
        return productMatch && colorMatch && sizeMatch
      }
      
      // 简单产品只需要ID匹配
      return productMatch
    })
  }
  
  // 获取购物车中指定商品的数量
  const getItemQuantity = (productId: number, color?: string, size?: string) => {
    const item = cartItems.value.find(item => {
      // 基础产品ID匹配
      const productMatch = item.product_id === productId
      
      // 如果是可配置产品，需要检查颜色和尺寸
      if (item.product?.type === 'configurable') {
        const colorMatch = !color || item.attributes?.color?.option_label === color
        const sizeMatch = !size || item.attributes?.device?.option_label === size
        return productMatch && colorMatch && sizeMatch
      }
      
      // 简单产品只需要ID匹配
      return productMatch
    })
    return item ? item.quantity : 0
  }
  
  return {
    cartItems,
    cartData,
    totalItems,
    totalPrice,
    isLoggedIn,
    isEmpty,
    addToCart,
    removeFromCart,
    updateQuantity,
    fetchCartData,
    clearCart,
    isInCart,
    getItemQuantity,
    batchRemoveCart,
    applyCoupon,
    saveShippingMethod,
    getCartStats,
    getSelectedItemsTotal,
    getPaymentMethod
  }
}, {
  persist: {
    storage: import.meta.client ? localStorage : undefined
  }
})

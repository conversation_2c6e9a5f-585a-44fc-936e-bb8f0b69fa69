# 产品详情页面国际化实现

## 概述

为产品详情页面 (`pages/index/product-details/[url_key].html.vue`) 添加了完整的国际化支持，支持中文、英文和意大利语。

## 新增翻译键

### 产品详情页面 (productDetails)

#### 基础信息
- `productDetails.loading` - 加载中提示
- `productDetails.productNotFound` - 产品不存在提示
- `productDetails.description` - 描述标签
- `productDetails.specification` - 规格标签

#### 产品规格
- `productDetails.dimensions` - 尺寸
- `productDetails.profile` - 配置
- `productDetails.dropRating` - 跌落等级

#### 设备选择
- `productDetails.device` - 设备标签
- `productDetails.selectBrand` - 选择品牌占位符
- `productDetails.selectDevice` - 选择设备占位符

#### 颜色和数量
- `productDetails.color` - 颜色标签
- `productDetails.quantity` - 数量标签

#### 购物操作
- `productDetails.addToCart` - 加入购物车按钮
- `productDetails.alreadyInCart` - 已在购物车中提示 (支持数量参数)
- `productDetails.buyNow` - 立即购买按钮
- `productDetails.pleaseSelectColor` - 请选择颜色提示
- `productDetails.productInCart` - 商品已在购物车提示

#### 支付和优惠
- `productDetails.checkoutSecurely` - 安全结账标题
- `productDetails.acceptedPayments` - 接受的支付方式
- `productDetails.interestFreePayments` - 免息付款信息
- `productDetails.learnMore` - 了解更多链接
- `productDetails.shippingUpTo` - 运费优惠信息 (支持百分比参数)

#### 相关产品
- `productDetails.relatedProducts` - 相关产品标题

#### 媒体预览
- `productDetails.mediaPreview.close` - 关闭按钮
- `productDetails.mediaPreview.previous` - 上一张按钮
- `productDetails.mediaPreview.next` - 下一张按钮

### 新闻页面 (news)

#### 加载状态
- `news.loadingTags` - 加载标签中
- `news.loadTagsFailed` - 加载标签失败提示
- `news.unknownError` - 未知错误
- `news.getTagsFailed` - 获取标签列表失败
- `news.getNewsFailed` - 获取新闻列表失败

## 实现细节

### 1. 模板国际化

将硬编码的文本替换为翻译键：

```vue
<!-- 之前 -->
<div class="loading-text">加载中...</div>

<!-- 之后 -->
<div class="loading-text">{{ t('productDetails.loading') }}</div>
```

### 2. JavaScript 国际化

在 script 部分使用 `useI18n`：

```typescript
const { t } = useI18n()

// 使用翻译
ElMessage.success(t('product.addToCartSuccess'))
```

### 3. 参数化翻译

支持动态参数的翻译：

```vue
<!-- 支持数量参数 -->
{{ t('productDetails.alreadyInCart', { count: cartQuantity }) }}

<!-- 支持百分比参数 -->
{{ t('productDetails.shippingUpTo', { percent: specialPriceData.discount_percent }) }}
```

## 语言支持

### 中文 (cn.json)
- 完整的中文翻译
- 符合中文用户习惯的表达方式

### 英文 (en.json)
- 标准的英文表达
- 保持专业性和准确性

### 意大利语 (it.json)
- 完整的意大利语翻译
- 符合意大利语语法和表达习惯

## 使用方法

### 1. 在模板中使用

```vue
<template>
  <div>
    <h1>{{ t('productDetails.description') }}</h1>
    <button>{{ t('productDetails.addToCart') }}</button>
  </div>
</template>
```

### 2. 在 JavaScript 中使用

```typescript
const { t } = useI18n()

// 基础翻译
const message = t('productDetails.loading')

// 带参数的翻译
const message = t('productDetails.alreadyInCart', { count: 2 })
```

### 3. 错误消息国际化

```typescript
// 错误提示使用翻译
ElMessage.error(t('productDetails.pleaseSelectColor'))
```

## 测试

1. 切换不同语言，验证所有文本正确翻译
2. 测试动态参数是否正确显示
3. 验证错误消息在不同语言下的显示
4. 检查媒体预览功能的翻译

## 注意事项

1. 所有用户可见的文本都已国际化
2. 错误消息和提示信息也支持多语言
3. 动态内容（如价格、数量）保持原有格式
4. 媒体预览功能完全支持多语言
5. 相关产品标题已国际化

## 维护

当需要添加新的文本时：

1. 在三个语言文件中添加对应的翻译键
2. 在模板中使用 `t()` 函数
3. 在 JavaScript 中使用 `t()` 函数
4. 测试所有语言环境下的显示效果 
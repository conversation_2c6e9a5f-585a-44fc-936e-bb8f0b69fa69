import noTokenRequest from "~/utils/noTokenRequest";

// 文章数据类型定义
export interface NewsItem {
  content: string
  description: string
  id: number
  published_at: string
  tags: {
    id: number
    name: string
    slug: string
  }[]
  thumbnail: string
  title: string
  url_key: string
}

// 文章列表
export const getNews = async (params: { page: number, limit: number, tag_id: number }) => {
    return noTokenRequest.get('/api/mlk/news', params)
}

// 频道列表
export const getNewsTags = async () => {
    return noTokenRequest.get('/api/mlk/news/tags')
}

// 文章详情
export const getNewDetail = async (params: { url_key: string }) => {
    return noTokenRequest.get('/api/mlk/news/detail?url_key='+params.url_key)
}



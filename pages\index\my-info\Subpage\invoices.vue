<template>
    <div class="table-page">
        <div class="table-title">
            <span>My Invoice</span>
        </div>
        <div class="table">
            <el-table :data="tableData" stripe style="width: 100%">
                <el-table-column prop="Invoice" label="Invoice"/>
                <el-table-column prop="Order" label="Order"/>
                <el-table-column prop="Due" label="Due"/>
                <el-table-column prop="Date" label="Due Date"/>
                <el-table-column prop="Amount" label="Amount"/>
                <el-table-column prop="address" label="Status">
                    <template #default>
                        <el-tag type="success">PAID</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="Actions">
                    <template #default>
                        <el-tag type="primary">view</el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="paging">
            <div class="paging-box">
                <el-pagination size="large" background layout="prev, pager, next" prev-icon="CaretLeft" next-icon="CaretRight" :total="pagination.total" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useTableStore } from '~/stores/table';

const tableStore = useTableStore()
const pagination = ref({
    page: 1,
    per_page: 10,
    total: 0,
})

// interface tableDataType {
//     orders: []
//     pagination: {
//         page: number,
//         per_page: number,
//         total: number,
//     }
// }

const getTableItem = async ()=> {
    const res = await tableStore.getTableItems({
        page: 1,
        per_page: 10,
        status: '',
    })
    if (res && res.success) {
        console.warn('订单详情数据==========================>', res)
        if (res && res.pagination) {
            pagination.value = res.pagination
        } else {
            pagination.value = {
                page: 1,
                per_page: 10,
                total: 0,
            }
        }
    } else {
        ElMessage.error(res?.message || '请求失败')
    }
}

onMounted(() => {
    getTableItem()
})

const tableData = ref([
    {
        Invoice: 'INV-10843',
        Order: 'ORD-7842',
        Due: '2024年10月28日',
        Date: '2024年11月28日',
        Amount:'$245.99'
    },
    {
        Invoice: 'INV-10843',
        Order: 'ORD-7842',
        Due: '2024年10月28日',
        Date: '2024年11月28日',
        Amount:'$245.99'
    },
    {
        Invoice: 'INV-10843',
        Order: 'ORD-7842',
        Due: '2024年10月28日',
        Date: '2024年11月28日',
        Amount:'$245.99'
    },
    {
        Invoice: 'INV-10843',
        Order: 'ORD-7842',
        Due: '2024年10月28日',
        Date: '2024年11月28日',
        Amount:'$245.99'
    },
])
</script>

<style scoped lang="scss">
.table-page {
    width: 100%;

    .table-title {
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }
    .table {
        ::v-deep .el-table th.el-table__cell{
            background-color: #EBEBEB;
        }
        ::v-deep .el-table .cell {
            text-align: center;
        }
        ::v-deep .el-table__cell {
            background-color: #F5F5F5;
        }
        ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
            background-color: #FFFFFF;
        }
    }
    .paging {
        margin: 50px 0px;
        .paging-box {
            width: 25rem;
            margin: 0 auto;
            ::v-deep .el-pagination.is-background .el-pager li.is-active{
                background-color: #000000;
                color: var(--el-color-white)
            }
        }
    }
}
</style>
<template>
    <div class="shipmentTracking-page">
        <div class="shipmentTracking-title">
            <span>Shipment Tracking</span>
        </div>
        <div class="shipmentTracking-table">
            <el-table :data="tableData" stripe style="width: 100%">
                <el-table-column prop="OrderNumber" label="Order Number" />
                <el-table-column prop="TrackingNumber" label="Tracking Number" />
                <el-table-column prop="Carrier" label="Carrier" />
                <el-table-column prop="ShippingMethod" label="Shipping Method" />
            </el-table>
        </div>
        <div class="result">
            <div class="result-title">
                <img src="~/assets/images/state_black.svg" alt="">
                <div class="text">
                    <span>In Transit</span>
                    <div style="font-size: 1.25rem;margin-left: .625rem;font-weight: 500;">Estimated Delivery Date</div>
                    <div style="font-size: 1rem;margin-left: .625rem;font-weight: 400;">Monday, October 19, 2023</div>
                    <div style="font-size: 1rem;margin-left: .625rem;font-weight: 400;">Your package is on track to be delivered on time.</div>
                </div>
            </div>
        </div>
        <div class="process">
            <div class="step">
                <el-steps direction="vertical" space="20%" :active="1">
                    <el-step v-for="(item,index) in processData" :key="index">
                        <template #icon>
                            <img :src="item.img" alt="">
                        </template>
                        <template #title>
                            <div class="step-title">
                                <span>{{ item.title }}</span>
                            </div>
                        </template>
                        <template #description>
                            <div class="state">
                                <span>{{ item.state }}</span>
                            </div>
                            <div class="address">
                                <span>{{ item.address }}</span>
                            </div>
                        </template>
                    </el-step>
                </el-steps>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tableData = ref([
    {
        OrderNumber: '#2023101512345',
        TrackingNumber: 'UPS1234567890',
        Carrier: 'UPS',
        ShippingMethod: 'Standard Shipping'
    }
])

const processData = ref([
    {
        title: 'October 17, 2023, 08:25 AM EST',
        state: 'Shipment departed',
        address: 'Chicago, IL Distribution Center',
        img:'/_nuxt/assets/images/state_black.svg'
    },
    {
        title: 'October 16, 2023, 10:15 PM EST',
        state: 'Arrived at UPS Facility',
        address: 'Chicago, IL',
        img:'/_nuxt/assets/images/state_raw.png'
    },
    {
        title: 'October 16, 2023, 02:30 PM EST',
        state: 'Departed UPS Facility',
        address: 'Louisville, KY',
        img:'/_nuxt/assets/images/state_raw.png'
    },
    {
        title: 'October 16, 2023, 09:45 AM EST',
        state: "Arrived at UPS Facility",
        address: 'Louisville, KY',
        img:'/_nuxt/assets/images/state_raw.png'
    },
    {
        title: 'October 15, 2023, 05:20 PM EST',
        state: 'Package has been processed',
        address: "Shipment information sent to UPS",
        img:'/_nuxt/assets/images/state_raw.png'
    },
    {
        title: 'October 15, 2023, 01:30 PM EST',
        state: "Order Shipped",
        address: 'Your order has been shipped',
        img:'/_nuxt/assets/images/state_raw.png'
    }
])
</script>

<style scoped lang="scss">
::v-deep .el-step__line-inner {
    border-style: dashed;
}

.shipmentTracking-page {
    width: 100%;

    .shipmentTracking-title {
        padding: 1.25rem 0;
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }

    .shipmentTracking-table {
        ::v-deep .el-table th.el-table__cell {
            background-color: #EBEBEB;
        }

        ::v-deep .el-table .cell {
            text-align: center;
        }

        ::v-deep .el-table__cell {
            background: #FFFFFF;
        }

        ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
            background-color: #FFFFFF;
        }
    }

    .result {
        height: 9.125rem;
        background: #F5F5F5;
        border-left: .1875rem solid #E3DED2;

        .result-title {
            padding: 1.25rem 0 0 2.5625rem;
            height: 1.875rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: bold;
            font-size: 1.125rem;
            color: #3D3D3D;
            text-align: left;
            font-style: normal;
            display: flex;

            img {
                width: 1.25rem;
                height: 1.25rem;
                margin-top: .3125rem;
            }

            .text {
                display: block;
                span {
                    line-height: 1.875rem;
                    margin-left: .625rem;
                }
                div {
                    margin: .3125rem 0
                }
            }
        }
    }
    .process {
        height: 45.625rem;
        background: #FFFFFF;
        border-radius: .5rem;
        .step {
            margin: 0 0 0 2.5rem;
            padding-top: 1.8125rem;
            height: 40.3125rem;
            .step-title {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 1rem;
                color: #3D3D3D;
                text-align: left;
                font-style: normal;
            }
            .state {
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                margin-top: .375rem;
                color: #3D3D3D;
                text-align: left;
                font-style: normal;
            }
            .address {
                font-family: PingFangSC, PingFang SC;
                margin-top: .375rem;
                font-weight: 400;
                font-size: 1rem;
                color: #3D3D3D;
                text-align: left;
                font-style: normal;
            }
        }
    }
}
</style>
<template>
  <div class="title">
    {{ title }}
  </div>
</template>

<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: ''
    },
})

</script>

<style scoped lang="scss">
@font-face {
  font-family: 'AlimamaShuHeiTi-Bold';
  src: url('~/assets/font/AlimamaShuHeiTi-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

.title {
  width: 100%;
  height: 3.125rem;
  text-align: center;
  margin: 1rem 0;
  font-weight: bold;
  font-size: 1.875rem;
  letter-spacing: 0.1rem;
  font-family: 'AlimamaShuHeiTi-Bold', sans-serif;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>
import noTokenRequest from "~/utils/noTokenRequest";
import httpRequest from "~/utils/request";


interface dataType{
  email?: string,
} 

interface paramsType{
  query?: string,
  category_id?: string,
  price?: string,
  sort?: string,
  order?: string,
  limit?: string,
  page?: string,
  size?: string,
  locale?: string,
  new?: string,
  featured?:string
}


// 获取语种
export const getLanguages = async () => {
    return noTokenRequest.get('/api/mlk/common/locales')
}

// 获取导航栏列表
export const getNavbar = async () => {
    return noTokenRequest.get('/api/mlk/common/navbar')
}

// 页眉优惠信息
export const getHeaderOffer = async () => {
    return noTokenRequest.get('/api/mlk/common/header-offer')
}

// 产品搜索  new 1 新品  featured 1 热卖产品
export const getProductSearch = async (params:paramsType) => {
    return noTokenRequest.get('/api/mlk/search',params)
}

// 获取底部页面内容
export const getFooterData = async () => {
    return noTokenRequest.get('/api/mlk/common/footer')
}

// 邮箱订阅
export const emailSubscribe = async (data:dataType) => {
    return httpRequest.post('/api/mlk/subscription/subscribe',data)
}

// 所有产品和品牌列表
export const getBrandList = async () => {
  return httpRequest.post('/api/mlk/common/brands')
}

import { login, register, registerToken, logout as logoutApi } from '~/api/user'

export const useUserStore = defineStore("user", () => {
  interface userInfoType { 
    created_at?: string,
    date_of_birth?: string,
    email?: string,
    first_name?: string,
    gender?: string,
    id?: string,
    status?: number,
    profile?: string,
    phone?: string,
    name?: string,
    last_name?: string,
    image_url?: string,
    is_wholesale?: string,
    group?: {
        code?: string,
        created_at?: string,
        is_user_defined?: number,
        name?: string,
        updated_at?: string,
    }
  }

  interface loginDataType {
    email?: string,
    password?: string,
    first_name?: string,
    last_name?: string,
    phone?: string,
    user_type?: string,
  }

  const token = ref("");
  const expiresAt = ref("");
  // 用户信息存储
  const userInfo = ref<userInfoType>({}); 
  // 登录状态
  const isLoggedIn = ref(false);
  // 加载状态
  const loading = ref(false);
  // token续期定时器
  const tokenRefreshTimer = ref<NodeJS.Timeout | null>(null);
  // 批发用户状态
  const isWholesale = ref(false);

  // 设置token
  function setToken(val: string) {
    token.value = val;
  }

  // 设置过期时间
  function setExpiresAt(val: string) {
    expiresAt.value = val;
  }

  // 设置用户信息
  function setUserInfo(info: userInfoType) {
    userInfo.value = info;
  }

  // 设置登录状态
  function setLoginStatus(status: boolean) {
    isLoggedIn.value = status;
  }
  // 判断用户是否是批发用户
  function isWholesaleUser() {
    return userInfo.value.group?.code === 'wholesale';
  }

  // 检查token是否在指定天数内过期
  function isTokenExpiringSoon(days: number = 1): boolean {
    if (!expiresAt.value) return false;
    
    const expirationDate = new Date(expiresAt.value);
    const now = new Date();
    const oneDayInMs = days * 24 * 60 * 60 * 1000;
    
    return (expirationDate.getTime() - now.getTime()) <= oneDayInMs;
  }

  // 检查token是否已过期
  function isTokenExpired(): boolean {
    if (!expiresAt.value) return true;
    
    const expirationDate = new Date(expiresAt.value);
    const now = new Date();
    
    return expirationDate <= now;
  }

  // 自动续期token
  async function autoRefreshToken(): Promise<boolean> {
    try {
      console.warn('🔄 开始自动续期token...');
      const response = await refreshUserToken();
      if (response.data && response.data.token) {
        console.warn('✅ token续期成功');
        // 重新设置定时器
        setupTokenRefreshTimer();
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ token续期失败:', error);
      // 续期失败，清除用户状态
      logout();
      return false;
    }
  }

  // 设置token续期定时器
  function setupTokenRefreshTimer() {
    // 清除现有定时器
    if (tokenRefreshTimer.value) {
      clearInterval(tokenRefreshTimer.value);
      tokenRefreshTimer.value = null;
    }

    // 只在客户端且有token时设置定时器
    if (import.meta.client && token.value && expiresAt.value) {
      // 每分钟检查一次token状态
      tokenRefreshTimer.value = setInterval(() => {
        // 如果token已过期，清除状态
        if (isTokenExpired()) {
          console.warn('⏰ token已过期，清除用户状态');
          logout();
          return;
        }

        // 如果token在1天内过期，尝试续期
        if (isTokenExpiringSoon(1)) {
          console.warn('⏰ token即将过期，尝试续期');
          autoRefreshToken();
        }
      }, 60 * 1000); // 每分钟检查一次
    }
  }

  // 登录方法
  async function userLogin(data: loginDataType) {
    try {
      loading.value = true;
      console.warn('=== userLogin 开始 ===');
      console.warn('登录参数:', data);
      
      const response = await login(data);
      console.warn('userLogin登录响应:', response);
      console.warn('response类型:', typeof response);
      console.warn('response是否为null:', response === null);
      console.warn('response是否为undefined:', response === undefined);
      console.warn('response.data:', response?.data);
      
      if (response && response.data) {
        console.warn('✅ 登录成功，设置用户信息');
        setToken(response.data.token || '');
        setExpiresAt(response.data.expires_at || '');
        setUserInfo(response.data.user || {});
        setLoginStatus(true);
        // 登录成功后设置token续期定时器
        setupTokenRefreshTimer();
      } else {
        console.warn('❌ 登录响应格式异常:', response);
        console.warn('response存在:', !!response);
        console.warn('response.data存在:', !!(response && response.data));
      }
      
      console.warn('=== userLogin 结束 ===');
      return response;
    } catch (error) {
      console.error('❌ 登录失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  }

  // 注册方法
  async function userRegister(data: loginDataType) {
    try {
      loading.value = true;
      const response = await register(data);
      return response;
    } catch (error) {
      ElMessage({
        message: error instanceof Error ? error.message : 'register failed',
        type: 'error',
      })
      throw error;
    } finally {
      loading.value = false;
    }
  }

  // 刷新token方法
  async function refreshUserToken() {
    try {
      loading.value = true;
      const response = await registerToken();
      if (response.data && response.data.token) {
        setToken(response.data.token);
        setExpiresAt(response.data.expires_at || '');
        // 刷新成功后重新设置定时器
        setupTokenRefreshTimer();
      }
      return response;
    } catch (error) {
      console.error('刷新token失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  }

  // 清除缓存数据
  function clearCache() {
    // 清除本地状态
    setToken('');
    setExpiresAt('');
    setUserInfo({});
    setLoginStatus(false);
    
    // 清除定时器
    if (tokenRefreshTimer.value) {
      clearInterval(tokenRefreshTimer.value);
      tokenRefreshTimer.value = null;
    }
    
    // 清除localStorage中的相关数据
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('expires_at');
      localStorage.removeItem('savedEmail');
      localStorage.removeItem('savedPassword');
      localStorage.removeItem('rememberMe');
    }
    
    console.warn('🧹 缓存清理完成');
  }

  // 登出方法
  async function logout() {
    try {
      // 调用登出API
      const result = await logoutApi()
      return result
    } catch (error) {
      console.error('登出API调用失败:', error);
      // 即使API调用失败，也要清除本地状态
    } finally {
      // 使用封装的缓存清理函数
      clearCache();
    }
  }

  // 初始化token状态（从localStorage恢复）
  function initializeTokenState() {
    if (import.meta.client && typeof window !== 'undefined') {
      const savedToken = localStorage.getItem('token');
      const savedExpiresAt = localStorage.getItem('expires_at');
      
      if (savedToken && savedExpiresAt) {
        setToken(savedToken);
        setExpiresAt(savedExpiresAt);
        
        // 检查token是否已过期
        if (isTokenExpired()) {
          console.warn('⏰ 恢复的token已过期，清除状态');
          clearCache();
        } else {
          console.warn('✅ 恢复token状态成功');
          setLoginStatus(true);
          // 设置token续期定时器
          setupTokenRefreshTimer();
        }
      }
    }
  }

  return {
    token,
    expiresAt,
    userInfo,
    isLoggedIn,
    loading,
    isWholesale,
    setToken,
    setExpiresAt,
    setUserInfo,
    setLoginStatus,
    userLogin,
    userRegister,
    refreshUserToken,
    logout,
    clearCache,
    isTokenExpiringSoon,
    isTokenExpired,
    autoRefreshToken,
    setupTokenRefreshTimer,
    initializeTokenState,
    isWholesaleUser
  }
}, 
 {
  persist: import.meta.client && {
    storage: localStorage,
  },
}
)
<template>
  <NuxtLayout>
    <div class="registe">
      <div class="top">
        <table>
          <thead>
            <tr>
              <th>Items</th>
              <th>Quantity</th>
              <th>Price</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="width: 700px;">
                <div class="item">
                  <div class="img"><img src="~/assets/images/product-placeholder.png" alt=""></div>
                  <div class="content">
                    <div class="text">Pad (A16/10th gen) CaseDefender Series </div>
                    <div class="text">Color: Baja Beach (Blue)</div>
                  </div>
                </div>
              </td>
              <td>80</td>
              <td>$22.49</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <th scope="row" colspan="3">
                <div class="subtotal-top">
                  Subtotal：<span>$ 32.04</span>
                </div>
                <div class="subtotal-bottom">
                  Subtotal: <span class="price">$ 32.04</span><span class="wholesaler">Wholesaler？</span> <a href="">Click here</a>
                </div>
              </th>
            </tr>
          </tfoot>
        </table>
      </div>
      <div class="xian"/>
      <div class="detail">
        Your Details
      </div>
      <div class="already">
        Already Registered? 
      </div>
      <div class="form-box">
        <el-form
          ref="ruleFormRef"
          style="max-width: 65.625rem;height: 3.5rem;line-height: 3.5rem;display: flex;flex-wrap: wrap;justify-content: space-between;"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
        >
          <el-form-item prop="firstName" style="width: 29.1875rem;">
            <el-input v-model="ruleForm.firstName" placeholder="First Name"/>
          </el-form-item>
          <el-form-item prop="lastName"  style="width: 29.1875rem;">
            <el-input v-model="ruleForm.lastName" placeholder="Last Name"/>
          </el-form-item>
          <el-form-item prop="email" style="width: 100%;">
            <el-input v-model="ruleForm.email" placeholder="Email"/>
          </el-form-item>
          <el-form-item prop="phoneNumber" style="width: 100%;">
           <el-input v-model="ruleForm.phoneNumber" placeholder="Phone Number"/>
          </el-form-item>
          <el-form-item prop="password" style="width: 100%;">
           <el-input v-model="ruleForm.password" placeholder="Password"/>
          </el-form-item>
          <el-form-item prop="password" style="width: 100%;">
           <el-input v-model="ruleForm.password" placeholder="Password"/>
          </el-form-item>
          <div class="title" style="width: 100%;">
            Shipping Address
          </div>
          <el-form-item prop="city" style="width: 29.1875rem;">
           <el-input v-model="ruleForm.city" placeholder="City"/>
          </el-form-item>
          <el-form-item prop="state" style="width: 29.1875rem;">
           <el-input v-model="ruleForm.state" placeholder="State/Province"/>
          </el-form-item>
          <el-form-item prop="country" style="width: 29.1875rem;">
            <el-select v-model="ruleForm.country" placeholder="Country">
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item prop="code" style="width: 29.1875rem;">
           <el-input v-model="ruleForm.code" placeholder="Postal Code"/>
          </el-form-item>
          <el-form-item prop="address"  style="width: 100%;">
           <el-input v-model="ruleForm.address" placeholder="Address"/>
          </el-form-item>
          <el-form-item prop="opional"  style="width: 100%;">
           <el-input v-model="ruleForm.opional" placeholder="Address2(Opional)"/>
          </el-form-item>
          <div class="title" style="width: 100%;">
            Payment Method
          </div>
          <el-form-item prop="patment" style="width: 100%;">
            <el-radio-group v-model="ruleForm.patment">
              <el-radio value="0">PayPal</el-radio>
              <el-radio value="1">Credit Card (Stripe)</el-radio>
              <el-radio value="2">Alipay</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="have" style="width: 100%;">
            <el-checkbox-group v-model="ruleForm.have">
              <el-checkbox value="0" name="have">
                Online activities
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-button type="primary"  @click="submitForm(ruleFormRef)">
              Create
          </el-button>
        </el-form>
      </div>
    </div>
  </NuxtLayout>
  
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'

definePageMeta({
  layout:"personal-center"
})

interface RuleForm {
  firstName: string,
  lastName: string,
  email: string,
  phoneNumber: string,
  password: string,
  city: string,
  state: string,
  country: string,
  code: string,
  address: string,
  opional: string,
  patment: string,
  have:number[]
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  firstName: '',
  lastName: '',
  email: '',
  phoneNumber: '',
  password: '',
  city: '',
  state: '',
  country: '',
  code: '',
  address: '',
  opional: '',
  patment: '',
  have:[0]
})

const rules = reactive<FormRules<RuleForm>>({
  email: [{required: true,message: 'Please input email address',trigger: 'blur',},
        {
          type: 'email',
          message: 'Please input correct email address',
          trigger: ['blur', 'change'],
        }],
  phoneNumber:[{ required: true, message: '请输入手机号', trigger: 'change' },
    { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'change' }]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.warn('表单提交成功')
    } else {
      console.warn('表单验证失败', fields)
    }
  })
}

</script>

<style lang="scss" scoped>
.registe{
  width: 100%;
  background: #F5F5F5;
  padding-bottom: 11.125rem;
  .top{
    width: 100%;
    background: #F5F5F5;
    padding-top: 5rem;
    box-sizing: border-box;
    table{
      margin: 0 auto;
      box-shadow: -0.6875rem .125rem 1.625rem 0rem #F5F5F5;
      border-radius: .9375rem;
      width: 65.8125rem;
      background: #FFFFFF;
      border-radius: .9375rem;
      overflow: hidden;
      border-spacing:0px;
      thead{
        tr {
          height: 4.375rem;
          width: 65.8125rem;
          background: #EBEBEB;
          border: 0px solid #BABABA;
          th{
            text-align: left;
            padding-left: 2.0625rem;
            box-sizing: border-box;
          }
        }
      }
      tbody{
        tr{
          width: 66.4375rem;
          height: 8.75rem;
          td{
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 500;
            font-size: 1.25rem;
            color: #000000;
            text-align: left;
            padding-left: 2.0625rem;
            font-style: normal;
            text-transform: none;
            border-bottom: 1px solid #EBEBEB;
            .item{
              display: flex;
              align-items: center;
              .img{
                width: 5.5rem;
                height: 5.5rem;
                img{
                  width: 100%;
                  height: 100%;
                }
              }
              .text{
                height: 1.5rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 400;
                font-size: 1.125rem;
                color: #000000;
                line-height: 1.5rem;
                text-align: left;
                font-style: normal;
                padding: .375rem .5625rem;
                text-transform: none;
              }
            }
          }
        }
      }
      tfoot{
        tr{
          width: 66.4375rem;
          height: 8.75rem;
          th{
            .subtotal-top{
              height: 1.5rem;
              font-family: SFProDisplay, SFProDisplay;
              font-weight: 400;
              font-size: 1rem;
              color: #000000;
              line-height: 1.5rem;
              text-align: left;
              padding-left: 2.0625rem;
              font-style: normal;
              text-transform: none;
            }
            .subtotal-bottom{
              margin-top: 1rem;
              height: 1.5rem;
              font-family: SFProDisplay, SFProDisplay;
              font-weight: 600;
              font-size: 1.25rem;
              color: #000000;
              padding-left: 2.0625rem;
              line-height: 1.5rem;
              text-align: left;
              font-style: normal;
              text-transform: none;
              .price{
                height: 1.5rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 600;
                font-size: 1.5rem;
                color: #000000;
                line-height: 1.5rem;
                font-style: normal;
                text-transform: none;
              }
              .wholesaler{
                height: 1.75rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 600;
                font-size: 1.25rem;
                color: #3D3D3D;
                line-height: 1.5rem;
                text-align: left;
                font-style: normal;
              }
              a{
                height: 1.75rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 600;
                font-size: 1.25rem;
                color: #FE8C5F;
                line-height: 1.5rem;
                text-align: left;
                font-style: normal;
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
  .xian{
    margin-top: 3.875rem;
    width: 100%;
    border-bottom:1px solid #ccc;
  }
  .detail{
    margin: 0 auto;
    margin-top: 5rem;
    height: 2.125rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: bold;
    font-size: 1.75rem;
    color: #000000;
    line-height: 2.125rem;
    text-align: center;
    font-style: normal;
  }
  .already{
    margin: 0 auto;
    margin-top: 1rem;
    height: 1.3125rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 400;
    font-size: 1.125rem;
    color: #000000;
    line-height: 1.3125rem;
    text-align: center;
    font-style: normal;
  }
  .form-box{
    margin:0 auto;
    margin-top: 2.6875rem;
    width: 65.625rem;
    height: 74rem;
    background: #FFFFFF;
    border-radius: .9375rem;
    padding: 4rem 2.375rem;
    box-sizing: border-box;
    .el-form{
      ::v-deep .el-form-item{
        .el-input{
          height: 3.5rem !important;
          line-height: 3.5rem !important;
        }
        .el-select{
          height: 3.5rem !important;
          line-height: 3.5rem !important;
          .el-select__wrapper{
            height: 100%;
          }
        }
      }
      .title{
        margin: 2.375rem 0 1.125rem 0;
        height: 1.5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 500;
        font-size: 1.125rem;
        color: #000000;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .el-button{
        margin: 4.3125rem auto;
        width: 21.1875rem;
        height: 3.5rem;
        background: #3D3D3D;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        
        &:hover {
          background: #555555;
          box-shadow: 0px .375rem .75rem 0px rgba(0,0,0,0.15);
          transform: translateY(-1px);
        }
        
        &:active {
          background: #1a1a1a;
          box-shadow: 0px .125rem .25rem 0px rgba(0,0,0,0.2);
          transform: translateY(1px);
        }
        
        &:disabled {
          background: #666666;
          cursor: not-allowed;
          opacity: 0.6;
          
          &:hover {
            background: #666666;
            box-shadow: none;
            transform: none;
          }
          
          &:active {
            background: #666666;
            box-shadow: none;
            transform: none;
          }
        }
      }
      
    }

  }
}
</style>
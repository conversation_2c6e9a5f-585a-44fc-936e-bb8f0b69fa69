# 语种变化后接口重新调用功能指南

## 功能概述

本功能实现了在语种切换后自动重新调用相关接口，确保页面数据与当前语种保持一致。

## 核心组件

### 1. useLanguageDataRefresh Composable
位置：`composables/useLanguageDataRefresh.ts`

功能：
- 提供响应式的导航栏、语种列表、页眉优惠信息、底部数据
- 监听语种变化并自动重新获取数据
- 提供全局数据刷新功能

使用示例：
```typescript
const { navData, languageList, headerOfferData, footerData, fetchData } = useLanguageDataRefresh()
```

### 2. useLanguageEventBus Composable
位置：`composables/useLanguageEventBus.ts`

功能：
- 提供全局语种变化事件总线
- 允许任何组件监听语种变化事件
- 支持多个监听器同时工作

使用示例：
```typescript
const { onLanguageChange, removeLanguageChangeListener } = useLanguageEventBus()

// 监听语种变化
onLanguageChange((newLanguage) => {
  // 处理语种变化逻辑
  console.log('语种已切换为:', newLanguage)
})
```

### 3. 全局插件
位置：`plugins/language-refresh.client.ts`

功能：
- 在应用启动时自动启动语种变化监听
- 确保全局数据在语种变化时自动刷新

## 实现原理

1. **语种变化检测**：通过监听 `useLanguageStore` 中的 `language` 状态变化
2. **事件总线**：使用事件总线模式，允许多个组件同时响应语种变化
3. **数据刷新**：在语种变化时自动重新调用相关API接口
4. **缓存优化**：在请求工具中添加语言相关的缓存键，确保语种变化时能重新请求

## 支持的接口

以下接口会在语种变化时自动重新调用：
- `/api/mlk/common/navbar` - 导航栏数据
- `/api/mlk/common/locales` - 语种列表
- `/api/mlk/common/header-offer` - 页眉优惠信息
- `/api/mlk/common/footer` - 底部页面内容

## 在组件中使用

### 基本使用
```vue
<script setup>
import { useLanguageDataRefresh } from '~/composables/useLanguageDataRefresh'

// 获取响应式数据
const { navData, languageList, headerOfferData, footerData } = useLanguageDataRefresh()
</script>

<template>
  <div>
    <!-- 导航栏数据会自动更新 -->
    <div v-for="item in navData" :key="item.id">
      {{ item.name }}
    </div>
  </div>
</template>
```

### 自定义语种变化处理
```vue
<script setup>
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

const { onLanguageChange } = useLanguageEventBus()

// 监听语种变化
onLanguageChange((newLanguage) => {
  // 自定义处理逻辑
  console.log('语种切换为:', newLanguage)
  // 可以在这里调用其他接口或更新组件状态
})
</script>
```

## 注意事项

1. **客户端执行**：所有语种变化监听都在客户端执行，避免SSR问题
2. **错误处理**：所有接口调用都包含错误处理，确保应用稳定性
3. **性能优化**：使用响应式数据和缓存机制，避免不必要的重复请求
4. **类型安全**：所有数据都有完整的TypeScript类型定义

## 扩展功能

如果需要添加新的接口到语种变化刷新列表中，可以：

1. 在 `useLanguageDataRefresh.ts` 中添加新的接口调用
2. 在 `fetchData` 函数中添加相应的数据获取逻辑
3. 添加相应的响应式数据变量

## 调试

在浏览器控制台中可以看到以下日志：
- `🌐 语种切换后数据已重新获取: [语种代码]`
- `🌐 语种切换后重新获取数据失败: [错误信息]`
- `🌐 全局语种变化监听器已启动`

这些日志可以帮助调试语种变化和数据刷新功能。 
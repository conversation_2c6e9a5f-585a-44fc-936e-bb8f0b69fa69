<template>
  <div class="invoiceDetail-page">
    <div class="title">
      Invoice Detail
    </div>
     <div class="top">
      <div class="top-left">
        <div class="icon"><el-icon color="#fff"><ArrowLeft /></el-icon></div><span>Back to My Order</span>
      </div>
      <div class="top-right">
        <div class="btn">
          <img src="" alt="">
          <span>Print</span>
        </div>
        <div class="btn">
          <img src="" alt="">
          <span>Download PDF</span>
        </div>
        <el-button type="primary">Pay Now</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-left">
        <MLKLogo style="width: 5.5rem;height: 3.6625rem; margin-top: 2.25rem;margin-left: 2.5625rem;"/>
      </div>
      <div class="content-right">
        <div class="content-right-text">
          Invoice Number
        </div>
        <div class="content-right-title">
          INV-10843
        </div>
        <div class="content-right-text">
          Order Number
        </div>
        <div class="content-right-title">
          Oct 28, 2023
        </div>
        <div class="content-right-text">
          Invoice Date
        </div>
        <div class="content-right-title">
          Oct 28, 2023
        </div>
        <div class="content-right-text">
          Due Date
        </div>
        <div class="content-right-title">
          Nov 28, 2023
        </div>
        <div class="paid-btn">
          PAID
        </div>
      </div>
    </div>
    <div class="table">
      <el-table :data="tableData" style="width: 100%;border: 1px solid #EBEBEB;">
        <el-table-column prop="shippingAddress" label="Shipping Address"/>
        <el-table-column prop="billingAddress" label="Billing Address" />
      </el-table>
    </div>
    <div class="bottom-table">
      <el-table :data="tableData1" :span-method="objectSpanMethod" style="width: 100%;border: 1px solid #EBEBEB;">
        <el-table-column prop="Product" label="Product" width="440">
          <template #default="scope">
            <div v-if="scope.$index === 2" class="table-content">
              <div class="text-h">
                Payment Information
              </div>
              <div class="text"><span class="text-left">Payment Method: </span><span class="text-right">Credit Card</span></div>
              <div class="text"><span class="text-left">Card Type:  </span><span class="text-right">Visa</span></div>
              <div class="text"><span class="text-left">Card Number : </span><span class="text-right">**** **** **** 1234</span></div>
              <div class="text-h">
                Payment Date: Oct 29, 2023
              </div>
            </div>
            <div v-else>{{ scope.row.id }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="SKU" label="SKU" width="210"/>
        <el-table-column prop="Price" label="Price" />
        <el-table-column prop="Quantity" label="Quantity" />
        <el-table-column prop="Total" label="Total" />
      </el-table>
    </div>
    <div class="thank">
      Thank you for your business!
    </div>
    <div class="ts">
      If you have any questions about this invoice, please contact our customer support team. <EMAIL> |(800)123-4567
    </div>
  </div>
</template>

<script setup lang="ts">

import type { TableColumnCtx } from 'element-plus'

interface tableType {
  shippingAddress?: string
  billingAddress?: string
  Product?: string
  SKU?: string
  Price?: string
  Quantity?: string
  Total?:string
}

interface SpanMethodProps {
  row: tableType
  column: TableColumnCtx<tableType>
  rowIndex: number
  columnIndex: number
}
const tableData =[{
    shippingAddress: 'John Doe',
    billingAddress:'John Doe'
  },
  {
    shippingAddress: '123 Main Street',
    billingAddress:'456 Park Avenue'
  },
  {
    shippingAddress: 'Apt 4B',
    billingAddress:'Suite 789'
  },
  {
    shippingAddress: '10001 | New York, NY 10001',
    billingAddress:'New York, NY 10022'
  },
  {
    shippingAddress: 'United States',
    billingAddress:'United States'
  },
  {
    shippingAddress: 'United States',
    billingAddress:'United States'
  },
  {
    shippingAddress: 'Phone: (*************',
    billingAddress:'Phone: (*************'
  }]
const tableData1 = [
  {
    Product: 'Premium Wireless Noise Cancelling Headphones',
    SKU: 'HDPH-001',
    Price: '$199.99',
    Quantity: '1',
    Total:"$199.99"
  },
  {
    Product: 'Premium Wireless Noise Cancelling Headphones',
    SKU: 'HDPH-001',
    Price: '$199.99',
    Quantity: '1',
    Total:"$199.99"
  },
  {
    Product: '',
    SKU: 'Subtotal:',
    Price: '',
    Quantity: '',
    Total:"$199.99"
  },
  {
    Product: '',
    SKU: 'Shipping & Handling:',
    Price: '',
    Quantity: '',
    Total:"$199.99"
  },
  {
    Product: '',
    SKU: 'Tax (8.25%):',
    Price: '',
    Quantity: '',
    Total:"$199.99"
  },
  {
    Product: '',
    SKU: 'Discount:',
    Price: '',
    Quantity: '',
    Total:"$199.99"
  },
  {
    Product: '',
    SKU: 'Grand Total:',
    Price: '',
    Quantity: '',
    Total:"$199.99"
  },
]

const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (columnIndex === 0) {
    if (rowIndex === 2 ) {
      return {
        rowspan: 5,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}


</script>

<style scoped lang="scss">
::v-deep .el-table__row:nth-child(n+4) .el-table__cell:nth-child(1) {
  background:#fff !important;
  border-right: 0px !important;
}
.invoiceDetail-page{
  width: 69.75rem;
  background: #FFFFFF;
  border-radius: .5rem;
  padding: 2rem 1.625rem 3.125rem 1.625rem;
  box-sizing: border-box;
  .title{
    height: 1.8125rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: bold;
    font-size: 1.5rem;
    color: #3D3D3D;
    line-height: 1.8125rem;
    text-align: left;
    font-style: normal;
  }
  .top{
    width: 100%;
    margin-top: 1.9375rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top-left{
      box-sizing: border-box;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: 400;
      font-size: 1.125rem;
      color: #3D3D3D;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
      .icon{
        cursor: pointer;
        margin-right: .5625rem;
        width: 1.25rem;
        height: 1.25rem;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;

      }
      span{
        cursor: pointer;
      }
    }
    .top-right{
      margin-right: 1.875rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;
      .btn{
        cursor: pointer;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: .5rem;
        border: 1px solid #979797;
        padding:0 1rem;
        box-sizing: border-box;
        &:hover{
          background: #979797;
        }
        img{
          width: 1.5rem;
          height: 1.5rem;
        }
        span{
          margin-left: .5rem;
          display: inline-block;
          height: 1.1875rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #565656;
          line-height: 1.1875rem;
          text-align: left;
          font-style: normal;
        }
      }
      .el-button{
        width: 6.8125rem;
        height: 3rem;
        border-radius: .5rem;
      }
    }
    
  }
  .content{
    margin-top: 1.5625rem;
    width: 100%;
    height: 22.3125rem;
    background: #FFFFFF;
    border-radius: .5rem .5rem 0px 0px;
    border: .0625rem solid #EBEBEB;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .content-left{
      height: 100%;
    }
    .content-right{
      height: 100%;
      padding: .6875rem 1.875rem 0 0;
      box-sizing: border-box;
      .content-right-text{
        height: 1.125rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 400;
        font-size: .9375rem;
        color: #3D3D3D;
        line-height: 1.125rem;
        text-align: left;
        font-style: normal;
        margin-top: 1rem;
      }
      .content-right-title{
        margin-top: .375rem;
        height: 1.3125rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.125rem;
        color: #3D3D3D;
        line-height: 1.3125rem;
        text-align: left;
        font-style: normal;
      }
      .paid-btn{
        margin-top: 2.0625rem;
        width: 6.5rem;
        height: 2rem;
        line-height: 2rem;
        text-align: center;
        background: rgba(38,148,255,0.24);
        border-radius: .5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 400;
        font-size: 1rem;
        color: #2694FF;
        font-style: normal;
      }
    }
    
  }
  .table{
    width: 100%;
    ::v-deep .el-table th.el-table__cell{
            background-color: #EBEBEB;
            height: 3.125rem;
        }
        ::v-deep .el-table .cell {
            text-align: left;
            padding-left: 2.1875rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1.25rem;
            color: #3D3D3D;
            font-style: normal;
        }
        ::v-deep .el-table__row .cell{
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1rem;
          color: #565656;
          text-align: left;
          padding-left: 2.1875rem;
          font-style: normal;
        }
        ::v-deep .el-table__cell {
            background-color: #fff;
            height: 3.125rem;
        }
        ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
            background-color: #FFFFFF;
        }
        .table-content{
          width: 100%;
          height: 100%;
          background: #F7F7F7;
        }
  }
  .bottom-table{
    width: 100%;
    margin-top: 2.0625rem;
    ::v-deep .el-table th.el-table__cell{
            background-color: #EBEBEB;
            height: 3.125rem;
    }
    ::v-deep .el-table .cell {
        text-align: left;
        padding-left: 2.1875rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1.25rem;
        color: #3D3D3D;
        font-style: normal;
    }
    ::v-deep .el-table__row{
      .cell{
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1rem;
        color: #3D3D3D;
        text-align: left;
        padding-left: 2.1875rem;
        font-style: normal;
      }
      &:nth-child(n+3){
        .el-table__cell:nth-child(1){
          border-right: 1px solid #EBEBEB;
          background: #F7F7F7;
        }
        .cell{
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1rem;
          color: #3D3D3D;
          text-align: left;
          padding-left: 2.1875rem;
          font-style: normal;
        }
      }
    } 
    ::v-deep .el-table__cell {
        background-color: #fff;
        height: 3.125rem;
    }
    ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
        background-color: #FFFFFF;
    }
    .table-content{
      padding: none;
      .text-h{
        margin-bottom: .9375rem;
        height: 1.5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1.25rem;
        color: #3D3D3D;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
      }
      .text{
        margin-bottom: .9375rem;
        .text-left{
          height: 1.1875rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1rem;
          color: #959595;
          line-height: 1.1875rem;
          text-align: left;
          font-style: normal;
        }
        .text-right{
          height: 1.1875rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1rem;
          color: #3D3D3D;
          line-height: 1.1875rem;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
  .thank{
    margin-top: 2.8125rem;
    height: 1.8125rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 600;
    font-size: 1.5rem;
    color: #3D3D3D;
    line-height: 1.8125rem;
    text-align: center;
    font-style: normal;
  }
  .ts{
    margin-top: 1rem;
    height: 1.1875rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 400;
    font-size: 1rem;
    color: #979797;
    line-height: 1.1875rem;
    text-align: center;
    font-style: normal;
  }
}
</style>
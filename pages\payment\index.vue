<template>
    <NuxtLayout>
        <div class="payment-container">
            <div class="payment-box">
                <div class="box-left">
                    <div class="box-left-top">
                        <div class="title">
                            <el-icon :size="20" color="#3D3D3D"><Location /></el-icon>
                            <span>Shipping Address</span>
                        </div>
                        <div class="address-box">
                            <!-- <div class="address-box-left">
                                <span><PERSON></span>
                            </div> -->
                        </div>
                        <div class="check-box">
                            <el-checkbox v-model="checkAll">
                                <span>Use company address as shipping address?</span>
                            </el-checkbox>
                        </div>
                    </div>
                    <div class="box-left-bottom">
                        <div class="title">
                            <el-icon :size="20" color="#3D3D3D"><Suitcase /></el-icon>
                            <span>Payment Method</span>
                        </div>
                        <div class="payment-method">
                            <div class="Debit-Card">
                                <el-icon :size="20" color="#3D3D3D" style="margin-left: 1.25rem;"><CreditCard /></el-icon>
                                <div style="margin-left: .9375rem;">
                                    <div class="Card-name">Credit/Debit Card</div>
                                    <div class="content">Visa, Mastercard, American Express</div>
                                </div>
                            </div>
                            <div class="Debit-Card">
                                <el-icon :size="20" color="#3D3D3D" style="margin-left: 1.25rem;"><WalletFilled /></el-icon>
                                <div style="margin-left: .9375rem;">
                                    <div class="Card-name">PayPal</div>
                                    <div class="content">Pay with your PayPal account</div>
                                </div>
                            </div>
                            <div class="Card-number">
                                <span>Card Number</span>
                                <div style="margin-top: .625rem;">
                                    <el-input v-model="CardNumber" type="number" style="height: 2.8125rem;border-radius: 1.25rem;"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-right">
                    <div class="title">
                        <span>Order Summary</span>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
definePageMeta({
    layout: "personal-center"
})

const checkAll = ref(false)

const CardNumber = ref('')
</script>

<style scoped lang="scss">
::v-deep .el-checkbox__inner:hover{
    border-color: #262626;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
    background-color: #262626;
}
::v-deep .el-checkbox__inner:after{
    border:.125rem solid transparent;
    border-left: 0;
    border-top: 0;
}
.payment-container {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;

    .payment-box {
        width: 93.75rem;
        height: 100%;
        margin: 0 auto;
        // border: 1px solid #E5E5E5;
        // background-color: #fff;
        display: flex;
        justify-content: space-around;

        .box-left {
            width: 60.625rem;
            height: 100%;
            margin-top: 1.875rem;
            // background-color: #fff;
            .box-left-top {
                width: 100%;
                height: 21.25rem;
                background-color: #fff;
                border-radius: 0.625rem;
                .title {
                    height: 25px;
                    display: flex;
                    align-items: center;
                    margin-left: 1.875rem;
                    padding-top: 1.875rem;
                    span{
                        font-family: SFProDisplay, SFProDisplay;
                        font-weight: 600;
                        font-size: 1.125rem;
                        color: #3D3D3D;
                        text-align: left;
                        font-style: normal;
                        margin-left: .3125rem;
                    }
                }
                .address-box{
                    width: 95%;
                    height: 12.5rem;
                    border: 2px solid #E5E5E5;
                    border-radius: 0.625rem;
                    margin: .9375rem auto;
                }
                .check-box{
                    width: 95%;
                    height: 2.5rem;
                    margin: .625rem auto;
                    span{
                        font-family: SFProDisplay, SFProDisplay;
                        font-weight: 500;
                        font-size: 14px;
                        color: #3D3D3D;
                    }
                }
            }
            .box-left-bottom{
                margin-top: 1.875rem;
                width: 100%;
                height: 31.25rem;
                background-color: #fff;
                border-radius: 0.625rem;
                .title {
                    height: 1.5625rem;
                    display: flex;
                    align-items: center;
                    margin-left: 1.875rem;
                    padding-top: 1.875rem;
                    span{
                        font-family: SFProDisplay, SFProDisplay;
                        font-weight: 600;
                        font-size: 1.0625rem;
                        color: #3D3D3D;
                        text-align: left;
                        font-style: normal;
                        margin-left: .3125rem;
                    }
                }
                .payment-method{
                    margin-top: 1.875rem;
                    .Debit-Card{
                        width: 95%;
                        height: 6.25rem;
                        border: 2px solid #E5E5E5;
                        border-radius: 0.625rem;
                        margin: .9375rem auto;
                        background-color: #fff;
                        display: flex;
                        align-items: center;
                        .Card-name{
                            font-family: SFProDisplay, SFProDisplay;
                            font-weight: 600;
                            font-size: .875rem;
                            color: #3D3D3D;
                            text-align: left;
                            font-style: normal;
                        }
                        .content{
                            font-family: Helvetica;
                            // width: 19.75rem;
                            // height: .0187rem;
                            font-size: .875rem;
                            color: #999999;
                            line-height: 1.25rem;
                            // text-shadow: 0px 4px 8px rgba(0,0,0,0.1);
                            // text-align: left;
                            // font-style: normal;
                            text-transform: none;
                            white-space: break-spaces;
                        }
                    }

                }
            }
            .Card-number{
                width: 95%;
                margin: 1.875rem auto 0 auto;
            }
        }

        .box-right {
            margin-top: 1.875rem;
            width: 30.625rem;
            height: 800px;
            background-color: #fff;
            border-radius: 0.625rem;
            .title {
                height: 1.5625rem;
                display: flex;
                align-items: center;
                margin-left: 1.875rem;
                padding-top: 1.25rem;
                span{
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 600;
                    font-size: 1.0625rem;
                    color: #3D3D3D;
                    text-align: left;
                    font-style: normal;
                    margin-left: .3125rem;
                }
            }
        }
    }
}
</style>
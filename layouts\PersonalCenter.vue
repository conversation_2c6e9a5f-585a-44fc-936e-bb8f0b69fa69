<template>
  <NuxtApp>
    <div class="layout">
      <div class="top">
        <div class="left">
          <MLKLogo style="width: 5.5rem;"/>
          <div class="user">
            <img src="~/assets/images/user.png" :alt="$t('personalCenter.user')">
          </div>
          <div class="checkout">
            {{ $t('personalCenter.secureCheckout') }}
          </div>
        </div>
        <div class="right">
          <div class="search-container">
          <i class="fas fa-search search-icon" />
          <input
            type="text"
            :placeholder="$t('header.search')"
            class="search-input"
            @keyup.enter="SearchSubmit"
            >
          </div>
          <div v-if="userStore.isLoggedIn" class="continue-shopping" @click="goShopping">
            {{ $t('personalCenter.continueShopping') }} —>
          </div>
          <div v-if="!userStore.isLoggedIn" class="username"  @click="navigateTo('/login')">
            {{ $t('header.wholesale') }} >
          </div>
        </div>
      </div>
    </div>
    <slot/>
  </NuxtApp>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user'
import { useLanguageStore } from '~/stores/language'

const userStore = useUserStore()
const languageStore = useLanguageStore()
//去购物
const goShopping = () => {
  const router = useRouter()
  router.push('/home')
}

const SearchSubmit = () => {
  const router = useRouter()
  router.push('/search')
}

// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  if (import.meta.client && newLanguage) {
    console.warn('🌐 PersonalCenter Layout 检测到语言变化:', newLanguage)
  }
})

</script>

<style lang="scss" scoped>
.layout{
  width: 100%;
  height: 5.5rem;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  .top{
    width: 85rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    .left{
      display: flex;
      align-items: center;
      .user{
        width: 2rem;
        height: 2rem;
        margin-left: 1.125rem;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .checkout{
        margin-left: 1rem;
        height: 1.3125rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1.125rem;
        color: #000000;
        line-height: 1.3125rem;
        text-align: left;
        font-style: normal;
      }
    }
    .right{
      display: flex;
      align-items: center;
      .search-container {
          width: 15.3125rem;
          position: relative;
          display: flex;
          align-items: center;
          border-radius: 2.02rem;
          background: #f5f5f5;
          padding: 1.02rem 1.33rem;

          .search-input {
            outline: none;
            width: 100%;
            padding-left: .625rem;
            font-size: 1.17rem;
            color: #656565;
            border: none;
            background: #f5f5f5;
          }

          .fas {
            transition: transform 0.3s ease;
          }

          .search-icon {
            position: static;
            color: #656565;
            font-size: 1.17rem;
          }
        }
      .continue-shopping{
        margin-left: 4.375rem;
        // width: 15.625rem;
        height: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 1.125rem;
        color: #000000;
        text-align: left;
        font-style: normal;
        cursor: pointer;
      }
      .username {
        margin-left: 4.9375rem;
        height: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 1.125rem;
        color: #000000;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        cursor: pointer;
      }
    }
  }
  

}
</style>
<template>
  <div class="promotion">
    <MLKTechnical/>
    <div class="proudct-list">
      <!-- 正常数据显示 -->
      <MLKProductCard
        v-for="(item) in specialInfo"
        :key="item.id"
        :product-data="item"
        class="proudct-item"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getSpecialPageData } from "~/api/channel"
const { onLanguageChange } = useLanguageEventBus()
// import { ElMessage } from 'element-plus'

// 定义产品类型
interface ProductType {
  id: number;
  type?: string;
  url_key: string;
  sku?: string;
  name: string;
  new?: number;
  featured?: number;
  special_price?:string
  price?: number;
  formatted_price: string;
  discount_percent?: number;
  base_image: string;
  short_description: string;
  color?: { option_label: string; swatch_value?: string }[];
}

interface RootObject {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  products?: ProductType[];
}

// 响应式数据
const specialInfo = ref<ProductType[]>([])

// 获取特价商品数据的函数
const getSpecialPage = async (): Promise<RootObject> => {
  if(import.meta.client){
    await nextTick()
  }
  try {
    const res = await getSpecialPageData()
    // 为没有颜色的商品添加默认颜色
    if (res.data.products && Array.isArray(res.data.products)) {
      res.data.products.forEach(item => {
        if (!item.color || !Array.isArray(item.color)) {
          item.color = [
            { option_label: 'red', swatch_value: '' },
            { option_label: 'blue', swatch_value: '' },
            { option_label: 'black', swatch_value: '' }
          ]
        }
      })
    }
    
    return res.data as RootObject
  } catch  {
    // 返回空数据结构
    return {
      total: 0,
      per_page: 0,
      current_page: 0,
      last_page: 0,
      products: []
    }
  }
}


// 语种监听相关


// 监听语种变化
onLanguageChange(async () => {
  await loadSpecialProducts()
})
// 加载特价商品
const loadSpecialProducts = async () => {
  try {
    const res = await getSpecialPage()
    specialInfo.value = res.products || []
  } catch {
    // 错误已在getSpecialPage中处理
    specialInfo.value = []
  }
}

// 页面挂载时加载数据
onMounted(() => {
  loadSpecialProducts()
})
</script>

<style lang="scss" scoped>
.promotion {
  width: 100%;
  
  .proudct-list {
    width: 85rem;
    margin: 3.375rem auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    gap: 2.5rem;
    
    .proudct-item {
      margin-bottom: 2.5rem;
    }
    
    // 错误状态样式
    .error-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      
      .error-text {
        font-size: 1.1rem;
        color: #e74c3c;
        margin-bottom: 1rem;
      }
      
      .retry-btn {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
        
        &:hover {
          background-color: #2980b9;
        }
      }
    }
    
    // 空数据状态样式
    .empty-container {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      
      .empty-text {
        font-size: 1.1rem;
        color: #95a5a6;
      }
    }
  }
}
</style>
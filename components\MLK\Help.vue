<template>
  <div class="help-page">
    <div class="top">
      <div class="close-btn" @click="handleClose">
        <img src="/assets/images/close.png" alt="close" >
      </div>
      {{ translations.title}}
    </div>
    <div class="content">
      <div class="text">
        {{ translations.about || "Sorry, we aren't online at the moment. Leave a message and we'll get back to you." }}
      </div>
      <div class="input-name">
        {{ translations.fields?.name?.label}}
      </div>
      <div class="input">
        <el-input 
          v-model="form.name" 
          size="large" 
          style="width: 27.125rem"
          :placeholder="translations.fields?.name?.placeholder"
        />
      </div>
      <div class="input-name">
        {{ translations.fields?.email?.label}}
      </div>
      <div class="input">
        <el-input 
          v-model="form.email" 
          size="large" 
          style="width: 27.125rem"
          :placeholder="translations.fields?.email?.placeholder"
        />
      </div>
      <div class="input-name">
        {{ translations.fields?.contact?.label}}
      </div>
      <div class="input">
        <el-input 
          v-model="form.contact" 
          size="large" 
          style="width: 27.125rem"
          :placeholder="translations.fields?.contact?.placeholder"
        />
      </div>
      <div class="input-name">
        {{ translations.fields?.message?.label}}
      </div>
      <div class="textarea-input"> 
        <el-input
          v-model="form.message"
          style="width: 27.125rem"
          :rows="6"
          type="textarea"
          :placeholder="translations.fields?.message?.placeholder"
        />
      </div>
      <div class="btn-box">
        <div class="btn" :class="{ 'loading': loading }" @click="handleSubmit">
          {{ translations.submit_button}}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getHelpTranslations, sendHelpInfo } from '~/api/user'

// 定义翻译类型
interface Translations {
  title?: string
  about?: string
  fields?: {
    name: {
      label?: string
      placeholder?: string
      required?: boolean
    }
    email: {
      label?: string
      placeholder?: string
      required?: boolean
    }
    contact: {
      label?: string
      placeholder?: string
      required?: boolean
    }
    message: {
      label?: string
      placeholder?: string
      required?: boolean
    }
  }
  submit_button?: string
  success_message?: string
}

const form = ref({
  name: '',
  email: '',
  contact: '',
  message: ''
})

const loading = ref(false)
const translations = ref<Translations>({})

// 获取翻译
const fetchTranslations = async () => {
  try {
    const response = await getHelpTranslations()
    if (response.success) {
      translations.value = response.data
    }
  } catch (error) {
    console.error('Failed to fetch translations:', error)
  }
}

// 表单验证
const validateForm = () => {
  // 验证姓名
  if (translations.value.fields?.name?.required && !form.value.name.trim()) {
    ElMessage.error('Name is required')
    return false
  }
  
  // 验证邮箱
  if (translations.value.fields?.email?.required) {
    if (!form.value.email.trim()) {
      ElMessage.error('Email is required')
      return false
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
      ElMessage.error('Please enter a valid email')
      return false
    }
  }
  
  // 验证电话
  if (translations.value.fields?.contact?.required && !form.value.contact.trim()) {
    ElMessage.error('Phone number is required')
    return false
  }
  
  // 验证消息
  if (translations.value.fields?.message?.required && !form.value.message.trim()) {
    ElMessage.error('Message is required')
    return false
  }
  
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) return
  
  loading.value = true
  try {
    const formData = {
      name: form.value.name,
      email: form.value.email,
      contact: form.value.contact,
      message: form.value.message
    }
    
    const response = await sendHelpInfo(formData)
    
    if (response.success) {
      ElMessage.success(response.message || 'Message sent successfully!')
      // 重置表单
      form.value = {
        name: '',
        email: '',
        contact: '',
        message: ''
      }
    }
  } catch (error) {
    console.error('Failed to send message:', error)
    ElMessage.error(error || 'Failed to send message. Please try again.')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  // 触发关闭事件，让父组件处理
  emit('close')
}

// 定义emit
const emit = defineEmits<{
  close: []
}>()

// 组件挂载时获取翻译
onMounted(() => {
  fetchTranslations()
})
</script>

<style lang="scss" scoped>
.help-page{
  width: 31.5rem;
  // height: 44.5625rem;
  border-radius: 1.25rem;
  overflow: hidden;
  border: 1px solid #565656;
  position: relative;
  background-color: #FFFFFF;
  .top{
    width: 31.5rem;
    height: 5.1875rem;
    line-height: 5.1875rem;
    background: #565656;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 800;
    font-size: 1.125rem;
    color: rgba(255,255,255,0.88);
    text-align: center;
    font-style: normal;
    position: relative;
    
    .close-btn {
      position: absolute;
      right: 1.25rem;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 0.7;
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .content{
    padding: .875rem 2.1875rem 0 2.1875rem;
    .text{
      width: 27.125rem;
      height: 3rem;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: 400;
      font-size: 1rem;
      color: rgba(0,0,0,0.88);
      line-height: 1.5rem;
      text-align: justify;
      font-style: normal;
    }
    .input-name{
      margin: 1.5rem 0 0 .625rem;
      box-sizing: border-box;
      height: 1.5rem;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: 400;
      font-size: 1.125rem;
      color: #979797;
      line-height: 1.5rem;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .input{
      margin-top: .3125rem;
      width: 27.125rem;
      height: 3.5rem;
      background: #F7F7F7;
      border-radius: .4375rem;
      border: 1px solid #9F9F9F;

      .el-input{
        width: 100%;
        height: 100%;
      }
    }
    .textarea-input{
      margin-top: .3125rem;
      width: 27.125rem;
      background: #F7F7F7;
      border-radius: .4375rem;
      border: 1px solid #9F9F9F;
      .el-input{
        width: 100%;
        height: 8.4375rem !important;
      }
    }
    .btn-box{
        width: 100%;
        padding:1.875rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
      .btn{
        cursor: pointer;
        padding: .625rem 1.25rem;
        background: #3D3D3D;
        border-radius: .5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 1.125rem;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        transition: all 0.3s ease;
        
        &:hover {
          background: #555555;
        }
        
        &.loading {
          background: #999999;
          cursor: not-allowed;
        }
      }
    }
    
  }
}
</style>
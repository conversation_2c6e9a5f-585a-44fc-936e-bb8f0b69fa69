<template>
  <div class="payment-page">
    <div class="top">
      <div class="icon"><el-icon color="#fff"><ArrowLeft /></el-icon></div><span>Back to My Order</span>
    </div>
    <div class="box">
      <div class="title">
        Complete Your Payment 
      </div>
      <div class="box-top">
        <div class="box-top-item bgeb fs18">
          <div class="text fontweight600">
            Order #2023110234567
          </div>
        </div>
        <div class="box-top-item">
          <div class="text fontweight500">
            Subtotal
          </div>
          <div class="text fontweight500">
            $599.00
          </div>
        </div>
        <div class="box-top-item">
          <div class="text fontweight500">
            Shipping
          </div>
          <div class="text fontweight500">
            $12.95
          </div>
        </div>
        <div class="box-top-item">
          <div class="text fontweight500">
            Tax
          </div>
          <div class="text fontweight500">
            $48.96
          </div>
        </div>
        <div class="box-top-item">
          <div class="text fs20 fontweight500">
            Total
          </div>
          <div class="text fs20 fontweight500">
            $660.91
          </div>
        </div>
      </div>
      <div class="payment-text">
        Payment Method
      </div>
      <div class="center">
        <div class="center-left">
          <div class="check-icon">
            <el-icon color="#fff" size="20"><Check /></el-icon>
          </div>
          <div class="center-left-top">
            <img src="~/assets/images/qianbao.png" alt="">
            <div class="center-left-top-title">
              Credit / Debit Card
            </div>
          </div>
          <div class="pay-text">
            Pay securely with your credit or debit card.
          </div>
          <div class="input-title">
            Card Number
          </div>
          <input type="text" placeholder="1234 5678 9012 3456">
          <div class="input-title">
            CW
          </div>
          <input type="text" placeholder="MM/YY">
          <div class="input-title">
            Expiry Date
          </div>
          <input type="text" placeholder="MM/YY">
          <div class="input-title">
            Cardholder Name
          </div>
          <input type="text" placeholder="John Doe">
        </div>
        <div class="center-right">
          <img src="~/assets/images/PayPal.png" alt="">
          <div class="center-right-content">
            Pay using your PayPal account.You will be redirected to PayPal to complete your payment.
          </div>
        </div>
      </div>
      <div class="box-xtbs">
        <div class="xtb">
          VISA
        </div>
        <div class="xtb">
          MC
        </div>
        <div class="xtb">
          AMEX
        </div>
        <div class="xtb">
          JCB
        </div>
      </div>
      <div class="btn-list">
        <el-button class="btn pay">Pay Now 278.94</el-button>
        <el-button class="btn cancel">Cancel</el-button>
      </div>
      <div class="box-bottom">
        <el-icon size="20" color="#959595"><Lock /></el-icon>
        <div class="box-bottom-text">
          Your payment information is secure.We use 256-bit SSL encryption to protect your data.
        </div> 
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.payment-page{
  width: 100%;
  background: #F5F5F5;
  padding: 2.125rem 0;
  .top{
    padding-left: 2.9375rem;
    box-sizing: border-box;
    margin: 0 auto;
    width: 69.5rem;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 400;
    font-size: 1.125rem;
    color: #3D3D3D;
    text-align: left;
    font-style: normal;
    display: flex;
    align-items: center;
    .icon{
      cursor: pointer;
      margin-right: .5625rem;
      width: 1.25rem;
      height: 1.25rem;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;

    }
    span{
      cursor: pointer;
    }
  }
  .box{
    margin: 2.125rem auto;
    width: 69.5rem;
    height: 85.5rem;
    padding: 1.5rem;
    box-sizing: border-box;
    background: #FFFFFF;
    .title{
      font-family: SFProDisplay, SFProDisplay;
      font-weight: bold;
      font-size: 1.5rem;
      color: #3D3D3D;
      text-align: left;
      font-style: normal;
      padding-left: 1.4375rem;
      box-sizing: border-box;

    }
     .box-top{
      width: 100%;
      margin-top: 1.25rem;
      .box-top-item{
        width: 100%;
        height: 3.125rem;
        background: #F7F7F7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 1.5rem;
        box-sizing: border-box;
        margin-bottom: .1875rem;
        &.bgeb{
          background: #EBEBEB;
        }
        .text{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #3D3D3D;
          text-align: left;
          font-style: normal;
          &.fs20{
            font-size: 1.25rem;
          }
          &.fs18{
            font-size: 1.125rem;
          }
          &.fontweight500{
             font-weight: 500;
          }
          &.fontweight600{
             font-weight: 600;
          }
        }
      }
    }
    .payment-text{
      margin-top: 3.125rem;
      padding-left: 1.4375rem;
      box-sizing: border-box;
    }
    .center{
      margin-top: 1.5rem;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .center-left{
        padding: 2rem 1.4375rem;
        box-sizing: border-box;
        width: 32.5rem;
        height: 31.3125rem;
        background: #F7F7F7;
        position: relative;
        .check-icon{
          width: 1.5rem; 
          height: 1.5rem;
          background: blue;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 1.5625rem;
          right: .8125rem;
        }
        .center-left-top{
          display: flex;
          align-items: center;
          img{
            width: 1.6875rem;
            height: 1.5rem;
          }
          .center-left-top-title{
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1.25rem;
            margin-left: .875rem;
            color: #3D3D3D;
            text-align: left;
            font-style: normal;
          }
        }
        .pay-text{
          margin-top: .9375rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #3D3D3D;
          text-align: left;
          font-style: normal;
        }
        .input-title{
          margin-top: 1rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 600;
          font-size: 1.125rem;
          color: #3D3D3D;
          text-align: left;
          font-style: normal;
        }
        input{
          margin-top: .3125rem;
          padding: .9375rem 1rem;
          box-sizing: border-box;
          width: 20.625rem;
          font-size: 1rem;
          background: #FFFFFF;
          border: 1px solid #BABABA;
        }

      }
      .center-right{
        width: 32.5rem;
        height: 31.3125rem;
        border: .0625rem solid #BABABA;
        padding: 1.1875rem 1rem;
        box-sizing: border-box;
        img{
          width: 4.5rem;
          height: 2.8125rem;
        }
        .center-right-content{
          width: 30rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #3D3D3D;
          line-height: 1.1875rem;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .box-xtbs{
      display: flex;
      align-items: center;
      margin-top: 1.75rem;
      .xtb{
        padding: .125rem 0;
        border-radius: .4375rem;
        border: 1px solid #BABABA;
        text-align: center;
        min-width: 3.75rem;
        margin-left: 1.125rem;
      }
    }
    .btn-list{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-top:4.375rem;
      gap: 1.375rem;
      .btn{
        width: 17.375rem;
        height: 3.4375rem;
        line-height: 3.4375rem;
        border-radius: 8px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 1.125rem;
        text-align: center;
        font-style: normal;
        &.pay{
          background: #3D3D3D;
          color: #FFFFFF;
        }
         &.cancel{
          background: #FFFFFF;
          color: #3D3D3D;
        }
      }
    }
    .box-bottom{
      width: 100%;
      margin-top: 4.6875rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .box-bottom-text{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: .9375rem;
        color: #3D3D3D;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
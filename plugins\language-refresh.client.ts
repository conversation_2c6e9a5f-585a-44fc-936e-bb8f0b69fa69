export default defineNuxtPlugin(async() => {
  // 只在客户端执行
  if (import.meta.client) {
    // const { useLanguageStore } = await import('~/stores/language')
    const { useLanguageDataRefresh } = await import('~/composables/useLanguageDataRefresh')
    
    // const languageStore = useLanguageStore()
    const { watchLanguageChange } = useLanguageDataRefresh()
    
    // 启动语言变化监听
    watchLanguageChange()
    
    console.warn('🌐 全局语种变化监听器已启动')
  }
}) 
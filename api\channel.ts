import httpRequest from "~/utils/request";
// import noTokenRequest from "~/utils/noTokenRequest";

interface paramsType{
  id?: string,
  locale?: string
  url_key?: string
} 

// 首页数据
export const getHomePageData = async () => {
    return httpRequest.get('/api/mlk/index')
}

// 特价页面数据
export const getSpecialPageData = async () => {
    return httpRequest.get('/api/mlk/promotions/special-products')
}

// 产品详情
export const getProductsDetail = async (params:paramsType) => {
    return httpRequest.get(`/api/mlk/product/detail?url_key=${params.url_key}`)
}

// 相关产品接口
export const getProductsrelated = async (params:paramsType) => {
  return httpRequest.get(`/api/mlk/product/${params.id}/related`)
}
interface dataType { 
  category_ids: number[],
  page: number,
  limit: number,
  sort: string,
  order: string
}

// 产品分类
export const getCategories = async (data:dataType) => {
  return httpRequest.post(`/api/mlk/category/by-categories`,data)
}

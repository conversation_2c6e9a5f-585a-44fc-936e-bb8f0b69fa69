<template>
  <div class="product-details">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-text">{{ $t('productDetails.loading') }}</div>
      <div class="loading-spinner"/>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <div class="error-message">{{ error }}</div>
      <div class="retry-info">{{ $t('productDetails.productNotFound') }}</div>
      <div class="network-warning">{{ $t('productDetails.networkError') }}</div>
      <button class="retry-btn" :disabled="loading" @click="fetchProductDetail">
        {{ $t('productDetails.retry') }}
      </button>
    </div>
    
    <!-- 产品详情内容 -->
    <div v-else class="header">
      <div class="header-left">
        <div class="header-left-top">
            <div class="img-bar">
                <el-affix target=".md" :offset="0" :style="{ width: '100%', height: `${mediaItems.length * 7.625+2.1}rem` }">
                <div v-for="(item, index) in mediaItems" :key="item.id" :class="imgIndex==index?(item.type === 'video' ? 'video-item active' : 'img-item active'):(item.type === 'video' ? 'video-item' : 'img-item')" @click="changeImg(index)">
                  <video 
                    v-if="item.type === 'video'" 
                    :src="item.url" 
                    loop 
                    muted
                    preload="metadata"
                    @loadedmetadata="handleVideoLoaded"
                    @error="handleVideoError"
                  />
                  <img v-else :src="item.url" alt="">
                </div>
              </el-affix>
            </div>
            <div class="md" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; background: transparent;"/>
          <div class="zsimg">
             <el-carousel ref="carousel" style="width: 40.8125rem;height: 40.8125rem;" :autoplay="false" arrow="never" indicator-position="none" :initial-index="imgIndex">
              <el-carousel-item v-for="item in mediaItems" :key="item.id" :name="imgIndex+''">
                <div style="width: 100%;height: 100%; display: flex;align-items: center;justify-content: center;">
                  <video 
                    v-if="item.type === 'video'" 
                    :src="item.url" 
                    loop 
                    controls
                    preload="metadata"
                    style="width: 40.8125rem;cursor: pointer;" 
                    @click="showPreview = true"
                    @loadedmetadata="handleVideoLoaded"
                    @error="handleVideoError"
                    @play="handleVideoPlay"
                    @pause="handleVideoPause"
                    @ended="handleVideoEnded"
                  />
                  <img v-else :src="item.url" alt="" style="width: 40.8125rem;" @click="showPreview = true">
                </div>
              </el-carousel-item>
            </el-carousel>
            <!-- 自定义媒体预览 -->
            <div v-if="showPreview" class="media-preview-overlay" @click="handlePreviewClose">
              <div class="media-preview-content" @click.stop>
                <div class="media-preview-header">
                  <button class="close-btn" :title="$t('productDetails.mediaPreview.close')" @click="handlePreviewClose">×</button>
                </div>
                <div class="media-preview-body">
                  <video 
                    v-if="mediaItems[imgIndex]?.type === 'video'" 
                    :src="mediaItems[imgIndex]?.url" 
                    controls
                    autoplay
                    style="max-width: 100%; max-height: 100%;"
                  />
                  <img 
                    v-else 
                    :src="mediaItems[imgIndex]?.url" 
                    alt="" 
                    style="max-width: 100%; max-height: 100%; object-fit: contain;"
                  >
                </div>
                <div class="media-preview-footer">
                  <button 
                    v-if="imgIndex > 0" 
                    class="nav-btn prev" 
                    :title="$t('productDetails.mediaPreview.previous')"
                    @click="imgIndex--"
                  >
                    ‹
                  </button>
                  <span class="media-counter">{{ imgIndex + 1 }} / {{ mediaItems.length }}</span>
                  <button 
                    v-if="imgIndex < mediaItems.length - 1" 
                    class="nav-btn next" 
                    :title="$t('productDetails.mediaPreview.next')"
                    @click="imgIndex++"
                  >
                    ›
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="header-left-bottom">
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item :title="$t('productDetails.description')" name="1">
              <div>
                {{ productDetail?.short_description }}
              </div>
            </el-collapse-item>
            <el-collapse-item :title="$t('productDetails.specification')" name="2">
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.dimensions') }}
                </div>
                <div class="text-right">
                  <div v-for="item in specifications?.dimensions" :key="item.id">
                    {{ item.title }}
                  </div>
                </div>
              </div>
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.profile') }}
                </div>
                <div class="text-right">
                  {{ specifications?.profile?.formatted }}
                </div>
              </div>
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.dropRating') }}
                </div>
                <div class="text-right">
                  {{ specifications?.drop_rating?.formatted }}
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="header-right">
        <el-affix target=".header-right-bottom" :offset="0" :style="{ width: '30.6875rem', height: affixHeight+16.3 + 'rem' }">
          <div ref="affixContent" style="width: 30.6875rem;">
            <div class="header-right-title" :title="productDetail?.name">
              {{productDetail?.name}}
            </div>
            <div class="header-right-text" :title="productDetail?.short_description">
              {{productDetail?.short_description}}
            </div>
            <div v-if="specialPriceData" class="header-right-shipping">
                <div class="header-right-shipping-text">
                  {{ $t('productDetails.shippingUpTo', { percent: specialPriceData.discount_percent }) }}
                </div>
                <div class="header-right-shipping-text">
                  {{ specialPriceData.end_date }}
                </div>
              </div>
            <div class="header-right-heading">
              {{ $t('productDetails.brands') }}:
            </div>
            <div class="product-select">
                <MLKProductCheckbox v-for="item in brands" :key="item.id" :devices="devices.filter(device => device.parent_id === item.id)" :title="item.name" @change-devices="handleChangeDevices"/>
            </div>
            <div class="header-right-heading">
              {{ $t('productDetails.colors') }} :
            </div>
            <div
              v-for="brandDevice in selectedDeviceList"
              :key="String(brandDevice.parent_id ?? 'unknown')"
              class="brand-device-group"
            >
              <div
                v-for="deviceId in brandDevice.devices"
                :key="deviceId"
                class="device-color-group"
              >
                <div class="device-title">
                  {{ devices.find(d => d.id === deviceId)?.name }}
                </div>
                <div class="color-bar">
                  <MLKProductColor
                    v-for="variant in getDeviceVariants(typeof brandDevice.parent_id === 'number' ? brandDevice.parent_id : 0, Number(deviceId))"
                    :key="variant.id"
                    :amount="variant.customer_group_prices?.[0]?.price"
                    :color="variant.color?.swatch_value"
                    @click="handleColorSelect(variant.color?.id)"
                  />
                </div>
              </div>
            </div>
            <div class="btn add">
              {{ $t('productDetails.addToCart') }}
            </div>
            <div class="btn buy">
              {{ $t('productDetails.buyNow') }}
            </div>
            <div class="checkout">
              {{ $t('productDetails.checkoutSecurely') }}
            </div>
            <div class="co">
              <img src="~/assets/images/detail/co1.png" alt="">
              <img src="~/assets/images/detail/co2.png" alt="">
              <img src="~/assets/images/detail/co3.png" alt="">
              <img src="~/assets/images/detail/co4.png" alt="">
              <img src="~/assets/images/detail/co5.png" alt="">
            </div>
            <div class="accept">
              {{ $t('productDetails.acceptedPayments') }}
            </div>
            <div class="ts">
              {{ $t('productDetails.interestFreePayments') }} <a href="#">{{ $t('productDetails.learnMore') }}</a>
            </div>
            <div class="bottom-img-list">
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/1.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/2.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/3.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/4.png" alt="">
              </div>
            </div>
          </div>
        </el-affix>
        <div class="header-right-bottom" style="position: absolute; bottom: 0; right: 0; width: 100%; height: 1px; background: transparent;"/>
      </div>
    </div>
    <div class="details_img">
      <img v-for="(item,index) in productBanners" :key="index" :src="item.image" alt="item.title">
    </div>
    <!-- 标题 -->
    <MLKTitle :title="$t('productDetails.relatedProducts')"/>
    <!-- 分割线 -->
    <el-divider style="width: 75rem;margin: 0 auto;" />
    <!-- 产品卡片组件 -->
    <div v-if="relatedProducts.length > 0" class="product-car">
      <MLKProductCard 
        v-for="item in relatedProducts" 
        :key="item.id" 
        :product-data="item"
        @add-to-cart="handleRelatedProductAddToCart"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getProductsDetail, getProductsrelated } from '~/api/channel'
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { ElMessage } from 'element-plus'
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

const route = useRoute()
const cartStore = useShoppingCartStore()
const { t } = useI18n()
const urlKey = computed(() => route.params.url_key as string)

// 获取语言事件总线
const { onLanguageChange } = useLanguageEventBus()

// 产品详情数据类型
interface ProductDetail {
  id: number
  name: string
  type: string
  short_description: string
  special_price: string
  end_date: string
  formatted_price: string
  wholesale_info?: string
  customer_group?: string
  brand?: string
  model?: string
  colors?: Array<{
    option_label: string
    swatch_value?: string
  }>
  images?: Array<{
    url: string
  }>
  videos?: Array<{
    id: number | string
    url: string
  }>
}


const affixContent = ref<HTMLElement | null>(null)
const affixHeight = ref(0)


const updateAffixHeight = () => {
  if (affixContent.value) {
    affixHeight.value = affixContent.value.offsetHeight/16
  }
}




// 特殊价格数据类型
interface SpecialPriceData {
  discount_percent: number
  end_date: string
}

// 产品横幅图片类型
interface ProductBanner {
  image: string
  title?: string
}

// 产品详情数据
const productDetail = ref<ProductDetail | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

const customerGroupPrices = ref()
const type = ref()
const brandDeviceVariants = ref()
// 品牌和设备数据类型
interface Brand {
  id: number
  name: string
}

interface Device {
  id: number
  name: string
  parent_id: number
}

const brands = ref<Brand[]>([])
const devices = ref<Device[]>([])
const specifications = ref()
const variants = ref<Array<{
  id: number
  name: string
  option_label: string
  swatch_value?: string,
  customer_group_prices?: {
    formatted_price: string
  }[] | {
    formatted_price: string
  },
  color?: {
    admin_name: string,
    label: string
    id: number
    swatch_value: string
    sort_order: number
  },
  images?: Array<{
    url: string
  }>,
  videos?: Array<{
    id: number | string
    url: string
  }>
}>>([])
// 选中的品牌和设备
const selectedBrand = ref<number>(0)
const selectedDevice = ref<number>(0)
// 选中的颜色
const selectedColor = ref<number | null>(null)

// 特殊价格数据
const specialPriceData = ref<SpecialPriceData | null>(null)

function getDeviceVariants(brandId: number, deviceId: number) {
  if (!brandDeviceVariants.value || !variants.value.length) return [];
  const deviceVariants = brandDeviceVariants.value[brandId]?.[deviceId];
  if (!Array.isArray(deviceVariants)) return [];
  return variants.value.filter(variant => deviceVariants.includes(variant.id));
}

// 监听相关数据变化
watch(
  [loading, productDetail, error, specialPriceData, selectedBrand, selectedDevice, selectedColor],
  () => {
    nextTick(() => {
      updateAffixHeight()
    })
  },
  { immediate: true }
)

// 根据选中的品牌和设备过滤可用的颜色变体
const availableColors = computed(() => {
  // 如果没有品牌设备变体数据，返回所有变体
  if (!brandDeviceVariants.value) {
    return variants.value
  }
  
  // 如果没有选择品牌或设备，返回所有变体
  if (!selectedBrand.value || !selectedDevice.value) {
    return variants.value
  }
  
  const brandVariants = brandDeviceVariants.value[selectedBrand.value]
  if (!brandVariants) {
    return variants.value
  }
  
  const deviceVariants = brandVariants[selectedDevice.value]
  if (!deviceVariants || !Array.isArray(deviceVariants)) {
    return variants.value
  }
  
  // 根据变体ID筛选可用的颜色
  const filteredVariants = variants.value.filter(variant => 
    deviceVariants.includes(variant.id)
  )
  
  // 如果过滤后没有变体，返回所有变体作为后备
  return filteredVariants.length > 0 ? filteredVariants : variants.value
})

// 获取当前选中颜色的变体
const selectedVariant = computed(() => {
  if (!selectedColor.value || !availableColors.value.length) {
    return availableColors.value[0] || null
  }
  return availableColors.value.find(variant => variant.color?.id === selectedColor.value) || availableColors.value[0] || null
})


// 获取产品详情
const fetchProductDetail = async () => {
  if(import.meta.client){
    await nextTick()
  }
  try {
    loading.value = true
    error.value = null
    
    // 验证产品URL key是否存在
    if (!urlKey.value) {
      throw new Error(t('productDetails.productNotFound'))
    }
    
    const response = await getProductsDetail({ url_key: urlKey.value })
    
    if (response.data) {
      productDetail.value = response.data.product
      productBanners.value = response.data.product_banners.images
      specialPriceData.value = response.data.special_price
      specifications.value = response.data.specifications
      customerGroupPrices.value = response.data.customer_group_prices
      type.value = response.data.type
      brands.value = response.data.brands
      devices.value = response.data.devices
      variants.value = response.data.variants
      brandDeviceVariants.value = response.data.brand_device_variants
      
      // 获取产品详情成功后，获取相关产品
      await fetchRelatedProducts()
    } else {
      throw new Error(t('productDetails.productNotFound'))
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : t('productDetails.productNotFound')
    // 自动重试逻辑已移除
  } finally {
    loading.value = false
  }
}

// 获取相关产品
const fetchRelatedProducts = async () => {
  try {
    // 验证产品URL key是否存在
    if (!urlKey.value) {
      return
    }
    
    const response = await getProductsrelated({ id: productDetail.value?.id.toString() })
    
    if (response.success && response.data) {
      // 确保数据格式与MLKProductCard组件兼容
      relatedProducts.value = response.data
      console.warn('相关产品数据:', relatedProducts.value)
    } else {
      // 如果没有相关产品，设置为空数组
      relatedProducts.value = []
      console.warn('没有找到相关产品')
    }
  } catch (err) {
    console.error('获取相关产品失败:', err)
    relatedProducts.value = [] // 出错时设置为空数组
  }
}


// 智能重试策略

const imgIndex = ref(0)

// 媒体项类型定义
interface MediaItem {
  type: 'video' | 'image'
  url: string
  id: number | string
  index: number
}

// 合并视频和图片的计算属性
const mediaItems = computed<MediaItem[]>(() => {
  const items: MediaItem[] = []
  
  // 默认使用 productDetail 中的媒体内容，只有在选中颜色时才使用变体的媒体内容
  let currentImages = productDetail.value?.images || []
  let currentVideos = productDetail.value?.videos || []
  
  // 如果选中了颜色，则使用对应变体的媒体内容
  if (selectedColor.value && selectedVariant.value) {
    currentImages = selectedVariant.value.images || productDetail.value?.images || []
    currentVideos = selectedVariant.value.videos || productDetail.value?.videos || []
  }
  
  // 添加视频
  if (currentVideos.length > 0) {
    currentVideos.forEach((video, index) => {
      items.push({
        type: 'video',
        url: video.url,
        id: video.id,
        index: index
      })
    })
  }
  
  // 添加图片
  if (currentImages.length > 0) {
    currentImages.forEach((image, index) => {
      items.push({
        type: 'image',
        url: image.url,
        id: `img-${index}`,
        index: currentVideos.length + index
      })
    })
  }
  
  return items
})




// 图片数组信息
const productBanners = ref<ProductBanner[]>([])
const handleChange = () =>{
  
}

const activeNames = ref(['1'])
// 走马灯组件
const carousel = ref()

// 点击图片导航栏执行方法
const changeImg = (index: number) => {
  if (carousel.value != null) {
    carousel.value.setActiveItem(index)
  }
  imgIndex.value = index
  
  // 停止当前播放的视频
  if (currentVideo.value && videoPlaying.value) {
    currentVideo.value.pause()
    videoPlaying.value = false
  }
}


const showPreview = ref(false)

// 视频播放状态管理
const videoPlaying = ref(false)
const currentVideo = ref<HTMLVideoElement | null>(null)



// 相关产品数据
const relatedProducts = ref<RelatedProduct[]>([])

// 1. 修改 selectedDeviceList 的声明
const selectedDeviceList = ref<{ parent_id: number | null; devices: (string | number)[] }[]>([])

// 2. 修改 handleChangeDevices
const handleChangeDevices = (value: { parent_id: number | null; devices: (string | number)[] }) => {
  const idx = selectedDeviceList.value.findIndex(item => item.parent_id === value.parent_id)
  if (idx === -1) {
    selectedDeviceList.value.push(value)
  } else {
    selectedDeviceList.value[idx] = value
  }
}






// 处理颜色选择
const handleColorSelect = (colorId: number | undefined) => {
  if (colorId !== undefined) {
    selectedColor.value = colorId
    // 重置图片索引到第一张
    imgIndex.value = 0
  }
}

// 处理视频加载完成
const handleVideoLoaded = (event: Event) => {
  const video = event.target as HTMLVideoElement
  console.warn('视频加载完成:', video.src)
}

// 处理视频加载错误
const handleVideoError = (event: Event) => {
  const video = event.target as HTMLVideoElement
  console.error('视频加载失败:', video.src, event)
}

// 处理视频播放
const handleVideoPlay = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = true
  currentVideo.value = video
  console.warn('视频开始播放:', video.src)
}

// 处理视频暂停
const handleVideoPause = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = false
  console.warn('视频暂停:', video.src)
}

// 处理视频结束
const handleVideoEnded = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = false
  console.warn('视频播放结束:', video.src)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!showPreview.value) return
  
  switch (event.key) {
    case 'Escape':
      showPreview.value = false
      break
    case 'ArrowLeft':
      if (imgIndex.value > 0) {
        imgIndex.value--
      }
      break
    case 'ArrowRight':
      if (imgIndex.value < mediaItems.value.length - 1) {
        imgIndex.value++
      }
      break
  }
}

// 处理预览关闭
const handlePreviewClose = () => {
  showPreview.value = false
  // 重置视频状态
  if (currentVideo.value) {
    currentVideo.value.pause()
    videoPlaying.value = false
  }
}

// 相关产品数据类型
interface RelatedProduct {
  id: number
  url_key: string
  name: string
  base_image: string
  formatted_price: string
  short_description: string
  price?: number
  color?: Array<{
    option_label: string
    swatch_value?: string
  }>
  selectedColor?: string
}

// 处理相关产品添加到购物车
const handleRelatedProductAddToCart = async (product: RelatedProduct) => {
  try {
    // 确保价格是数字类型
    const price = product.price || parseFloat(product.formatted_price.replace(/[^0-9.]/g, ''))
    
    // 调用购物车store的添加方法
    const result = await cartStore.addToCart({
      id: product.id.toString(),
      name: product.name,
      base_image: product.base_image,
      formatted_price: product.formatted_price,
      short_description: product.short_description,
      product_id: product.id.toString(),
      price: price,
      selectedColor: product.selectedColor,
      attributes: product.selectedColor ? {
        color: {
          option_label: product.selectedColor
        }
      } : undefined
    }, 1)
    
    if (result?.success) {
      ElMessage.success(t('product.addToCartSuccess'))
    } else {
      ElMessage.error(result?.message || t('product.addToCartFailed'))
    }
  } catch{
    ElMessage.error(t('product.addToCartError'))
  }
}

// 监听路由参数变化
watch(() => route.params.url_key, (newUrlKey) => {
  if (newUrlKey) {
    // 路由参数变化时重新获取产品详情（相关产品会在fetchProductDetail中自动获取）
    fetchProductDetail()
  }
}, { immediate: true })

// 监听语种变化并重新获取数据
onLanguageChange(async () => {
  await fetchProductDetail()
})

// 调用动画效果和获取产品详情
onMounted(() => {
  // 只在客户端执行
  if (import.meta.client) {
    // 监听键盘事件
    window.addEventListener('keydown', handleKeydown);
  }
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('keydown', handleKeydown);
    
    // 清理视频资源
    if (currentVideo.value) {
      currentVideo.value.pause()
      currentVideo.value.src = ''
      currentVideo.value.load()
    }
  }
})


</script>

<style lang="scss" scoped>
.product-details{
  width: 100%;
  
  .loading, .error {
    text-align: center;
    padding: 2rem;
    font-size: 1.125rem;
    color: #6C6C6C;
  }
  
  .loading {
    .loading-text {
      margin-bottom: 1rem;
      font-weight: 500;
    }
    
    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #ff4757;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .error {
    color: #ff4757;
    text-align: center;
    
    .error-message {
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
    
    .retry-info {
      margin-top: 0.5rem;
      font-size: 0.75rem;
      color: #6C6C6C;
    }
    
    .network-warning {
      margin-top: 0.5rem;
      font-size: 0.875rem;
      color: #ff9500;
      font-weight: 500;
    }
    
    .retry-btn {
      margin-top: 1rem;
      padding: 0.5rem 1rem;
      background: #ff4757;
      color: white;
      border: none;
      border-radius: 0.25rem;
      cursor: pointer;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      
      &:hover:not(:disabled) {
        background: #ff3742;
      }
      
      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        color: #666;
      }
    }
  }
  .header{
    margin: 4.375rem auto;
    width: 91.25rem;
    display: flex;
    justify-content: space-between;
    .header-left{
      width: 56.4375rem;
      .header-left-top{
        width: 56.375rem;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .img-bar{
          width: 6.875rem;
          .video-item{
            cursor: pointer;
            width: 6.875rem;
            height: 6.875rem;
            background: #fff;
            border-radius: .5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: .75rem;
            transition: all 0.3s ease;
            position: relative;
            
            &:hover {
              background: #f5f5f5;
            }
            
            &.active{
              background: #F6F6F6;
            }
            
            video{
              width: 4.6875rem;
              height: 5.8125rem;
              border-radius: .25rem;
              object-fit: cover;
              pointer-events: none;
            }
            
            &::after {
              content: '▶';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 1.5rem;
              text-shadow: 0 0 4px rgba(0,0,0,0.8);
              pointer-events: none;
            }
          }
          .img-item{
            cursor: pointer;
            width: 6.875rem;
            height: 6.875rem;
            background: #fff;
            border-radius: .5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: .75rem;
            &:last-child{
              margin-bottom: none;
            }
            &.active{
              background: #F6F6F6;
            }
            img{
              width: 4.6875rem;
              height: 5.8125rem;
            }
          }
        }
        .zsimg{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 47.75rem;
          height: 47.75rem;
          background: #F6F6F6;
          border-radius: .8125rem;
          margin-left: 1.75rem;
          cursor: zoom-in;

          //主预览图宽高相同
          ::v-deep .el-carousel__container {
            height: 100%;
          }

          video {
            border-radius: .5rem;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
          
          img {
            border-radius: .5rem;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }
      }
      .header-left-bottom{
        margin-top: 2.4375rem;
        width: 100%;
        border-radius: .5rem;
        border: 1px solid #EBEBEB;
        overflow: hidden;
        padding: 0 1.5rem;
        box-sizing: border-box;
        .text{
          display: flex;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 13px;
          color: #565656;
          text-align: left;
          font-style: normal;
          padding:.625rem 0;
          border-top: 1px solid #EBEBEB;
          .text-left{
            width: 12.5rem;
          }
        } 
      }
      
    }
    .header-right {
      width: 30.625rem;
      .header-right-title {
        width: 100%;
        height: 2.5rem;
        font-family: Helvetica;
        font-size: 2.5rem;
        margin-top: .4375rem;
        color: #000000;
        line-height: 2.5rem;
        font-style: normal;
        text-transform: none;
        margin-bottom: .5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .header-right-text {
        width: 100%;
        height: 1.5rem;
        font-family: AppleSystemUIFont;
        font-size: .875rem;
        color: #6C6C6C;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: .4375rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .header-right-shipping {
        width: 30.625rem;
        height: 3rem;
        background: #F3F3F3;
        margin-top: .625rem;
        border-radius: .375rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 .9375rem;
        box-sizing: border-box;

        .header-right-shipping-text {
          height: 1.25rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: .875rem;
          color: #6C6C6C;
          line-height: 1.25rem;
          font-style: normal;
        }
      }

      .price {
        height: 3rem;
        font-family: Helvetica;
        font-size: 2rem;
        color: #000000;
        line-height: 3rem;
        font-style: normal;
        text-transform: none;
        margin-top: 1.0625rem;
      }

      .header-right-heading {
        height: 1.3125rem;
        font-family: AppleSystemUIFont;
        font-size: 1.125rem;
        color: #0C0C0C;
        line-height: 1.3125rem;
        font-style: normal;
        margin-top: 1.1875rem;
      }

      .brand-device-group {
        margin-bottom: 1.5rem;
        padding: 1rem 0;
        border-bottom: 1px solid #EBEBEB;
        &:last-child {
          border-bottom: none;
        }
        .device-color-group {
          margin-bottom: 0.75rem;
          .device-title {
            font-weight: 500;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: #333;
            user-select: text !important;
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
          }
          .color-bar {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
          }
        }
      }

      .product-select {
        margin-top: .75rem;
        // height: 3rem;
      }

      .color-bar {
        width: 30.8125rem;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;

      }


      .btn {
        width: 22.375rem;
        height: 3.625rem;
        line-height: 3.625rem;
        border-radius: .5rem;
        font-family: Helvetica;
        font-size: 1rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin: auto;
        margin-top: .75rem;
        cursor: pointer;

        &.add {
          color: #000000;
          background: #FFBA60;
        }

        &.buy {
          color: #FFFFFF;
          background: #000000;
        }
      }

      .checkout {
        font-family: AppleSystemUIFont;
        font-size: 1.125rem;
        color: #565656;
        margin: 1.5rem auto;
        font-style: normal;
        text-align: center;
      }

      .co {
        margin-top: .5rem;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;

        img {
          width: 4.125rem;
          height: 2.25rem;
        }
      }

      .accept {
        margin-top: 1.5rem;
        height: 1.3125rem;
        font-family: SFNS;
        font-weight: 400;
        font-size: 1.125rem;
        color: #3D3D3D;
        line-height: 1.3125rem;
        text-align: center;
        font-style: normal;
      }

      .ts {
        margin-top: 1.6875rem;
        height: 1.5rem;
        font-family: AppleSystemUIFont;
        font-size: .875rem;
        color: #6C6C6C;
        line-height: 1.5rem;
        text-align: center;
        font-style: normal;
        text-transform: none;

        a {
          text-decoration: underline;
          color: #6C6C6C;

          span {
            color: #000000;
          }
        }
      }

      .bottom-img-list {
        margin: 1.0625rem auto;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2.3125rem;

        .bottom-img-item {
          width: 2.0625rem;
          height: 2.0625rem;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .details_img {
    width: 87.5rem;
    margin: 1.25rem auto;

    img {
      border-radius: 1rem;
      width: 100%;
      margin-bottom: 1rem;
    }
    .Rectangle_1 {
      width: 87.5rem;
      height: 31.625rem;
      background: #D8D8D8;
      border-radius: 1rem;
      margin: 2.5rem 0;
      // border: 1px solid #979797;
    }

    .Rectangle_2 {
      width: 87.5rem;
      height: 43.75rem;
      background: #D8D8D8;
      border-radius: 1rem;
      margin: 2.5rem 0;
      // border: 1px solid #979797;
    }
  }

  .product-car{
    width: 84.75rem;
    margin: 4.1875rem auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  

  
  // 媒体预览样式
  .media-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .media-preview-content {
      position: relative;
      width: 90%;
      height: 90%;
      background: #000;
      border-radius: 0.5rem;
      display: flex;
      flex-direction: column;
      
      .media-preview-header {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 10;
        padding: 1rem;
        
        .close-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          font-size: 2rem;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
      
      .media-preview-body {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
      }
      
      .media-preview-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        
        .nav-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          font-size: 2rem;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
          
          &.prev {
            transform: translateX(-50%);
          }
          
          &.next {
            transform: translateX(50%);
          }
        }
        
        .media-counter {
          color: white;
          font-size: 1rem;
          background: rgba(0, 0, 0, 0.5);
          padding: 0.5rem 1rem;
          border-radius: 1rem;
        }
      }
    }
  }
}

::v-deep .el-carousel__container{
  width: 100%;
  height: 47.75rem;
}

// 自定义下拉框样式
::v-deep .product-select .el-select {
  .el-input__wrapper {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
  }
  
  .el-input__inner {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 1rem;
    color: #000000;
    text-align: center;
    padding: 0;
    height: 2.5rem;
    line-height: 2.5rem;
  }
  
  .el-input__suffix {
    display: none;
  }
}
</style>
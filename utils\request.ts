import type { UseFetchOptions } from "nuxt/app";
import { ElMessage } from 'element-plus'
import { useLanguageStore } from '~/stores/language';
import { useUserStore } from '~/stores/user';
import { useLanguageEventBus } from '~/composables/useLanguageEventBus';
// HTTP 请求的方法类型
type Methods = "GET" | "POST" | "DELETE" | "PUT";

// 请求结果数据格式
export interface IResultData<T> {
  locale: string;
  data: T;
  message: string;
  success: boolean
}

/**
 * api请求封装，使用useFetch函数或$fetch函数
 * @param { string } url 请求地址
 * @param { string } method 请求方法
 * @param { object } data 请求数据
 * @param { UseFetchOptions } options 请求选项
 */
class HttpRequest {
  // 标记是否正在刷新token
  private isRefreshing = false;
  // 存储等待token刷新的请求
  private failedQueue: Array<{
    resolve: (value: unknown) => void;
    reject: (error: unknown) => void;
  }> = [];

  // 处理等待队列中的请求
  private processQueue(error: unknown, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    this.failedQueue = [];
  }

  request<T>(url: string, method: Methods, data, options?: UseFetchOptions<T>) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return new Promise<IResultData<any>>((resolve, reject) => {
      // 获取运行时配置
      const config = useRuntimeConfig();
      const BASE_URL = config.public.apiBase || 'http://mlk.shiyus.com';
      
      // 继承UseFetchOptions类型，包含了baseURL和method两个属性
      const newOptions: UseFetchOptions<T> = {
        baseURL: BASE_URL,
        method,
        // 添加 key 属性确保缓存一致性
        key: `${method}-${url}-${JSON.stringify(data || {})}`,
        // 确保服务端和客户端都能获取数据
        server: true,
        ...options,
      };

      // 在函数内部获取语言设置
      const languageStore = useLanguageStore()
      const currentLanguage = computed(() => languageStore.language)
      const { onLanguageChange } = useLanguageEventBus()
      
      newOptions.headers = {
        'Accept': 'application/json',
        'X-Locale': currentLanguage.value
      }
      
      // 使用事件总线监听语言变化，确保及时更新
      onLanguageChange((newLanguage: string) => {
        if (newOptions.headers) {
          newOptions.headers['X-Locale'] = newLanguage
          console.warn('🌐 request 语言已更新:', newLanguage)
        }
      })
      
      // 保留原有的 watch 监听作为备用
      watch(currentLanguage, (newVal) => {
        if (newOptions.headers) {
          newOptions.headers['X-Locale'] = newVal
        }
      })
      
      // 从用户store获取token
      const userStore = useUserStore()
      const token = userStore.token
      if (token) {
        newOptions.headers['Authorization'] = `Bearer ${token}`
      }

      // 根据请求方法处理请求的数据
      if (method === "GET" || method === "DELETE") {
        // 将数据设置为newOptions的params属性
        newOptions.params = data;
      }
      if (method === "POST" || method === "PUT") {
        // 将数据设置为newOptions的body属性
        newOptions.body = data;
      }

      const fetchMethod = useFetch;

      // 发送请求
      fetchMethod(url, newOptions)
        .then((res) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          if (res && (res as any).status) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            resolve((res as any).data.value);
          } else if (res) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            resolve(res as any);
          } else {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            resolve({} as any);
          }
        })
        .catch(async (error) => {
          // 检查是否是401错误（token过期）
          if (error.response && error.response.status === 401) {
            // 如果是刷新token的请求失败，直接返回错误
            if (url.includes('/refresh-token')) {
              userStore.logout();
              return;
            }

            // 如果正在刷新token，将请求加入等待队列
            if (this.isRefreshing) {
              return new Promise((resolve, reject) => {
                this.failedQueue.push({ resolve, reject });
              }).then(() => {
                // 重新发送原请求
                return this.request(url, method, data, options);
              }).catch((err) => {
                reject(err);
              });
            }

            this.isRefreshing = true;

            try {
              // 尝试刷新token
              const refreshResult = await userStore.autoRefreshToken();
              
              if (refreshResult) {
                // token刷新成功，处理等待队列
                this.processQueue(null, userStore.token);
                
                // 重新发送原请求
                const retryResult = await this.request(url, method, data, options);
                resolve(retryResult);
              } else {
                // token刷新失败，清除用户状态
                this.processQueue(new Error('Token刷新失败'), null);
                reject(new Error('Token已过期，请重新登录'));
              }
            } catch (refreshError) {
              // 刷新token时出错
              this.processQueue(refreshError, null);
              reject(refreshError);
            } finally {
              this.isRefreshing = false;
            }
            return;
          }

          let errorMessage = "服务端错误";
          if (error.response && error.response._data) {
            let data = error.response._data;
            if (typeof error.response._data === 'string') {
              try {
                data = JSON.parse(error.response._data);
              } catch {
                errorMessage = error.response._data;
              }
            }
            if (data.errors) {
              const errorMessages: string[] = [];
              for (const key in data.errors) {
                errorMessages.push(`${data.errors[key].join(', ')}`);
              }
              errorMessage = errorMessages.join('; ') || errorMessage;
            } else {
              errorMessage = data.message || errorMessage;
            }
          }
          if (import.meta.client) {
            ElMessage.error(errorMessage)
          }

          if (error.response &&
            (error.response._data.code === 40001 || error.response._data.code === 40002 || error.response._data.code === 40003)) {
            resolve(error.response._data);
          }
          reject(error.response ? error.response._data : errorMessage);
        });
    });
  }

  // 封装常用方法
  get<T>(url: string, params?: T, options?: UseFetchOptions<T>) {
    return this.request(url, "GET", params, options);
  }

  post<T>(url: string, data?: T, options?: UseFetchOptions<T>) {
    return this.request(url, "POST", data, options);
  }

  put<T>(url: string, data: T, options?: UseFetchOptions<T>) {
    return this.request(url, "PUT", data, options);
  }

  delete<T>(url: string, params: T, options?: UseFetchOptions<T>) {
    return this.request(url, "DELETE", params, options);
  }
}

// 实例化 HttpRequest 并导出
const httpRequest = new HttpRequest();
export default httpRequest;

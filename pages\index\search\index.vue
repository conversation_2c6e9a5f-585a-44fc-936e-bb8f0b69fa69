<template>
  <div class="search">
    <MLKTechnical/>
    <div class="mbx-box">
      <div class="mbx">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/home' }">{{ $t('search.home') }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="select">
        <el-select v-model="searchParams.sort" :teleported="false" :placeholder="$t('search.byRating')" style="width: 16rem" @change="handleSortChange">
          <el-option
            v-for="item in toolbar"
            :key="item.position"
            :label="item.title"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
    <div class="conter">
      <div class="conter-left">
        <div class="conter-left-top">
          {{ $t('search.showHideFilters') }}
        </div>
        <div class="conter-left-content">
            <el-collapse @change="handleChange">
              <el-collapse-item v-for="item in filterableAttributes" :key="item.id" :title="item.name" :name="item.id">
                <div v-if="item.code === 'color'">
                  <div class="color-list">
                    <div v-for="color in item.options" :key="color.id" class="color-item">
                      <div 
                        class="yuan" 
                        :class="{ 'active': selectedFilters[item.id.toString()]?.some(id => id.toString() === (color.admin_name || color.id).toString()) }"
                        :style="{ background: color.admin_name,'--hover-color': color.admin_name}"
                        @click="() => handleColorClick(item.id, color.admin_name || color.id)"
                      />
                      <div class="color-item-text">{{color.label}}</div>
                    </div>
                  </div>
                  <div class="showall" @click="() => handleCheckAllChange(item.id)">
                      {{ isAllSelected[item.id.toString()] ? $t('search.hideAll') : $t('search.showAll') }}
                  </div>
                </div>
                <div v-else>
                  <div class="check-box">
                    <div class="check">
                      <el-checkbox-group
                        v-model="selectedFilters[item.id]"
                        @change="(value) => handleDeviceFilterChange(item.id, value)"
                      >
                        <el-checkbox v-for="brand in item.options" :key="brand.id" :label="brand.label" :value="brand.admin_name">
                          {{ brand.label }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                    <div class="showall" @click="() => handleCheckAllChange(item.id)">
                        {{ isAllSelected[item.id.toString()] ? $t('search.hideAll') : $t('search.showAll') }}
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
        </div>
      </div>
      <div class="conter-right">
        <div class="proudct-list">
          <MLKProductCard 
            v-for="(item, index) in productList" 
            :key="item.id || index" 
            class="proudct-item"
            :product-data="item"
            @add-to-cart="handleAddToCart"
          />
        </div>

        <div v-if="!loading && productList.length > 0" class="pagination">
          <el-pagination 
            background 
            layout="prev, pager, next" 
            size="small" 
            :total="total" 
            :current-page="currentPage"
            :page-size="pageSize"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import type { CollapseModelValue, CheckboxValueType  } from 'element-plus'
import { ElMessage } from 'element-plus'

import { getProductSearch } from '~/api/public'
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

// 产品类型定义
interface Product {
  id: number
  name: string
  url_key: string
  base_image: string
  formatted_price: string
  short_description: string
  color: { option_label: string; swatch_value?: string }[]
}

// 可筛选属性类型定义
interface FilterableAttribute {
  id: string | number
  name: string
  code: string
  options: Array<{
    id: string | number
    label: string
    admin_name?: string
  }>
}

// 排序选项
const toolbar = ref()

// 搜索参数
const searchParams = ref<Record<string, string>>({
  query: '',
  category_id: '',
  price: '',
  sort: '',
  order: '',
  limit: '6',
  page: '1',
})

// 产品数据
const productList = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(6)
const loading = ref(false)
const filterableAttributes = ref<FilterableAttribute[]>([])

// 筛选选项
const selectedFilters = ref<Record<string, (string | number)[]>>({})
const isAllSelected = ref<Record<string, boolean>>({})

// 搜索产品方法
const searchProducts = async () => {
  if(import.meta.client){
    await nextTick()
  }
  try {
    
    // 过滤掉空值参数
    const filteredParams: Record<string, string> = {}
    Object.entries(searchParams.value).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        filteredParams[key] = value.trim()
      }
    })
    
    
    const res = await getProductSearch(filteredParams)
    
    if (res && res.data) {
      productList.value = res.data.products || []
      filterableAttributes.value = res.data.filterable_attributes || []
      toolbar.value = res.data.toolbar || []
      total.value = res.data.total || 0
      // 初始化筛选器状态
      filterableAttributes.value.forEach(item => {
        const filterKey = item.id.toString()
        if (!selectedFilters.value[filterKey]) {
          selectedFilters.value[filterKey] = []
          isAllSelected.value[filterKey] = false
        }
      })
    } else {
      productList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
    productList.value = []
    total.value = 0
  } finally {
    // loading.value = false // 不再设置 loading 状态
  }
}

// 处理排序变化
const handleSortChange = (value: string) => {
  if (value && value.trim() !== '') {
    const [field, order] = value.split('_')
    searchParams.value.sort = field || ''
    searchParams.value.order = order || ''
  } else {
    searchParams.value.sort = ''
    searchParams.value.order = ''
  }
  
  searchProducts()
}

// 处理设备筛选变化
const handleDeviceFilterChange = (filterId: string | number, value: CheckboxValueType[]) => {
  console.warn('handleDeviceFilterChange 被调用:', { filterId, value })
  
  const filterKey = filterId.toString()
  selectedFilters.value[filterKey] = value as (string | number)[]
  
  // 检查是否全选
  const currentFilter = filterableAttributes.value.find(item => item.id === filterId)
  if (currentFilter) {
    isAllSelected.value[filterKey] = value.length === currentFilter.options.length
  }
  
  // 强制触发响应式更新
  selectedFilters.value = { ...selectedFilters.value }
  
  // 更新搜索参数
  updateSearchParams()
  searchProducts()
}

// 处理颜色点击
const handleColorClick = (filterId: string | number, colorId: string | number | undefined) => {
  const currentFilter = filterableAttributes.value.find(item => item.id === filterId)
  if (!currentFilter || colorId === undefined) return

  // 确保 filterId 是字符串类型，用于对象键
  const filterKey = filterId.toString()
  const currentSelected = selectedFilters.value[filterKey] || []
  
  // 统一转换为字符串进行比较，避免类型不匹配问题
  const colorIdStr = colorId.toString()
  const isSelected = currentSelected.some(id => id.toString() === colorIdStr)

  if (isSelected) {
    // 取消选中
    selectedFilters.value[filterKey] = currentSelected.filter(id => id.toString() !== colorIdStr)
  } else {
    // 选中
    selectedFilters.value[filterKey] = [...currentSelected, colorId]
  }

  // 检查是否全选
  const updatedSelected = selectedFilters.value[filterKey] || []
  isAllSelected.value[filterKey] = updatedSelected.length === currentFilter.options.length

  // 强制触发响应式更新
  selectedFilters.value = { ...selectedFilters.value }

  // 更新搜索参数
  updateSearchParams()
  searchProducts()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  searchParams.value.page = page.toString()
  
  searchProducts()
}

// 处理全选/取消全选
const handleCheckAllChange = (filterId: string | number) => {
  const currentFilter = filterableAttributes.value.find(item => item.id === filterId)
  if (!currentFilter) return
  
  const filterKey = filterId.toString()
  const isCurrentlyAllSelected = isAllSelected.value[filterKey]
  
  if (isCurrentlyAllSelected) {
    // 取消全选
    selectedFilters.value[filterKey] = []
    isAllSelected.value[filterKey] = false
  } else {
    // 全选
    selectedFilters.value[filterKey] = currentFilter.options.map(option => option.admin_name || option.id)
    isAllSelected.value[filterKey] = true
  }
  
  // 强制触发响应式更新
  selectedFilters.value = { ...selectedFilters.value }
  
  // 更新搜索参数
  updateSearchParams()
  searchProducts()
}

// 更新搜索参数
const updateSearchParams = () => {
  // 创建新的搜索参数对象，保留基本参数
  const newSearchParams: Record<string, string> = {}
  
  // 保留现有的基本参数
  if (searchParams.value.query && searchParams.value.query.trim() !== '') {
    newSearchParams.query = searchParams.value.query.trim()
  }
  if (searchParams.value.sort && searchParams.value.sort.trim() !== '') {
    newSearchParams.sort = searchParams.value.sort.trim()
  }
  if (searchParams.value.order && searchParams.value.order.trim() !== '') {
    newSearchParams.order = searchParams.value.order.trim()
  }
  if (searchParams.value.limit && searchParams.value.limit.trim() !== '') {
    newSearchParams.limit = searchParams.value.limit.trim()
  }
  if (searchParams.value.page && searchParams.value.page.trim() !== '') {
    newSearchParams.page = searchParams.value.page.trim()
  }
  
  // 添加新的筛选参数
  Object.entries(selectedFilters.value).forEach(([filterId, selectedValues]) => {
    if (selectedValues && selectedValues.length > 0) {
      const currentFilter = filterableAttributes.value.find(item => item.id.toString() === filterId)
      if (currentFilter) {
        // 过滤掉空值
        const filteredValues = selectedValues.filter(value => value !== null && value !== undefined && value !== '')
        if (filteredValues.length > 0) {
          newSearchParams[currentFilter.code] = filteredValues.join(',')
        }
      }
    }
  })
  
  // 更新搜索参数
  searchParams.value = newSearchParams
  
}

const handleChange = (_val: CollapseModelValue) => {
  // console.log(_val)
}

// 获取购物车store实例
const cartStore = useShoppingCartStore()

// 语种监听相关
const { onLanguageChange } = useLanguageEventBus()

// 监听语种变化
onLanguageChange(async () => {
  // 语种变化时重新获取搜索数据
  try {
    await searchProducts()
  } catch (error) {
    console.error('🌐 语种切换后重新获取搜索数据失败:', error)
  }
})

// 处理添加购物车
const handleAddToCart = async (product: {
    id: number
    name: string
    base_image: string
    formatted_price: string
    short_description: string
    product_id?: string
    price?: number
    selectedColor?: string
}) => {
    try {
        console.warn('添加商品到购物车:', product)
        // 调用购物车store的添加方法
        const result = await cartStore.addToCart({
          id: product.id.toString(),
          product_id: product.product_id || product.id.toString(),
          name: product.name,
          price: product.price || parseFloat(product.formatted_price.replace(/[^0-9.]/g, '')),
          base_image: product.base_image,
          selectedColor: product.selectedColor,
          formatted_price: '',
          short_description: ''
        }, 1)
        
        if (result && result.success) {
            // 显示成功提示
            ElMessage.success($t('search.addToCartSuccess'))
        } else {
            // 显示错误提示
            ElMessage.error(result?.message || $t('search.addToCartFailed'))
        }
    } catch (error) {
        console.error('添加购物车失败:', error)
        ElMessage.error($t('search.addToCartError'))
    }
}

// 页面加载时执行搜索
onMounted(() => {
  // 从URL参数中获取搜索条件
  const route = useRoute()
  // 更新搜索参数
  if (route.query.query) {
    searchParams.value.query = route.query.query as string
  }
  if (route.query.category_id) {
    searchParams.value.category_id = route.query.category_id as string
  }
  if (route.query.brand) {
    // 如果有brand参数，需要转换为对应的筛选参数
    // 这里可以根据实际需求调整
  }
  if (route.query.device) {
    // 如果有device参数，需要转换为对应的筛选参数
    // 这里可以根据实际需求调整
  }
  if (route.query.sort) {
    searchParams.value.sort = route.query.sort as string
  }
  if (route.query.order) {
    searchParams.value.order = route.query.order as string
  }
  if (route.query.page) {
    searchParams.value.page = route.query.page as string
    currentPage.value = parseInt(route.query.page as string)
  }
  
  // 执行搜索
  searchProducts()
})
</script>

<style lang="scss" scoped>
.search{
  width: 100%;
  
  .mbx-box{
    padding-left: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 83.0625rem;
    margin: 2.8125rem auto;
    box-sizing: border-box;
    .select{
      width: 16rem;
      height: 2.5rem;
      border-radius: 8px;
    }
  }
  .conter{
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 83.0625rem;
    margin: 2.8125rem auto;
    .conter-left{
      width: 19rem;
      background: #FFFFFF;
      border-radius: .5rem;
      padding: 0 1.125rem;
      box-sizing: border-box;
      .conter-left-top{
        height: 1.5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 500;
        font-size: 1.25rem;
        color: #3D3D3D;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        padding-bottom: 1.0625rem;
        border-bottom: .125rem solid #EBEBEB;
      }
      .conter-left-content{
        width: 100%;
        .showall{
          margin: 0 auto;
          padding: .5rem 0;
          width: 5.375rem;
          line-height: 1.5rem;
          background: #000000;
          border-radius: .3125rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: .875rem;
          color: #FFFFFF;
          text-align: center;
          font-style: normal;
          margin-top: 1.8125rem;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #333333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
          
          &:active {
            background: #666666;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        }
        .check{
          width: 50%;
          .check-box{
            width: 50%;
          }
        }
        .color-list{
          margin-top: 1.875rem;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 1.5rem;
          padding-left: 2.625rem;
          width: 100%;
          box-sizing: border-box;
          .color-item{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .yuan{
              width: 1.375rem;
              height: 1.375rem;
              border-radius: 50%;
              cursor: pointer;
              transition: all 0.3s ease;
              border: 2px solid transparent;
              &:hover {
                box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
              }
              
              &.active {
                box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
                transform: scale(1.1);
                border: 2px solid #000;
              }
              
            }
            .color-item-text{
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: .75rem;
              color: #3D3D3D;
              line-height: 1.0625rem;
              font-style: normal;
              margin-top: 0.5rem;
            }
          }
          
        }
      }
    }
    .conter-right{
      width: 63.125rem;
      
      .loading-container{
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 0;
        
        p{
          margin-top: 1rem;
          color: #666;
          font-size: 1rem;
        }
      }
      
      .empty-container{
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 0;
        text-align: center;
        
        .empty-icon{
          margin-bottom: 1.5rem;
        }
        
        h3{
          font-size: 1.5rem;
          color: #333;
          margin-bottom: 1rem;
          font-weight: 500;
        }
        
        p{
          color: #666;
          font-size: 1rem;
          line-height: 1.5;
          margin-bottom: 0.5rem;
          
          &:last-child{
            margin-bottom: 0;
          }
        }
        
        .retry-section{
          margin-top: 2rem;
          text-align: center;
          
          .retry-info{
            color: #999;
            font-size: 0.875rem;
            margin-bottom: 1rem;
          }
          
          .retry-button{
            background: #000;
            border-color: #000;
            color: #fff;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover{
              background: #333;
              border-color: #333;
              transform: translateY(-2px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }
            
            &:active{
              background: #666;
              border-color: #666;
              transform: translateY(0);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
      
      .proudct-list{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        .proudct-item{
          margin-bottom: 2.5rem;
        }
      }
      .pagination{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        ::v-deep .el-pagination.is-background .el-pager li.is-active{
          background-color: #000 !important;
          color: #fff;
        }
      }
    }
  }
}
</style>
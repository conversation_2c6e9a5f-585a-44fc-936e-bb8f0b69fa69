<template>
    <div class="MerryGoRound_page">
        <div class="MerryGoRound_content">
            <ClientOnly>
                <swiper 
                v-if="dataList && dataList.length > 0"
                :key="`MerryGoRound-${swiperKey}`" 
                :slides-per-view="4" 
                :space-between="20"
                :allow-touch-move="false" 
                :breakpoints="breakpoints"
                :modules="modules" 
                class="mySwiper"
                @swiper="onSwiper">
                    <swiper-slide 
                    v-for="item in dataList || []" 
                    :key="item.id"
                    style="display: flex;justify-content: space-between;background-color: white;">
                        <div 
                        class="MerryGoRound_goods"
                        :style="{ cursor: item.link ? 'pointer' : 'default' }"
                        @click="handleItemClick(item)">
                            <div class="MerryGoRound_title" :title="item.title">
                                {{ item.title }}
                            </div>
                            <div class="MerryGoRound_photo">
                                <img style="width: 100%" :src="item.image" alt="">
                            </div>

                        </div>
                    </swiper-slide>
                </swiper>
            </ClientOnly>
            <div class="Carousel_arrow">
                <div :class="{'disabled': isBeginning}" @click="prevSlide"><el-icon :size="27">
                        <Back />
                    </el-icon></div>
                <div :class="{'disabled': isEnd}" @click="nextPage"><el-icon :size="27">
                        <Right />
                    </el-icon></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';

import 'swiper/css/free-mode';
import 'swiper/css/pagination';

import { FreeMode, Pagination, Navigation } from 'swiper/modules';
interface MerryGoRoundItem {
    id: number;
    title?: string;
    link?: string;
    image?: string;
    url?: string;
}
const props = defineProps<{
    dataList: MerryGoRoundItem[] | undefined;
}>()
//   console.warn('MerryGoRound=======================》',props.dataList?.length)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperInstance = ref<any>(null);
const swiperKey = ref(0);
const isInitialized = ref(false);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onSwiper = (swiper: any) => {
  swiperInstance.value = swiper;
    isInitialized.value = true;
  // 添加滑动事件监听器
  swiper.on('slideChange', () => {
    console.warn('🔄 滑动事件触发');
    
    if (swiper.isBeginning) {
      // 到达开始位置
      console.warn('到达开始的地方');
      isBeginning.value = true;
    } else if (swiper.isEnd) {
      // 到达结束位置
      console.warn('到达结束的地方');
      isEnd.value = true;
    } else {
      isBeginning.value = false;
      isEnd.value = false;
    }
  });
  
  // 初始化状态
  if (swiper.isBeginning) {
    isBeginning.value = true;
  }
  if (swiper.isEnd) {
    isEnd.value = true;
  }
};
const breakpoints = ref({
    '640': {
        slidesPerView: 4,
        spaceBetween: 20,
    },
    '768': {
        slidesPerView: 4,
        spaceBetween: 40,
    },
    '1024': {
        slidesPerView: 4,
        spaceBetween: 50,
    },
    '1920': {
        slidesPerView: 4,
        spaceBetween: 20,
    },
})
const modules = [Navigation, FreeMode, Pagination];


const prevSlide = () => {
    if (swiperInstance.value) {
        swiperInstance.value.slidePrev();
    }
};
const nextPage = () => {
    if (swiperInstance.value) {
        swiperInstance.value.slideNext()
    }
};

// 处理商品项点击事件
const handleItemClick = (item: MerryGoRoundItem) => {
    if (item.link) {
        // 如果是外部链接，直接跳转
        if (item.link.startsWith('http://') || item.link.startsWith('https://')) {
            window.open(item.link, '_blank');
        } else {
            // 如果是内部路由，使用 navigateTo
            navigateTo(item.link);
        }
    }
};

// 手动初始化方法
const manualInitSwiper = () => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // 销毁现有实例
  if (swiperInstance.value) {
    try {
      swiperInstance.value.destroy(true, true);
    } catch (error) {
      console.warn('⚠️ 销毁实例时出错:', error);
    }
    swiperInstance.value = null;
    isInitialized.value = false;
  }
  
  // 强制重新渲染
  swiperKey.value++;
};

const isBeginning = ref(true)
const isEnd = ref(false)
if (Array.isArray(props.dataList) && props.dataList.length <= 4) {
   isEnd.value = true;
}

// 组件挂载
onMounted(() => {
  // 只在客户端执行
  if (!import.meta.client) return;
  // 确保 DOM 完全渲染
  nextTick(() => {
    if (!isInitialized.value) {
      setTimeout(() => {
        manualInitSwiper();
      }, 100);
    }
  });
});
</script>

<style scoped lang="scss">
.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #444;

    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.MerryGoRound_page {
    width: 100%;
    height: 37.5rem;

    .MerryGoRound_content {
        margin: 0 auto;
        width: 115.5rem;
        height: 31.25rem;
        // background-color: #475669;

        .Carousel_arrow {
            float: right;
            margin-top: 1.875rem;
            width: 3.125rem;
            height: 1.875rem;
            display: flex;
            justify-content: space-between;
            .disabled{
                cursor: not-allowed;
                opacity: 0.5;
            }
        }

        // background-color: blueviolet;
        .el-carousel__item h3 {
            color: #475669;
            opacity: 0.75;
            line-height: 23.75rem;
            margin: 0;
            text-align: center;
        }

        .el-carousel__item:nth-child(2n) {
            background-color: #99a9bf;
        }

        .el-carousel__item:nth-child(2n + 1) {
            background-color: #d3dce6;
        }

        .MerryGoRound_goods {
            width: 27.375rem;
            height: 100%;
            border-radius: .625rem;
            // box-shadow: 0 0 .625rem #EFEFEF;
            background-color: #EFEFEF;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .MerryGoRound_title {
                height: 3rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 1.5rem;
                letter-spacing: .125rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: center;
                line-height: 3rem;
                margin: 1.5625rem 0 0 0;
                overflow: hidden;
            }

            .MerryGoRound_photo {
                width: 18.125rem;
                margin: 1.25rem auto;
            }
        }

        // .MerryGoRound_goods:hover {
        //     background-color: white;
        // }
    }
}
</style>
import en from './locales/en.json'
import cn from './locales/cn.json'
import it from './locales/it.json'
import de from './locales/de.json'
import fr from './locales/fr.json'
import gr from './locales/gr.json'

export default defineI18nConfig(() => {
    return {
        legacy: false,
        locale: it, // 与 nuxt.config.ts 保持一致
        messages: {
            en: en,
            cn: cn,
            it: it,
            de: de,
            fr: fr,
            gr: gr
        }
    }
})
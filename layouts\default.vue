<template>
  <NuxtApp>
    <div class="layout">
      <div class="shipping">
        <span>{{ headerOfferData.title }}
           (*<NuxtLink
            to="/shipping-details"
            class="details"
            >{{headerOfferData.redirection_title}}</NuxtLink
          >)
        </span>
      </div>
      <nav class="nav">
        <div class="container">
          <div class="select-box">
            <MLKLanguageSelector :languagelist="languageList" />
          </div>
          <div class="logo-box">
            <MLKLogo
              style="height: 3.17rem;
                    width: 4.76rem;"/>
          </div>
          <div class="nav-search">
            <div class="search-container">
              <i class="fas fa-search search-icon" />
              <input
                v-model="searchValue"
                type="text"
                :placeholder="$t('header.search')"
                class="search-input"
                @keyup.enter="SearchSubmit"
              >
            </div>
            <button @click="goToWishlist">
              <el-badge :value="wishlistTotalItems" :show-zero="false" class="item">
                <img
                  :src="useAssetsImage('images/heart.svg')"
                  :alt="$t('header.wishlist')"
                  class="nav-icon"
                >
              </el-badge>
            </button>
            <button class="cart-button" @click="goToCart">
              <el-badge :value="cartTotalItems" :show-zero="false" class="item">
                <img
                  :src="useAssetsImage('images/shopping-cart.svg')"
                  :alt="$t('header.cart')"
                  class="nav-icon"
                >
              </el-badge>
            </button>
            <button @click="handleUserClick">
              <img :src="useAssetsImage('images/user.svg')" :alt="$t('header.user')" class="nav-icon" >
            </button>
          </div>
        </div>
        <div v-if="!userStore.isWholesale" class="username"  @click="navigateTo('/login')">
            {{ $t('header.wholesale') }} >
          </div>
      </nav>
      <div class="navbar">
        <div class="navbar-list">
          <div v-for="(item) in navData" :key="item.name" class="navbar-item" @mouseenter="onMouseBar(item.position)" @mouseleave="onMouseDown">
          <a :href="'/category?category_ids=' + (Array.isArray(item.category_ids) ? item.category_ids.join(',') : item.category_ids)">{{ item.name }}</a> 
          </div>
        </div>
        <div v-if="isShowbarContent" class="bar-content">
          <div class="bar-content-list" @mouseenter="onMouseChildrenBar" @mouseleave="onMouseDown">
            <div v-for="(item) in barProductData?.children" :key="item.name" class="product-box">
              <div class="product-model">
                <a :href="'/category?category_ids=' + (Array.isArray(item.category_ids) ? item.category_ids.join(',') : item.category_ids)">{{ item.name }}</a> 
              </div>
              <div v-for="item1 in item.children" :key="item1.name" class="product">
                <a :href="'/category?category_ids=' + (Array.isArray(item1.category_ids) ? item1.category_ids.join(',') : item1.category_ids)">{{ item1.name }}</a> 
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <slot/>
    <MLKFooter v-if="hideFooter" :footerdata="footerData"/>
 </NuxtApp>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useUserStore } from '~/stores/user'
import { useWishlistStore } from '~/stores/wishlist'

import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";
import { useLanguageDataRefresh } from "~/composables/useLanguageDataRefresh";

// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言store
const languageStore = useLanguageStore()

// 使用语言数据刷新composable
const { navData, languageList, headerOfferData, footerData, fetchData } = useLanguageDataRefresh()

// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
}, { immediate: true })

// 使用 useAssets composable
const { useAssetsImage } = useAssets()

// 购物车store
const cartStore = useShoppingCartStore()
// 用户store
const userStore = useUserStore()
// 心愿单store
const wishlistStore = useWishlistStore()

interface navDataType {
  name: string,
  link: string,
  logo_path: string,
  position: number,
  category_ids: string,
  children: Array<navDataType>
}



const barProductData = ref<navDataType>()
const isShowbarContent = ref(false);

// 初始化获取数据
await fetchData()

// 购物车总数量
const cartTotalItems = computed(() => cartStore.totalItems)
// 心愿单总数量
const wishlistTotalItems = computed(() => wishlistStore.totalItems)

// 处理用户点击事件
const handleUserClick = () => {
  if (!userStore.isLoggedIn) {
    // 跳转到登录页面
    navigateTo('/login')
  } else {
    navigateTo('/my-info/subpage')
  }
} 
const searchValue = ref('')
const SearchSubmit = () => {
  const router = useRouter()
  if (searchValue.value) {
    router.push(`/search?query=${searchValue.value}`)
  } else {
    router.push('/search')
  }
}

const goToCart = () => {
  const router = useRouter()
  const isWholesaleStatus = userStore.isWholesaleUser()
  if (isWholesaleStatus) {
    router.push('/shopping-wholesale-cart')
  } else {
    router.push('/shopping-retail-cart')
  }
}

const goToWishlist = () => {
  const router = useRouter()
  router.push('/my-info/subpage/wishlist')
}

const onMouseBar = (id: number) => {
  barProductData.value = navData.value.find((item: { position: number; }) => item?.position == id)
  isShowbarContent.value = true
}
const onMouseChildrenBar = () => {
  isShowbarContent.value = true
}
const onMouseDown = () => {
  isShowbarContent.value = false
}

const hideFooter = ref(true)
// 判断当前路由是否是my-info，是则隐藏MLKfooter组件，否则不变
onMounted(async () => {
  const route = useRoute()
  if (route.path.includes('/my-info')) {
    hideFooter.value = false
  } else {
    hideFooter.value = true
  }
  
  // 初始化用户状态
  userStore.initializeTokenState()
  
  // 获取购物车数据
  await cartStore.fetchCartData()
})


</script>

<style lang="scss" scoped>
.layout {
  .shipping {
    padding: 0.92rem 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    border-bottom: 1px solid #F5F5F5;
    span {
      font-size: 13px;
      text-align: center;

      .details {
        font-weight: bold;
        color: #ffba60;
        cursor: pointer;
      }
    }
  }
  .nav{
    width: 100%;
    height: 5.625rem;
    position: relative;
  }
  .username {
    height: 100%;
    line-height: 5.625rem;
    font-size: .875rem;
    margin-left: 9.375rem;
    cursor: pointer;
    transition: color 0.3s ease;
    position: absolute;
    top: 0;
    right: 1.5rem;
    
  }

  .container {
    width: 82.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 5.625rem;
    margin: 0 auto;
    box-sizing: border-box;
    border-bottom: 2px solid #F5F5F5;

    .select-box {
      display: flex;
      align-items: center;
    }

    .logo-box {
      margin-left: 14.375rem;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-search {
      display: flex;
      align-items: center;
      gap: .5rem;

      button {
        background: transparent;
        border: none;
        padding: .5rem;
        cursor: pointer;
        position: relative;

        .nav-icon {
          width: 24px;
          height: 24px;
        }
      }
      
      .cart-button {
        .cart-badge {
          position: absolute;
          top: 0;
          right: 0;
          background: #ff4757;
          color: white;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          min-width: 18px;
          line-height: 1;
        }
      }

      .search-container {
        width: 11.5625rem;
        position: relative;
        display: flex;
        align-items: center;
        border-radius: 2.02rem;
        background: #f5f5f5;
        padding: 1.02rem 1.33rem;

        .search-input {
          outline: none;
          width: 100%;
          font-size: 1.17rem;
          color: #656565;
          border: none;
          background: #f5f5f5;
        }

        .fas {
          transition: transform 0.3s ease;
        }

        .search-icon {
          position: static;
          color: #656565;
          font-size: 1.17rem;
        }
      }
    }

   
  }
  
  .navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    .navbar-list{
      display: flex;
      align-items: center;
      height: 3rem;
      line-height: 3rem;
      margin: 0 auto;
      .navbar-item {
        padding: 0 2.5rem;
        box-sizing: border-box;
        position: relative;
        border-right: 1px solid #f5f5f5;
        cursor: pointer;
        &:last-child{
          border-right: none;
        }

        a {
          color: #999;

          &:hover {
            color: #000;
          }

          &.active-bar {
            color: #000;
          }
        }

        &:last-child {
          border-right: node;
        }

      }
    }
    .bar-content {
      background: #fff;
      position: absolute;
      top: 3rem;
      z-index: 10;
      width: 100%;
      .bar-content-list{
        margin: auto;
        width: 56.25rem;
        display: flex;
        align-items: center;
      }
      .product-box {
        padding: 1.25rem 0;
        .product-model {
          cursor: pointer;
          font-size: 1rem;
          padding: 0 2.5rem;
          a {
            padding: .625rem 0;
            color: #000;
          }

        }

        .product {
          padding: .625rem 2.5rem;
          font-size: .875rem;

          a {
            color: #000;
            padding: .625rem 0;
          }
        }
      }
    }
  }

  
}
</style>

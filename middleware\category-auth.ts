export default defineNuxtRouteMiddleware((to) => {
  // 只对分类页面进行拦截
  if (to.path.includes('/category')) {
    const { category_id, category_ids } = to.query
    
    // 检查是否有分类ID参数
    if (!category_id && !category_ids) {
      // 如果没有分类ID，重定向到首页并显示提示
      console.warn('分类页面缺少必要的分类ID参数，重定向到首页')
      return navigateTo('/home')
    }
    
    // 如果有category_ids参数，检查是否为空
    if (category_ids) {
      const categoryIdsStr = category_ids as string
      if (!categoryIdsStr || categoryIdsStr.trim() === '') {
        console.warn('category_ids参数为空，重定向到首页')
        return navigateTo('/home')
      }
      
      // 检查category_ids是否为有效的数字或数字组合
      const categoryIdsArray = categoryIdsStr.split(',').map(id => id.trim()).filter(id => id !== '')
      if (categoryIdsArray.length === 0) {
        console.warn('category_ids参数解析后为空，重定向到首页')
        return navigateTo('/home')
      }
      
      // 验证每个ID是否为有效数字
      for (const id of categoryIdsArray) {
        if (isNaN(Number(id)) || Number(id) <= 0) {
          console.warn(`无效的分类ID: ${id}，重定向到首页`)
          return navigateTo('/home')
        }
      }
    }
    
    // 如果有category_id参数，检查是否为空
    if (category_id) {
      const categoryIdStr = category_id as string
      if (!categoryIdStr || categoryIdStr.trim() === '') {
        console.warn('category_id参数为空，重定向到首页')
        return navigateTo('/home')
      }
      
      // 验证category_id是否为有效数字
      if (isNaN(Number(categoryIdStr)) || Number(categoryIdStr) <= 0) {
        console.warn(`无效的分类ID: ${categoryIdStr}，重定向到首页`)
        return navigateTo('/home')
      }
    }
  }
}) 
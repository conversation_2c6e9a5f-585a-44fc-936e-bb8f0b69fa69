<template>
  <div class="news">
    <MLKTechnical/>
    <!-- 标签列表加载状态 -->
    <div v-if="tagsPending" class="loading">
      <el-button type="primary" loading color="#000" style="width: 5.375rem;height: 1.5625rem;">
        <template #loading>
          <div class="custom-loading">
            <svg class="circular" viewBox="-10, -10, 50, 50">
              <path
                class="path"
                d="
                M 30 15
                L 28 17
                M 25.61 25.61
                A 15 15, 0, 0, 1, 15 30
                A 15 15, 0, 1, 1, 27.99 7.5
                L 15 15
              "
                style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"
              />
            </svg>
          </div>
        </template>
        {{ t('news.loadingTags') }}
      </el-button>
    </div>
    
    <!-- 标签列表错误状态 -->
    <div v-else-if="tagsError" class="error-message">
      <p>{{ t('news.loadTagsFailed') }}</p>
    </div>
    
    <!-- 标签列表 -->
    <div v-else class="btn-list">
      <div v-for="item,index in btnList" :key="index" :class="['btn',item.id==checkBtnId?'active':'']" @click="checkBtnId=item.id;getNewsList()">
        {{ item.name }}
      </div>
    </div>
    

    <div class="new-list">
      <MLKNewsNew v-for="item in newsList" :key="item.id" :news="item" @click="goNewsDetail(item.url_key)"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getNews,getNewsTags } from '~/api/news'
import type { NewsItem } from '~/api/news'

const { t } = useI18n()

const btnList = ref<{id: number, name: string}[]>([])
const checkBtnId = ref(0)
const newsList = ref<NewsItem[]>([])
const _pending = ref(false)
const _error = ref(false)
const tagsPending = ref(false)
const tagsError = ref(false)

// 引入语言事件总线
const { onLanguageChange } = useLanguageEventBus()

const getNewsTagsList = async () => {
  tagsPending.value = true
  tagsError.value = false
  try {
    const res = await getNewsTags()
    if (res && res.success) {
      btnList.value = res.data?.tags || []
      if (btnList.value.length > 0) {
        checkBtnId.value = btnList.value[0].id
      }
    } else {
      tagsError.value = true
      console.error($t('news.getTagsFailed') + ':', res?.message || $t('news.unknownError'))
    }
  } catch (error) {
    console.error($t('news.getTagsFailed') + ':', error)
    tagsError.value = true
  } finally {
    tagsPending.value = false
  }
}

// 使用封装的 getNews 接口
const getNewsList = async () => {
  _pending.value = true
  _error.value = false
  try {
    const params = {
      tag_id: checkBtnId.value,
      page: 1,
      limit: 12
    }
    const res = await getNews(params)
    if (res && res.success) {
      newsList.value = res.data?.news || []
    } else {
      _error.value = true
      console.error($t('news.getNewsFailed') + ':', res?.message || $t('news.unknownError'))
    }
  } catch (error) {
    console.error($t('news.getNewsFailed') + ':', error)
    _error.value = true
  } finally {
    _pending.value = false
  }
}

// 语种变化时重新获取数据
const handleLanguageChange = async () => {
  // 重置状态
  btnList.value = []
  checkBtnId.value = 0
  newsList.value = []
  tagsError.value = false
  _error.value = false
  
  // 重新获取标签列表
  await getNewsTagsList()
  
  // 只有在成功获取到标签列表后才获取新闻列表
  if (btnList.value.length > 0) {
    await getNewsList()
  }
}

// 监听语种变化
onLanguageChange(handleLanguageChange)

// 跳转到新闻详情页面
const goNewsDetail = (url_key: string) => {
  navigateTo(`/news/detail/${url_key}.html`)
}

onMounted(async () => {
  if(import.meta.client){
    await nextTick()
  }

  // 初始化数据
  await getNewsTagsList()
  // 只有在成功获取到标签列表后才获取新闻列表
  if (btnList.value.length > 0) {
    await getNewsList()
  }
})

// 组件卸载时清理监听器
onUnmounted(() => {
  // 注意：useLanguageEventBus 会自动管理监听器，这里不需要手动清理
})

</script>

<style scoped lang="scss">
.news{
  width: 100%;
  .btn-list{
    margin: 4.8125rem auto;
    width: 76.9375rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn{
      cursor: pointer;
      width: 17.4375rem;
      height: 3.625rem;
      line-height: 3.625rem;
      background: #000;
      border-radius: .5rem;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: 500;
      font-size: 1.125rem;
      color: #fff;
      text-align: center;
      font-style: normal;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      &.active{
        background: #FFBA60;
        color: #3D3D3D;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 186, 96, 0.3);
        font-weight: 600;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
          pointer-events: none;
        }
      }
    }

  }
  .new-list{
    margin: 4.0625rem auto;
    width: 76.9375rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0 2.8125rem;
  }
  .loading{
    width: 76.9375rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5.6875rem;
  }
  
  .error-message{
    width: 76.9375rem;
    margin: 4.0625rem auto;
    text-align: center;
    p{
      margin-bottom: 1rem;
      color: #666;
      font-size: 1rem;
    }
  }
  
  .no-data{
    width: 100%;
    text-align: center;
    padding: 2rem 0;
    color: #999;
    font-size: 1rem;
  }
}
</style>
<template>
    <div class="Color_page">
        <div class="round" :style="{ 'background-color': color }"/>
        <div class="Color_name">{{ name }}</div>
        <div class="Color_amount">{{ '$'+amount*count}}</div>
        <div class="Color_number">
          <div style="cursor: pointer;" @click="decrement()">
              <el-icon :size="15"><Minus /></el-icon>
          </div>
          <input v-model="count" type="text">
          <div style="cursor: pointer;" @click="increment()">
              <el-icon :size="15"><Plus /></el-icon>
          </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
defineProps({
    // 颜色
    color: {
        type: String,
        default: 'red'
    },
    // 名字
    name: {
        type: String,
        default: 'Red'
    },
    // 金额
    amount: {
        type: Number,
        default: 0
    }
})

const count = ref(1)

function decrement() {
  if (count.value > 0) count.value--
}
function increment() {
  count.value++
}

watch(count, (newVal) => {
  if (newVal < 0) count.value = 0
})

</script>

<style lang="scss" scoped>
.Color_page {
    width: 15.0625rem;
    height: 3rem;
    border-radius: 8px;
    border: .0625rem solid #3D3D3D;
    display: flex;
    cursor: pointer;

    .round {
        margin: auto 0 auto .625rem;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
    }

    .Color_name {
        margin: auto 0 auto .625rem;
        width: 2.9375rem;
        font-family: AppleSystemUIFont;
        font-size: 1.125rem;
        color: #0C0C0C;
        line-height: 1.3125rem;
        text-align: left;
        font-style: normal;
    }

    .Color_amount {
        width: 2.9375rem;
        font-family: AppleSystemUIFont;
        font-size: 1.25rem;
        color: #000000;
        line-height: 3rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }

    .Color_number {
        width: 3.125rem;
        display: flex;
        align-items: center;
        margin-left: 2.1875rem;
        div {
            // margin: auto ;
            width: 1.875rem;
            height: 1.875rem;
            line-height: 1.625rem;
            // font-family: AppleSystemUIFont;
            font-size:1.5625rem;
            color: #6F6F6F;
            font-style: normal;
            text-transform: none;
            text-align: center;
        }

        input {
            width: 1.25rem;
            height: 1.25rem;
            line-height: 3rem;
            font-family: AppleSystemUIFont;
            font-size: 1.125rem;
            color: #6F6F6F;
            font-style: normal;
            text-transform: none;
            text-align: center;
            border: .0625rem solid white;
        }
    }

}
</style>
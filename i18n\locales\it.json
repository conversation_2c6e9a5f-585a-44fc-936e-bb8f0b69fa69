{"hello": "Ciao", "test": {"title": "Pagina di Test i18n", "description": "Questa pagina è utilizzata per testare la funzionalità di internazionalizzazione", "languageSwitcher": "<PERSON><PERSON><PERSON>", "basicTranslations": "Traduzioni Base", "currentInfo": "Informazioni Correnti", "urlInfo": "Informazioni URL", "currentLocale": "Locale Corrente", "defaultLocale": "Locale Predefinito", "availableLocales": "Locali Disponibili", "currentUrl": "URL Corrente", "baseUrl": "URL Base", "footer": "Questa è una pagina di test per la funzionalità i18n", "pageTitle": "Pagina di Test i18n", "hello": "Ciao", "welcome": "<PERSON><PERSON><PERSON>", "goodbye": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "retailCart": {"selectAll": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Elimina", "itemsTotal": "Articoli {count} totali", "orderSummary": "Riepilogo Ordine", "subtotal": "Subtotale", "saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tax": "Tassa", "shipping": "Spedizione", "total": "Totale", "freeShipping": "Spedizione gratuita oltre US$ 79.00", "promotionalCode": "Codice Promozionale", "enterCouponCode": "Inserisci codice coupon", "selectMyCoupons": "Seleziona i Miei Coupon", "checkoutNow": "PROCEDI ALL'ACQUISTO", "estimateShipping": "Stima Spedizione", "shippingWeight": "Peso Spedizione: {weight} kg", "weAccept": "Accettiamo", "pleaseEnterCouponCode": "Inserisci il codice coupon", "couponAppliedSuccessfully": "Coupon applicato con successo", "couponApplicationFailed": "Applicazione coupon fallita", "cartIsEmpty": "<PERSON><PERSON> vuoto, impossibile procedere all'acquisto", "pleaseLoginFirst": "Effettua prima l'accesso", "removeConfirmText": "Sei sicuro di voler eliminare tutti gli articoli selezionati?", "removeConfirm": "Conferma Eliminazione", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "wholesaleCart": {"selectAll": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Elimina", "itemsTotal": "Articoli {count} totali", "orderSummary": "Riepilogo Ordine", "subtotal": "Subtotale", "saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Totale", "freeShipping": "Spedizione gratuita oltre US$ 79.00", "promotionalCode": "Codice Promozionale", "enterCouponCode": "Inserisci codice coupon", "selectMyCoupons": "Seleziona i Miei Coupon", "checkoutNow": "PROCEDI ALL'ACQUISTO", "estimateShipping": "Stima Spedizione", "shippingWeight": "Peso Spedizione: {weight} kg", "weAccept": "Accettiamo", "pleaseEnterCouponCode": "Inserisci il codice coupon", "couponAppliedSuccessfully": "Coupon applicato con successo", "couponApplicationFailed": "Applicazione coupon fallita", "cartIsEmpty": "<PERSON><PERSON> vuoto, impossibile procedere all'acquisto", "pleaseLoginFirst": "Effettua prima l'accesso", "searchProducts": "Cerca prodotti...", "noSearchResults": "<PERSON><PERSON><PERSON> prodotto trovato", "searchTip": "Prova parole chiave diverse o controlla l'ortografia", "emptyCart": "Il carrello è vuoto", "goShopping": "Vai a fare shopping"}, "header": {"search": "Cerca", "wishlist": "Lista dei Desideri", "wholesale": "INGROSSO", "cart": "<PERSON><PERSON>", "user": "Utente", "selectLanguage": "Seleziona"}, "personalCenter": {"secureCheckout": "CHECKOUT SICURO", "user": "Utente", "continueShopping": "CONTINUA L'ACQUISTO"}, "home": {"findDevice": "TROVA IL TUO DISPOSITIVO", "selectBrand": "Seleziona marca", "selectDevice": "Seleziona dispositivo", "shopByBrand": "ACQUISTA PER MARCA", "inspirationText": "Entra nella nostra zona di ispirazione e senti il battito della vita bella.", "help": "<PERSON><PERSON>"}, "productCard": {"addToCart": "Aggiungi al Carrello", "inCart": "<PERSON><PERSON>", "selectColor": "Seleziona colore", "wholesaleLogin": "wholesale:", "login": "Accedi", "toView": "per visual<PERSON><PERSON>e", "removeFromWishlist": "Rimuovi dalla Lista dei Desideri", "addToWishlist": "Aggiungi alla Lista dei Desideri", "wishlistAdded": "Prodotto aggiunto alla lista dei desideri", "wishlistRemoved": "<PERSON><PERSON><PERSON> dalla lista dei desideri", "wishlistAddFailed": "Impossibile aggiungere alla lista dei desideri", "wishlistRemoveFailed": "Impossibile rimuovere dalla lista dei desideri", "wishlistError": "Errore durante l'operazione della lista dei desideri", "selectColorFirst": "Seleziona prima il colore del prodotto", "new": "NUOVO"}, "newArrival": {"filter": "Filtro", "brand": "<PERSON><PERSON>", "device": "Dispositivo", "allBrands": "<PERSON><PERSON> le March<PERSON>", "allDevices": "Tutti i Dispositivi", "loading": "Caricamento...", "loadingNewProducts": "Caricamento nuovi prodotti...", "noNewProducts": "Nessun nuovo prodotto", "noNewProductsDesc": "<PERSON><PERSON><PERSON><PERSON>, non ci sono nuovi prodotti disponibili al momento", "addToCartSuccess": "Il prodotto è stato aggiunto al carrello.", "addToCartFailed": "Impossibile aggiungere", "addToCartError": "Impossibile aggiungere al carrello"}, "swiper": {"shopNow": "ACQUISTA ORA"}, "shoppingCartRetail": {"emptyCart": "Il carrello è vuoto", "goShopping": "Vai a fare shopping", "freeShipping": "SPEDIZIONE GRATUITA", "color": "Colore", "weight": "Peso", "subtotal": "Subtotale", "subtotalWeight": "Peso Subtotale", "delete": "Elimina", "brand": "<PERSON><PERSON>", "device": "Dispositivo", "price": "Prezzo", "quantity": "Quantità", "minus": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "updateQuantity": "Aggiorna Quantità", "quantityError": "Errore Quantità", "removeConfirm": "Conferma Eliminazione", "removeConfirmText": "Sei sicuro di voler eliminare questo articolo?", "removeSuccess": "Rimozione Riuscita", "removeFailed": "Rimozione Fallita", "updateSuccess": "Aggiornamento Riuscito", "updateFailed": "Aggiornamento Fallito", "brandEmpty": "La marca è vuota", "deviceEmpty": "Il dispositivo è vuoto", "colorEmpty": "Il colore è vuoto"}, "wishlist": {"loginConfirmTitle": "Avviso", "loginConfirmMessage": "Non hai effettuato l'accesso. Vuoi andare alla pagina di login?", "confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "pleaseLoginFirst": "Effettua prima l'accesso", "addSuccess": "Aggiunto con successo alla lista dei desideri", "addFailed": "Impossibile aggiungere alla lista dei desideri", "removeSuccess": "<PERSON><PERSON><PERSON> con <PERSON>o", "removeFailed": "Impossibile rimuovere", "moveToCartSuccess": "Spostato con successo nel carrello", "moveToCartFailed": "Impossibile spostare nel carrello", "clearSuccess": "Lista dei desideri svuotata con successo", "clearFailed": "Impossibile svuotare la lista dei desideri", "fetchDataFailed": "Impossibile recuperare i dati della lista dei desideri"}, "wishlistPage": {"title": "La Mia Lista dei Desideri", "emptyTitle": "La tua lista dei desideri è vuota", "emptyDescription": "Nessun articolo è stato ancora aggiunto alla tua lista dei desideri", "goShopping": "Vai a fare Shopping", "selectAll": "<PERSON><PERSON><PERSON><PERSON>", "addToCart": "Aggiungi al Carrello", "viewDetails": "Visualizza Dettagli", "inStock": "Disponibile", "outOfStock": "<PERSON><PERSON><PERSON>", "save": "Risparmia", "addSelectedToCart": "Aggiungi Selezionati al Carrello", "removeSelected": "Rimuovi Selezionati", "removeConfirmTitle": "Conferma Rimozione", "removeConfirmMessage": "Sei sicuro di voler rimuovere gli {count} articoli selezionati dalla tua lista dei desideri?", "removeSuccess": "Articolo rimosso dalla lista dei desideri", "removeFailed": "Rimozione fallita", "addToCartSuccess": "Aggiunti con successo {count} articoli al carrello", "addToCartFailed": "Impossibile aggiungere al carrello", "batchAddToCartError": "Errore durante l'aggiunta in blocco al carrello", "batchRemoveError": "Errore durante la rimozione in blocco", "removeSelectedSuccess": "<PERSON><PERSON><PERSON> con successo {count} articoli", "addToCartTitle": "Aggiungi al Carrello", "selectVariant": "Seleziona una variante", "selectColor": "Seleziona un colore", "selectBrand": "Seleziona una marca", "selectDevice": "Seleziona un dispositivo", "quantity": "Quantità", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "delete": "Elimina"}, "product": {"addToCart": "Aggiungi al Carrello", "removeFromWishlist": "Rimuovi dalla Lista dei Desideri", "addToWishlist": "Aggiungi alla Lista dei Desideri", "addToCartSuccess": "<PERSON>dotto aggiunto al carrello", "addToCartFailed": "Impossibile aggiungere al carrello", "addToCartError": "Errore durante l'aggiunta al carrello", "invalidProduct": "Dati prodotto non validi", "wishlistToggleError": "Errore durante l'operazione della lista dei desideri", "shoppingCart": "<PERSON><PERSON>"}, "productDetails": {"loading": "Caricamento...", "productNotFound": "Prodotto non trovato o impossibile caricare", "retry": "<PERSON><PERSON><PERSON><PERSON>", "networkError": "Errore di connessione di rete, controlla la tua rete e riprova", "description": "Descrizione", "specification": "Specifiche", "dimensions": "Dimensioni", "profile": "<PERSON>ilo", "dropRating": "Classificazione di Caduta", "device": "Dispositivo", "selectBrand": "Seleziona marca", "selectDevice": "Seleziona dispositivo", "color": "Colore", "quantity": "Quantità", "addToCart": "Aggiungi al Carrello", "alreadyInCart": "<PERSON><PERSON><PERSON> nel carrello ({count})", "buyNow": "Acquista ora", "checkoutSecurely": "CHECKOUT SICURO CON", "acceptedPayments": "<PERSON><PERSON><PERSON>", "interestFreePayments": "4 pagamenti senza interessi di $16.00 con <PERSON><PERSON><PERSON>", "learnMore": "Scopri di più", "shippingUpTo": "Spedizione fino al {percent}% di sconto", "pleaseSelectColor": "Seleziona prima il colore del prodotto", "productInCart": "Il prodotto è già nel carrello", "relatedProducts": "<PERSON><PERSON><PERSON> p<PERSON>erti anche", "brands": "<PERSON><PERSON>", "colors": "Colori", "mediaPreview": {"close": "<PERSON><PERSON>", "previous": "Precedente", "next": "Successivo"}}, "news": {"loadingTags": "Caricamento Tag", "loadTagsFailed": "Impossibile caricare i tag, aggiorna la pagina e riprova", "unknownError": "<PERSON><PERSON><PERSON> scon<PERSON>", "getTagsFailed": "Impossibile ottenere la lista dei tag", "getNewsFailed": "Impossibile ottenere la lista delle notizie", "detail": {"home": "Home", "news": "Notizie", "relatedArticles": "Articoli Correlati", "youMayAlsoLike": "<PERSON><PERSON><PERSON> p<PERSON>erti anche", "addToCartSuccess": "Il prodotto è stato aggiunto al carrello.", "addToCartFailed": "Impossibile aggiungere", "addToCartError": "Impossibile aggiungere al carrello"}}, "discover": {"showAll": "MOSTRA TUTTO", "more": "altro"}, "footer": {"newsletter": "NEWSLETTER", "stayUpdated": "Rimani aggiornato sulle nuove collezioni MLK+:", "emailPlaceholder": "E-mail", "subscribe": "ISCRIVITI", "companyName": "MLK+ OUSAND S.R.L.", "companyAddress": "<PERSON><PERSON>, 47 - 00197 Roma, ITALIA", "taxId": "P. I. 13085581000", "copyright": "Copyright © 2025 MLK+. Tutti i diritti riservati."}, "subscribe": {"title": "Unisciti alla Community MLK+", "description": "Iscriviti alla Newsletter", "policy": "Se sei un amante della tecnologia e del design, iscriviti alla nostra newsletter e rimani aggiornato sulle novità.", "emailPlaceholder": "Inserisci il tuo indirizzo email", "signUp": "ISCRIVITI", "emailValidation": "Inserisci un indirizzo email corretto"}, "search": {"home": "Home", "showHideFilters": "Mostra/Nascondi Filtri", "byRating": "Per valutazione", "hideAll": "Nascondi TUTTO", "showAll": "Mostra TUTTO", "addToCartSuccess": "Il prodotto è stato aggiunto al carrello.", "addToCartFailed": "Impossibile aggiungere", "addToCartError": "Impossibile aggiungere al carrello"}}
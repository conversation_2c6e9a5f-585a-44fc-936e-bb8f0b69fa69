<template>
  <div v-if="cartItemsWithCheck.length === 0" class="empty-cart">
    <div class="empty-message">
      <img src="~/assets/images/shopping-cart.svg" :alt="t('shoppingCartRetail.delete')" class="empty-icon">
      <p>{{ t('shoppingCartRetail.emptyCart') }}</p>
      <el-button style="background-color: #000000;" type="primary" @click="goShopping">{{ t('shoppingCartRetail.goShopping') }}</el-button>
    </div>
  </div>
  <el-checkbox-group v-else v-model="checkedItems" @change="handleCheckAllChange">
  <div class="shopping-item-box">
    <div v-for="item in cartItemsWithCheck" :key="item.cart_item_id" class="shopping-item">
      <div class="shopping-item-left">
        <el-checkbox
          :value="item.cart_item_id"
        />
        <div class="img">
          <img :src="item.product.base_image" :alt="item.product.name">
        </div>
        <div class="product">
          <div class="name">
            {{ item.product.name }}
          </div>
          <div class="free">
            <!-- {{ t('shoppingCartRetail.freeShipping') }} -->
          </div>
          <div v-if="item.attributes?.color?.option_label" class="color">
            {{ t('shoppingCartRetail.color') }}: {{ item.attributes?.brand?.option_label }}  {{ item.attributes?.device?.option_label }}  ({{ item.attributes?.color?.option_label }})
          </div>
          <div class="price">
            {{ item.formatted_price }} | {{ t('shoppingCartRetail.weight') }}: {{ item.weight }}kg
          </div>
        </div>
      </div>
      <div class="shopping-item-right">
        <div class="count-box">
          <div class="count-box-top">
            <div class="minus" @click="decreaseQuantity(item)">
              <el-icon :size="14" style="margin-top: .3125rem;"><Minus /></el-icon>
            </div>
            <input 
              v-model="item.quantity" 
              type="number" 
              min="1" 
              max="999"
              @change="updateQuantity(item)"
              @blur="validateQuantity(item)"
              @keyup.enter="updateQuantity(item)"
            >
            <div class="add" @click="increaseQuantity(item)">
              <el-icon :size="14" style="margin-top: .3125rem;"><Plus /></el-icon>
            </div>
          </div>
          <div class="subtotal-price">
            {{ t('shoppingCartRetail.subtotal') }}: {{ item.formatted_total }}
          </div>
          <div class="subtotal-weight">
            {{ t('shoppingCartRetail.subtotalWeight') }}: {{ (Number(item.weight) * item.quantity).toFixed(3) }}kg
          </div>
        </div>
        <div class="delect" @click="removeItem(item)">
          <img src="~/assets/images/shopping/delect.png" :alt="t('shoppingCartRetail.delete')">
        </div>
      </div>
    </div>
  </div>
  </el-checkbox-group>
</template>

<script setup lang="ts">
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { CheckboxValueType } from 'element-plus'

// i18n
const { t } = useI18n()

const props = defineProps({
  checkAll: {
    type: Boolean,
    default: false
  },
  cartItems: {
    type: Array as PropType<CartItemWithCheck[]>,
    default: () => []
  },
  cartData: {
    type: Object,
    default: null
  },
  checkedItemInfo: {
    type: Array as PropType<string[]>,
    default: () => []
  }
})
const checkedItems = ref<string[]>([])

// 监听 checkAll 属性的变化，控制 checkedItems 的全选/全不选
watch(
  () => props.checkedItemInfo,
  (newVal) => {
    console.warn('📄 购物车组件: 是否全选:', newVal)
    if (newVal) {
      // 全选时，将所有商品的 cart_item_id 加入 checkedItems
      checkedItems.value = newVal
    }
  }
)

const emit = defineEmits(['update-check', 'update-isIndeterminate', 'update-cart', 'update-checkedItems'])

// 监听 checkedItems 的变化
watch(
  checkedItems,
  (newVal: string[]) => {
    console.warn('📄 购物车组件: 数据选中状态', newVal)
    emit('update-checkedItems', newVal)
  }
)

const handleCheckAllChange = (val: CheckboxValueType[]) => {
  console.warn('📄 购物车页面: 数据选中状态', val)
  const checkedCount = val.length  
  emit('update-check', checkedCount === props.cartItems.length)
  emit('update-isIndeterminate', checkedCount > 0 && checkedCount < props.cartItems.length)
}

// 购物车store
const cartStore = useShoppingCartStore()

// 路由
const router = useRouter()

// 定义购物车商品类型
interface CartItemWithCheck {
  cart_item_id: string
  quantity: number
  sku: string
  type: string
  name: string
  price: number
  base_price: number
  total: number
  base_total: number
  tax_percent: number
  weight:string
  tax_amount: number
  base_tax_amount: number
  discount_percent: number
  discount_amount: number
  base_discount_amount: number
  product_id: number
  product: {
    product_id: number
    type: string
    name: string
    url_key: string
    base_image: string
  }
  attributes?: {
    color?: {
      option_label?: string
    }
    brand?: {
      option_label?: string
    }
    device?: {
      option_label?: string
    }
  }
  formatted_price: string
  formatted_base_price: string
  formatted_total: string
  formatted_base_total: string
}

// 计算属性：购物车商品列表（添加checked属性）
const cartItemsWithCheck = computed<CartItemWithCheck[]>(() => {
  const items = props.cartItems.length > 0 ? props.cartItems : cartStore.cartItems
  return items.map(item => ({
    ...item,
  }))
})


const increaseQuantity = async (item: CartItemWithCheck) => {
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, item.quantity + 1)
    if (result.success) {
      // ElMessage.success('数量更新成功')
      emit('update-cart')
    } else {
      ElMessage.error(result.message || '数量更新失败')
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error('更新数量时发生错误')
  }
}

const decreaseQuantity = async (item: CartItemWithCheck) => {
  if (item.quantity <= 1) {
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return
  }
  
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, item.quantity - 1)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.updateSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.updateFailed'))
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error(t('shoppingCartRetail.updateFailed'))
  }
}

const validateQuantity = (item: CartItemWithCheck) => {
  const newQuantity = parseInt(item.quantity.toString())
  if (isNaN(newQuantity) || newQuantity < 1) {
    item.quantity = 1
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return false
  }
  
  // 限制最大数量为999
  if (newQuantity > 999) {
    item.quantity = 999
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return false
  }
  
  return true
}

const updateQuantity = async (item: CartItemWithCheck) => {
  if (!validateQuantity(item)) {
    return
  }
  
  const newQuantity = parseInt(item.quantity.toString())
  
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, newQuantity)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.updateSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.updateFailed'))
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error(t('shoppingCartRetail.updateFailed'))
  }
}

const removeItem = async (item: CartItemWithCheck) => {
  try {
    await ElMessageBox.confirm(t('shoppingCartRetail.removeConfirmText'), t('shoppingCartRetail.removeConfirm'), {
      confirmButtonText: t('shoppingCartRetail.delete'),
      cancelButtonText: t('shoppingCartRetail.remove'),
      type: 'warning'
    })
    
    const result = await cartStore.removeFromCart(item.cart_item_id)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.removeSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.removeFailed'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除购物车商品失败:', error)
      ElMessage.error(t('shoppingCartRetail.removeFailed'))
    }
  }
}
// 批量删除购物车商品
const batchRemoveCart = async () => {
  const result = await cartStore.batchRemoveCart(checkedItems.value)
  if(result.success){
    ElMessage.success(result.message || t('shoppingCartRetail.removeSuccess'))
    // 清空选中状态
    checkedItems.value = []
    // 更新全选状态
    emit('update-check', false)
    // 更新半选状态
    emit('update-isIndeterminate', false)
    // 更新购物车数据
    emit('update-cart')
  }else{
    ElMessage.error(result.message || t('shoppingCartRetail.removeFailed'))
  }
}
// 暴露批量删除购物车商品方法
defineExpose({
  batchRemoveCart
})

const goShopping = () => {
  router.push('/home')
}

// 组件挂载时获取购物车数据
onMounted(async () => {
  if (props.cartItems.length === 0) {
    await cartStore.fetchCartData()
  }
})
</script>

<style lang="scss" scoped>
.empty-cart {
  background: #FFFFFF;
  border-radius: .9375rem;
  padding: 3rem;
  text-align: center;
  
  .empty-message {
    .empty-icon {
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
    
    p {
      font-size: 1.125rem;
      color: #666;
      margin-bottom: 1.5rem;
    }
  }
}

.shopping-item-box{
    overflow-y: auto;
    max-height: 62.5rem;
  .shopping-item{
    padding: 0 2.1875rem 0 1.3125rem;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: .9375rem;
    border: 1px solid #EBEBEB;
    margin-bottom: 1rem;
    width: 53.125rem;
    height: 12.875rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .shopping-item-left{
      display: flex;
      align-items: center;
      
      .img{
        width: 8.25rem;
        height: 8.25rem;
        margin-left: 1.0625rem;
        
        img{
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 0.5rem;
        }
      }
      
      .product{
        height: 100%;
        margin-left: 1rem;
        
        .name{
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        
        .free{
          margin-top: .875rem;
          // height: .9375rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #FE8C5F;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        
        .color{
          margin-top: .625rem;
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        
        .size{
          margin-top: .625rem;
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        
        .price{
          margin-top: .625rem;
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: .875rem;
          color: #000000;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    
    .shopping-item-right{
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      justify-content: flex-end;
      
      .count-box{
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: flex-end;
        
        .count-box-top{
          width: 7.625rem;
          height: 2rem;
          border-radius: .25rem;
          border: 1px solid #D9D9D9;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          box-sizing: border-box;
          padding: 0 .5625rem;
          overflow: hidden;
          
          .minus, .add{
            width: 1.5rem;
            height: 1.5rem;
            line-height: 1.5rem;
            text-align: center;
            cursor: pointer;
            user-select: none;
            
            &:hover {
              background-color: #f5f5f5;
            }
          }
          
          input{
            // width: 2.5rem;
            padding: 0 0 0 .625rem;
            text-align: center;
            border: none;
            outline: none;
          }
        }
        
        .subtotal-price{
          margin-top: .625rem;
          height: 1.25rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .875rem;
          color: #000000;
          line-height: 1.25rem;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
        
        .subtotal-weight{
          margin-top: .625rem;
          height: 1.25rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: .75rem;
          color: #959595;
          line-height: 1.25rem;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
      
      .delect{
        margin-top: 1rem;
        cursor: pointer;
        transition: opacity 0.3s ease;
        
        &:hover {
          opacity: 0.7;
        }
        
        img {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
}
</style>
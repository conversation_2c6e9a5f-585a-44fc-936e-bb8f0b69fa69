<template>
  <div class="home-section">
    <h2 class="home-section-title">The Case ShowCase</h2>
    <div class="container">
      <div v-for="i in 4" :key="i" class="new-card">
        <img src="~/assets/images/product-placeholder.png" alt="Product">
        <div class="font-bold">Popular Products</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">

</script>
<style scoped>

.container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 14px;
}

.new-card {
  background: #EFEFEF;
  width: 158px;
  aspect-ratio: 23.33 / 30.33;
  border-radius: 5px;
  text-align: center;
}

.new-card img {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  border-radius: 15px;
  /* margin-bottom: 1rem; */
}

</style>
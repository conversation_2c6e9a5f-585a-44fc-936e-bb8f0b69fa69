import { useLanguageStore } from '~/stores/language'
import { useI18n } from 'vue-i18n'

export const useLanguageSwitch = () => {
  const languageStore = useLanguageStore()
  const { locale } = useI18n()

  // 语言代码映射表
  const languageMapping: Record<string, 'en' | 'cn' | 'it' | 'de' | 'fr' | 'gr'> = {
    'en': 'en',
    'zh': 'cn', // 中文
    'it': 'it', // 意大利语
    'de': 'de', // 德语
    'fr': 'fr', // 法语
    'gr': 'gr' // 希腊语
  }

  // 切换语言的方法
  const switchLanguage = (languageCode: string) => {
    // 更新 store 中的语言
    languageStore.setLanguage(languageCode)
    
    // 直接更新 i18n 语言（如果 store 中没有处理）
    try {
      const mappedLanguage = languageMapping[languageCode] || 'it'
      if (locale.value !== mappedLanguage) {
        locale.value = mappedLanguage
      }
    } catch (error) {
      console.error('🌐 语言切换失败:', error)
    }
  }

  // 获取当前语言
  const getCurrentLanguage = () => {
    return languageStore.language
  }

  // 获取当前 i18n 语言
  const getCurrentI18nLanguage = () => {
    return locale.value
  }

  // 检查语言是否支持
  const isLanguageSupported = (languageCode: string) => {
    return Object.prototype.hasOwnProperty.call(languageMapping, languageCode)
  }

  // 获取支持的语言列表
  const getSupportedLanguages = () => {
    return Object.keys(languageMapping)
  }

  // 初始化语言设置
  const initializeLanguage = () => {
    if (import.meta.client) {
      const currentLanguage = languageStore.language
      
      // 如果当前语言不是默认的 'en'，则触发语言切换
      if (currentLanguage !== 'it') {
        switchLanguage(currentLanguage)
      }
    }
  }

  return {
    switchLanguage,
    getCurrentLanguage,
    getCurrentI18nLanguage,
    isLanguageSupported,
    getSupportedLanguages,
    initializeLanguage,
    languageMapping
  }
} 
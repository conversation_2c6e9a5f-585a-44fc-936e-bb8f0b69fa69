<template>
  <div class="new">
    <div class="img">
      <img :src="news?.thumbnail" :alt="news?.title">
    </div>
    <div class="fgx"/>
    <div class="new-title" :title="news?.title">
      {{ news?.title }}
    </div>
    <div class="new-content" :title="news?.description">
      {{ news?.description }}
    </div>
    <div class="new-bottom">
      <nuxt-link :to="`/news/detail/${news?.id}`">Read More</nuxt-link>
    </div> 
  </div>
</template>

<script setup lang="ts">
import type { NewsItem } from '~/api/news'

// 定义props
interface Props {
  news?: NewsItem
}

defineProps<Props>()
</script>

<style scoped lang="scss">
.new{
  flex-shrink: 0;
  width: 17.125rem;
  height: 31.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  img{
    width: 17.125rem;
    height: 22.875rem;
    .img{
      width: 100%;
      height: 100%;
    }
  }
  .fgx{
    margin-top: 1.3125rem;
    width: 2.125rem;
    height: .375rem;
    background: #FFBA60;
  }
  .new-title{
    margin-top: .875rem;
    height: 1.5625rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 1.125rem;
    color: #000000;
    line-height: 1.5625rem;
    text-align: left;
    font-style: normal;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
  .new-content{
    width: 17.125rem;
    height: 2.375rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: .875rem;
    color: #919191;
    line-height: 1.1875rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: -webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:2;
    line-clamp:2;
    overflow:hidden;
  }
  .new-bottom{
    height: 1.3125rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: bold;
    font-size: .9375rem;
    color: #000000;
    line-height: 1.3125rem;
    text-align: left;
    font-style: normal;
    a{
      text-decoration: underline;
      color: #000;
    }
  }
}
</style>
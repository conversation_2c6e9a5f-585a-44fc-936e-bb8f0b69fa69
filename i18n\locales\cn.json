{"hello": "你好", "test": {"title": "i18n 测试页面", "description": "此页面用于测试国际化功能", "languageSwitcher": "语言切换器", "basicTranslations": "基础翻译", "currentInfo": "当前信息", "urlInfo": "URL 信息", "currentLocale": "当前语言", "defaultLocale": "默认语言", "availableLocales": "可用语言", "currentUrl": "当前 URL", "baseUrl": "基础 URL", "footer": "这是 i18n 功能的测试页面", "pageTitle": "i18n 测试页面", "hello": "你好", "welcome": "欢迎", "goodbye": "再见"}, "retailCart": {"selectAll": "全选", "delete": "删除", "itemsTotal": "共 {count} 件商品", "orderSummary": "订单摘要", "subtotal": "小计", "saved": "已节省", "tax": "税费", "shipping": "运费", "total": "总计", "freeShipping": "满 US$ 79.00 免运费", "promotionalCode": "优惠码", "enterCouponCode": "输入优惠码", "selectMyCoupons": "选择我的优惠券", "checkoutNow": "立即结账", "estimateShipping": "预估运费", "shippingWeight": "运输重量: {weight} kg", "weAccept": "我们接受", "pleaseEnterCouponCode": "请输入优惠码", "couponAppliedSuccessfully": "优惠券应用成功", "couponApplicationFailed": "优惠券应用失败", "cartIsEmpty": "购物车为空，无法结账", "pleaseLoginFirst": "请先登录", "removeConfirmText": "确定要删除所有选中的商品吗？", "removeConfirm": "确认删除", "remove": "移除"}, "wholesaleCart": {"selectAll": "全选", "delete": "删除", "itemsTotal": "共 {count} 件商品", "orderSummary": "订单摘要", "subtotal": "小计", "saved": "已节省", "total": "总计", "freeShipping": "满 US$ 79.00 免运费", "promotionalCode": "优惠码", "enterCouponCode": "输入优惠码", "selectMyCoupons": "选择我的优惠券", "checkoutNow": "立即结账", "estimateShipping": "预估运费", "shippingWeight": "运输重量: {weight} kg", "weAccept": "我们接受", "pleaseEnterCouponCode": "请输入优惠码", "couponAppliedSuccessfully": "优惠券应用成功", "couponApplicationFailed": "优惠券应用失败", "cartIsEmpty": "购物车为空，无法结账", "pleaseLoginFirst": "请先登录", "searchProducts": "搜索商品...", "noSearchResults": "未找到商品", "searchTip": "尝试不同的关键词或检查拼写", "emptyCart": "购物车为空", "goShopping": "去购物"}, "header": {"search": "搜索", "wishlist": "愿望清单", "wholesale": "批发", "cart": "购物车", "user": "用户", "selectLanguage": "选择"}, "personalCenter": {"secureCheckout": "安全结账", "user": "用户", "continueShopping": "继续购物"}, "home": {"findDevice": "查找您的设备", "selectBrand": "选择品牌", "selectDevice": "选择设备", "shopByBrand": "按品牌购物", "inspirationText": "走进我们的灵感区域，感受美好生活的脉动。", "help": "帮助"}, "productCard": {"addToCart": "加入购物车", "inCart": "已在购物车", "selectColor": "请选择颜色", "wholesaleLogin": "wholesale:", "login": "登录", "toView": "查看", "removeFromWishlist": "取消收藏", "addToWishlist": "收藏", "wishlistAdded": "商品已添加到心愿单", "wishlistRemoved": "已从心愿单移除", "wishlistAddFailed": "添加到心愿单失败", "wishlistRemoveFailed": "从心愿单移除失败", "wishlistError": "心愿单操作时发生错误", "selectColorFirst": "请先选择商品颜色", "new": "新品"}, "newArrival": {"filter": "筛选", "brand": "品牌", "device": "设备", "allBrands": "所有品牌", "allDevices": "所有设备", "loading": "加载中...", "loadingNewProducts": "正在加载新品...", "noNewProducts": "暂无新品", "noNewProductsDesc": "抱歉，暂时没有新品上架", "addToCartSuccess": "商品已添加到购物车", "addToCartFailed": "添加失败", "addToCartError": "添加购物车失败"}, "swiper": {"shopNow": "立即购买"}, "shoppingCartRetail": {"emptyCart": "购物车为空", "goShopping": "去购物", "freeShipping": "免费配送", "color": "颜色", "weight": "重量", "subtotal": "小计", "subtotalWeight": "小计重量", "delete": "删除", "brand": "品牌", "device": "设备", "price": "价格", "quantity": "数量", "minus": "减少", "add": "增加", "remove": "移除", "updateQuantity": "更新数量", "quantityError": "数量错误", "removeConfirm": "确认删除", "removeConfirmText": "确定要删除这个商品吗？", "removeSuccess": "删除成功", "removeFailed": "删除失败", "updateSuccess": "更新成功", "updateFailed": "更新失败", "brandEmpty": "品牌为空", "deviceEmpty": "设备为空", "colorEmpty": "颜色为空"}, "wishlist": {"loginConfirmTitle": "提示", "loginConfirmMessage": "您还未登录，是否跳转到登录页面？", "confirm": "确定", "cancel": "取消", "pleaseLoginFirst": "请先登录", "addSuccess": "添加到心愿单成功", "addFailed": "添加到心愿单失败", "removeSuccess": "移除成功", "removeFailed": "移除失败", "moveToCartSuccess": "移动到购物车成功", "moveToCartFailed": "移动到购物车失败", "clearSuccess": "清空心愿单成功", "clearFailed": "清空心愿单失败", "fetchDataFailed": "获取心愿单数据失败"}, "wishlistPage": {"title": "我的心愿单", "emptyTitle": "您的心愿单是空的", "emptyDescription": "还没有添加任何商品到心愿单", "goShopping": "去购物", "selectAll": "全选", "addToCart": "加入购物车", "viewDetails": "查看详情", "inStock": "有库存", "outOfStock": "缺货", "save": "节省", "addSelectedToCart": "将选中商品加入购物车", "removeSelected": "移除选中商品", "removeConfirmTitle": "确认移除", "removeConfirmMessage": "确定要从心愿单中移除选中的 {count} 个商品吗？", "removeSuccess": "商品已从心愿单移除", "removeFailed": "移除失败", "addToCartSuccess": "成功添加 {count} 个商品到购物车", "addToCartFailed": "添加购物车失败", "batchAddToCartError": "批量添加购物车时发生错误", "batchRemoveError": "批量移除时发生错误", "removeSelectedSuccess": "成功移除 {count} 个商品", "addToCartTitle": "添加到购物车", "selectVariant": "选择变体", "selectColor": "选择颜色", "selectBrand": "选择品牌", "selectDevice": "选择设备", "quantity": "数量", "cancel": "取消", "confirm": "确认", "delete": "删除"}, "product": {"addToCart": "加入购物车", "removeFromWishlist": "取消收藏", "addToWishlist": "收藏", "addToCartSuccess": "商品已添加到购物车", "addToCartFailed": "添加购物车失败", "addToCartError": "添加购物车时发生错误", "invalidProduct": "商品数据无效", "wishlistToggleError": "收藏操作时发生错误", "shoppingCart": "购物车"}, "productDetails": {"loading": "加载中...", "productNotFound": "产品不存在或加载失败", "retry": "重试", "networkError": "网络连接错误，请检查网络后重试", "description": "描述", "specification": "规格", "dimensions": "尺寸", "profile": "配置", "dropRating": "跌落等级", "device": "设备", "selectBrand": "选择品牌", "selectDevice": "选择设备", "color": "颜色", "quantity": "数量", "addToCart": "加入购物车", "alreadyInCart": "已在购物车中 ({count})", "buyNow": "立即购买", "checkoutSecurely": "安全结账", "acceptedPayments": "接受的支付方式", "interestFreePayments": "4期免息付款，每期 $16.00", "learnMore": "了解更多", "shippingUpTo": "运费最高可省 {percent}%", "pleaseSelectColor": "请先选择商品颜色", "productInCart": "商品已在购物车中", "relatedProducts": "您可能还喜欢", "brands": "品牌", "colors": "颜色", "mediaPreview": {"close": "关闭", "previous": "上一张", "next": "下一张"}}, "news": {"loadingTags": "加载标签中", "loadTagsFailed": "加载标签失败，请刷新页面重试", "unknownError": "未知错误", "getTagsFailed": "获取标签列表失败", "getNewsFailed": "获取新闻列表失败", "detail": {"home": "首页", "news": "新闻", "relatedArticles": "相关文章", "youMayAlsoLike": "您可能还喜欢", "addToCartSuccess": "商品已添加到购物车", "addToCartFailed": "添加购物车失败", "addToCartError": "添加购物车时发生错误"}}, "discover": {"showAll": "查看全部", "more": "更多"}, "footer": {"newsletter": "新闻订阅", "stayUpdated": "及时了解最新的MLK+系列产品：", "emailPlaceholder": "邮箱地址", "subscribe": "订阅", "companyName": "MLK+ OUSAND S.R.L.", "companyAddress": "<PERSON><PERSON>, 47 - 00197 Roma, ITALIA", "taxId": "P. I. 13085581000", "copyright": "版权所有 © 2025 MLK+。保留所有权利。"}, "subscribe": {"title": "加入 MLK+ 社区", "description": "订阅新闻通讯", "policy": "如果您热爱技术和设计，请注册我们的新闻通讯，及时了解最新资讯。", "emailPlaceholder": "请输入您的邮箱地址", "signUp": "注册", "emailValidation": "请输入正确的邮箱地址"}, "search": {"home": "首页", "showHideFilters": "显示/隐藏筛选器", "byRating": "按评分排序", "hideAll": "隐藏全部", "showAll": "显示全部", "addToCartSuccess": "商品已添加到购物车。", "addToCartFailed": "添加失败", "addToCartError": "添加购物车失败"}}
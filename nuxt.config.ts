// https://nuxt.com/docs/api/configuration/nuxt-config
// nuxt.config.ts
import path from 'path'
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  experimental: {
    writeEarlyHints: false
  },
  features:{
    inlineStyles:false
  },
  // 添加 SSR 配置
  ssr: true,
  nitro: {
    // 减少水合错误的配置
    experimental: {
      wasm: true
    }
  },
  runtimeConfig: {
    // apiSecret: '123',
    // public 命名空间中定义的，在服务器端和客户端都可以普遍访问
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://mlk.shiyus.com'
    }
  },
  devServer: {
    host: '127.0.0.1', // 允许外部访问
    port: 3000 // 自定义端口
  },
  css: [
    '@fortawesome/fontawesome-free/css/all.min.css',
    '~/assets/css/main.css',
    'element-plus/dist/index.css',
    'element-plus/theme-chalk/display.css',
    '~/assets/css/cartoon.css'
  ],
  // ui: {
  //   prefix: 'Nuxt',
  //   fonts: false,
  //   colorMode: false
  // },
  app: {
    head: {
      title: 'MLK+',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1,user-scalable=yes,shrink-to-fit=no' }
      ]
    }
  },
  modules: [
    '@nuxt/content',
    '@nuxt/eslint',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/scripts',
    '@nuxt/ui',
    '@element-plus/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/i18n',
  ],
  i18n: {
    defaultLocale: 'it',
    locales: [
      { code: 'en', name: 'English', file: 'en.json' },
      { code: 'cn', name: '中文', file: 'cn.json' },
      { code: 'it', name: 'Italiano', file: 'it.json' },
      { code: 'de', name: 'Deutsch', file: 'de.json' },
      { code: 'fr', name: 'Français', file: 'fr.json' },
      { code: 'gr', name: 'Ελληνικά', file: 'gr.json' }
    ],
    langDir:path.resolve(__dirname, 'i18n/locales') ,
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
    },
  }
})
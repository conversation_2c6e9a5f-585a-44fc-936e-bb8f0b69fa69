<template>
  <NuxtLayout>
    <div class="shopping-cart">
      <div class="content">
          <div class="left">
            <div class="search">
              <el-input
                v-model="searchValue"
                :placeholder="t('wholesaleCart.searchProducts')"
                style="width: 22.6875rem;height: 3rem;"
                clearable
                @input="handleSearch"
                @clear="handleClearSearch"
              >
                <template #suffix>
                  <el-icon class="el-input__icon" size="24"><search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="select-all">
              <div class="all-left">
                <el-checkbox
                  v-model="checkAll"
                  :disabled="filteredWholesaleCartItems.length === 0"
                  :indeterminate="isIndeterminate"
                  @change="handleCheckAllChange"
                >
                  {{ t('wholesaleCart.selectAll') }}
                </el-checkbox>
                <div class="detail" @click="batchRemoveCarts">
                  {{ t('wholesaleCart.delete') }}
                </div>
              </div>
              <div class="all-right">
                {{ t('wholesaleCart.itemsTotal', { count: allCheckedList.length }) }}
              </div>
            </div>
            <div v-if="filteredWholesaleCartItems.length === 0 && searchValue" class="no-results">
              <div class="no-results-content">
                <el-icon size="48" color="#999"><search /></el-icon>
                <p>{{ t('wholesaleCart.noSearchResults') }}</p>
                <p class="search-tip">{{ t('wholesaleCart.searchTip') }}</p>
              </div>
            </div>
            <div v-if="filteredWholesaleCartItems.length === 0 && !searchValue" class="empty-cart">
              <div class="empty-cart-content">
                <img src="~/assets/images/shopping-cart.svg" :alt="t('wholesaleCart.emptyCart')" class="empty-icon">
                <p>{{ t('wholesaleCart.emptyCart') }}</p>
                <el-button style="background-color: #000000;" type="primary" @click="goShopping">{{ t('wholesaleCart.goShopping') }}</el-button>
              </div>
            </div>
            <div v-if="filteredWholesaleCartItems.length > 0" style="overflow-y: auto;max-height: 62.5rem;">
              <MLKShoppingCartWholesale 
                v-for="(item, index) in filteredWholesaleCartItems" 
                :key="index" 
                :check-alls="checkAll" 
                :check-is-indeterminate="isIndeterminate"
                :item="item" 
                @update-cart="updateCart"
                @check-list-change="(val) => handleCheckListChange(val, index)"/>
            </div>
            <!-- <MLKShoppingCartWholesale/> -->
        </div>
      <div class="right">
        <div class="right-top">
          <div class="order">
            {{ t('wholesaleCart.orderSummary') }}
          </div>
          <div class="total-box">
            <div class="total-left">
              {{ t('wholesaleCart.subtotal') }}
            </div>
            <div class="total-right">
              US${{ Subtotal.toFixed(2) }}
            </div>
          </div>
          <div v-if="(cartData?.discount_amount || 0) > 0" class="saved">
            <div class="saved-left">
              {{ t('wholesaleCart.saved') }}
            </div>
            <div class="saved-right">
              --{{ cartData?.formatted_prices?.discount_amount || 'US$ 0.00' }}
            </div>
          </div>
          <div class="total-box">
            <div class="total-left">
              {{ t('wholesaleCart.total') }}
            </div>
            <div class="total-right">
              US${{ totalAmount.toFixed(2) }}
            </div>
          </div>
          <div class="xian"/>
          <div class="free">
            {{ t('wholesaleCart.freeShipping') }}
          </div>
          <div class="promotional">
            {{ t('wholesaleCart.promotionalCode') }}
          </div>
          <div class="code">
            <el-input 
              v-model="couponCode" 
              type="text" 
              :placeholder="t('wholesaleCart.enterCouponCode')"
              @keyup.enter="applyCoupons"/>
            <el-button @click="applyCoupons">{{ t('wholesaleCart.selectMyCoupons') }}</el-button>
          </div>
          <div class="checkout" @click="goToCheckout">
            {{ t('wholesaleCart.checkoutNow') }}
          </div>
        </div>
        <div class="right-bottom">
          <div class="text">
            {{ t('wholesaleCart.estimateShipping') }}
          </div>
          <div class="text">
            {{ t('wholesaleCart.shippingWeight', { weight: calculateTotalWeight }) }}
          </div>
          <div class="title">
            {{ t('wholesaleCart.weAccept') }}
          </div>
          <div class="imgs">
            <div v-for="(item,index) in shippingMethod?.images" :key="index" class="img">
              <img :src="item?.image" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useShoppingCartStore } from '~/stores/shopping-cart'
import type { CheckboxValueType } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";
import { useLanguageStore } from "~/stores/language";


// i18n
const { t } = useI18n()
// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言
const { language } = useLanguageStore()
// 切换语言
switchLanguage(language)


definePageMeta({
    layout: "personal-center"
})
// 购物车store
const cartStore = useShoppingCartStore()

// 使用storeToRefs来正确解构响应式数据
const { 
  cartItems, 
  cartData, 
  isLoggedIn
} = storeToRefs(cartStore)

// 获取非响应式的方法
const { fetchCartData,applyCoupon,batchRemoveCart } = cartStore

// 搜索相关
const searchValue = ref('')
const searchTimeout = ref<NodeJS.Timeout | null>(null)

// 记录每个分组的勾选项
const groupCheckedMap = ref<Record<number, string[]>>({})

// 合成所有分组的勾选项
const allCheckedList = computed(() => {
  return Object.values(groupCheckedMap.value).flat()
})

const isIndeterminate = ref(false)

// checkAll 变为计算属性
const checkAll = computed({
  get() {
    return allCheckedList.value.length === cartItems.value.length && cartItems.value.length > 0
  },
  set(val: boolean) {
    if (val) {
      // 全选
      wholesaleCartItems.value.forEach((group, idx) => {
        groupCheckedMap.value[idx] = group.map(item => item.cart_item_id || item.name)
      })
    } else {
      // 取消全选
      wholesaleCartItems.value.forEach((_, idx) => {
        groupCheckedMap.value[idx] = []
      })
    }
  }
})

// 只在点击全选时批量操作
const handleCheckAllChange = (val: CheckboxValueType) => {
  checkAll.value = !!val
  isIndeterminate.value = false
}

// 只更新分组，不操作 checkAll
const handleCheckListChange = (val: string[], groupIndex: number) => {
  console.warn('handleCheckListChange===========================>', val);
  
  groupCheckedMap.value[groupIndex] = val
  // 只处理 isIndeterminate
  if (allCheckedList.value.length === cartItems.value.length) {
    isIndeterminate.value = false
  } else if (allCheckedList.value.length > 0) {
    isIndeterminate.value = true
  } else {
    isIndeterminate.value = false
  }
}
// 批量删除
const batchRemoveCarts = async () => {
  console.warn('batchRemoveCart===========================>')
  const result = await batchRemoveCart(allCheckedList.value)
  if(result && result.success){
    ElMessage.success(result.message)
    await fetchCartData()
    checkAll.value = false
    isIndeterminate.value = false
  }else{
    ElMessage.error(result.message)
  }
}

// 搜索处理函数
const handleSearch = () => {
  // 清除之前的定时器
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  // 设置新的定时器，防抖处理
  searchTimeout.value = setTimeout(() => {
    console.warn('🔍 搜索关键词:', searchValue.value)
  }, 300)
}

// 清除搜索
const handleClearSearch = () => {
  searchValue.value = ''
  console.warn('🔍 清除搜索')
}

// 确保在DOM挂载后再获取数据，避免与布局中的调用冲突
onMounted(async () => {
  try {
    console.warn('📄 购物车页面: 开始初始化')
    console.warn('📄 购物车页面: 当前登录状态:', isLoggedIn.value)
    console.warn('📄 购物车页面: 当前购物车商品数量:', cartItems.value)
    
    // 等待一小段时间确保用户状态和布局初始化完成
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 强制重新获取最新数据
    console.warn('📄 购物车页面: 强制重新获取购物车数据')
    await fetchCartData()
    // 保存配送方式
    await saveShippingMethod()
    console.warn('📄 购物车页面: 数据获取完成，最终商品数量:', cartItems.value.length)
  } catch (error) {
    console.error('📄 购物车页面: 获取购物车数据失败:', error)
  }
})

const updateCart = async () => {
  await fetchCartData()
}

// 将所有购物车商品根据商品名称分类
const wholesaleCartItems = computed(() => {
  // 按商品名称分组
  const groupedItems = cartItems.value.reduce((groups, item) => {
    const name = item.name
    if (!groups[name]) {
      groups[name] = []
    }
    groups[name].push(item)
    return groups
  }, {} as Record<string, typeof cartItems.value>)
  
  // 将分组结果转换为数组格式
  return Object.values(groupedItems)
})

// 过滤后的购物车商品
const filteredWholesaleCartItems = computed(() => {
  if (!searchValue.value.trim()) {
    return wholesaleCartItems.value
  }
  
  const searchTerm = searchValue.value.toLowerCase().trim()
  
  return wholesaleCartItems.value.filter(group => {
    // 检查组内是否有任何商品匹配搜索条件
    return group.some(item => {
      // 搜索商品名称
      if (item.name && item.name.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索商品SKU
      if (item.sku && item.sku.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索商品类型
      if (item.type && item.type.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索商品属性（颜色、品牌、设备等）
      if (item.attributes) {
        // 搜索颜色
        if (item.attributes.color?.option_label && 
            item.attributes.color.option_label.toLowerCase().includes(searchTerm)) {
          return true
        }
        
        // 搜索品牌
        if (item.attributes.brand?.option_label && 
            item.attributes.brand.option_label.toLowerCase().includes(searchTerm)) {
          return true
        }
        
        // 搜索设备
        if (item.attributes.device?.option_label && 
            item.attributes.device.option_label.toLowerCase().includes(searchTerm)) {
          return true
        }
      }
      
      return false
    })
  })
})

interface shippingInterface{
  images: Array<{
    image: string,
    title: string,
    link: string,
    intro: string
  }>
}
const shippingMethod = ref<shippingInterface>()

// 获取支持的支付方式图标
const saveShippingMethod = async () => {
  const result = await cartStore.getPaymentMethod()
  // console.warn('📄 购物车页面: 保存配送方式结果:', result)
  if(result && result.success){
    shippingMethod.value = result.data
    console.warn('📄 购物车页面: 保存配送方式结果:', shippingMethod.value);
    
  }else{
    ElMessage.error(result?.message)
  }
}
//计算选中商品价格
const Subtotal = computed(() => {
  if(allCheckedList.value.length > 0){
    return cartItems.value.filter(item => allCheckedList.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.total))
    }, 0)
  }
  return 0
})

// 计算打折后的总金额
const totalAmount = computed(() => {
  if(allCheckedList.value.length > 0){
    const total = cartItems.value.filter(item => allCheckedList.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.total))
    }, 0)
    return total - (Number(cartData.value?.discount_amount) || 0) < 0 ? 0 : total - (Number(cartData.value?.discount_amount) || 0)
  }
  return 0
})
const couponCode = ref('')
// 应用优惠券
const applyCoupons = async () => {
  if (!couponCode.value.trim()) {
    ElMessage.warning(t('wholesaleCart.pleaseEnterCouponCode'))
    return
  }
  
  try {
    const result = await applyCoupon(couponCode.value)
    console.warn('📄 购物车页面: 应用优惠券结果:', result)
    // 这里可以调用应用优惠券的API
    if(result.success){
      ElMessage.success(t('wholesaleCart.couponAppliedSuccessfully'))
      await fetchCartData() // 重新获取购物车数据
    }else{
      ElMessage.error(result.message)
    }
  } catch(error) {
    console.error('📄 购物车页面: 应用优惠券失败:', error)
    ElMessage.error(t('wholesaleCart.couponApplicationFailed'))
  }
  
}

// 计算选中商品的总重量（假设每个商品0.003kg）
const calculateTotalWeight = computed(() => {
  if (allCheckedList.value.length > 0) {
   const totalWeight = cartItems.value.filter(item => allCheckedList.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.weight) * item.quantity)
    }, 0)
    return totalWeight.toFixed(3)
  }
  return '0.000'
})
const router = useRouter()

// 跳转到购物页面
const goShopping = () => {
  router.push('/home')
}

// 跳转到结账页面
const goToCheckout = () => {
  if (allCheckedList.value.length === 0) {
    ElMessage.warning(t('wholesaleCart.cartIsEmpty'))
    return
  }
  
  if (!isLoggedIn.value) {
    ElMessage.warning(t('wholesaleCart.pleaseLoginFirst'))
    router.push('/login')
    return
  }
  
  router.push('/payment')
}
</script>

<style lang="scss" scoped>
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
  background-color: #3D3D3D;
  border-color: #3D3D3D;
}
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #3D3D3D;
  border-color: #3D3D3D;
}
.shopping-cart{
  width: 100%;
  padding-bottom:12.625rem;
  background: #F5F5F5;
  .content{
    box-sizing: border-box;
    margin: 0 auto;
    padding-top: 3.375rem;
    width: 87.5625rem;
    
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left{
      width: 54.875rem;
      height: 73.4375rem;
      background: #FFFFFF;
      border-radius: .9375rem;
      padding: 0 3.5rem;
      box-sizing: border-box;
      // overflow: auto;

      .search{
        margin-top: 3.625rem;
      }
      
      .no-results {
        margin-top: 2rem;
        text-align: center;
        padding: 3rem 0;
        
        .no-results-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          
          p {
            margin: 0;
            color: #999;
            font-size: 1rem;
            
            &.search-tip {
              font-size: 0.875rem;
              color: #ccc;
            }
          }
        }
      }
      
      .empty-cart {
        margin-top: 2rem;
        text-align: center;
        padding: 3rem 0;
        
        .empty-cart-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1.5rem;
          
          .empty-icon {
            width: 4rem;
            height: 4rem;
            opacity: 0.5;
          }
          
          p {
            margin: 0;
            color: #666;
            font-size: 1.125rem;
          }
        }
      }
      
      .select-all{
        border-bottom: 1px solid #EBEBEB;
        margin-top: .5rem;
        padding: 1rem 0;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .all-left{
          display: flex;
          align-items: center;
          .el-checkbox{
            ::v-deep .el-checkbox__label{
              font-size: 1rem;
              color: #000000;
              font-family: SFProDisplay, SFProDisplay;
              font-weight: 600;
            }
          }
          .detail{
            margin-left: 1.5rem;
            height: 1.5rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1rem;
            color: #000000;
            line-height: 1.5rem;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }
    .right{
      width: 31.125rem;
      height: 73.4375rem;
      .right-top{
        width: 31.125rem;
        height: 34.8125rem;
        background: #FFFFFF;
        border-radius: .9375rem;
        padding: 3.5625rem 2.5rem 2.75rem 2.5rem;
        box-sizing: border-box;
        .order{
          font-family: SFProDisplay, SFProDisplay;
          font-weight: bold;
          font-size: 1.25rem;
          color: #111111;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .total-box{
          margin-top: 2.125rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          font-style: normal;
          text-transform: none;
        }
        .saved{
          margin-top: 2.125rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #979797;
          font-style: normal;
          text-transform: none;
        }
        .xian{
          margin-top: 1.8125rem;
          width: 26.1875rem;
          height: .0625rem;
          border-bottom: .0625rem solid #E0E0E0;
        }
        .free{
          margin-top: 1.5625rem;
          width: 25.5rem;
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1.125rem;
          color: #3D3D3D;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .promotional{
          margin-top: .625rem;
          height: 1.6875rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .875rem;
          color: #3D3D3D;
          line-height: 1.6875rem;
          font-style: normal;
          text-transform: none;
        }
        .code{
          margin-top: .625rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .el-input{
            width: 13.75rem;
            height: 3.5rem;
            background: #FFFFFF;
            border-radius: .375rem;
            border: .0625rem solid #BABABA;
          }
          .el-button{
            // width: 10.875rem;
            height: 3.5rem;
            margin-left: .3125rem;
            background: #F7F7F7;
            border-radius: 6px;
            border: 1px solid #3D3D3D;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 500;
            font-size: 1rem;
            color: #3D3D3D;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }

        }
        .checkout{
          margin-top: 2.25rem;
          width: 100%;
          height: 3.5rem;
          background: #000000;
          border-radius: .375rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1rem;
          color: #FFFFFF;
          line-height: 3.5rem;
          text-align: center;
          font-style: normal;
        }
      }
      .right-bottom{
        margin-top: 1.1875rem;
        width: 31.125rem;
        height: 16.4375rem;
        background: #FFFFFF;
        border-radius: .9375rem;
        padding: 2rem 2.5rem 0 2.4375rem;
        box-sizing: border-box;
        .text{
          margin-bottom: .5625rem;
          width: 25.5rem;
          height: 1rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .9375rem;
          color: #3D3D3D;
          line-height: 1rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .title{
          margin-top: 1.3125rem;
          height: 1rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 800;
          font-size: 1.125rem;
          color: #545454;
          line-height: 1rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .imgs{
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 1.0625rem;
          gap: .9375rem;
          .img{
            flex-shrink: 0;
            width: 5.5rem;
            height: 3rem;
            // border: .0625rem solid #D5D5D5;
            img{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
  
}
</style>
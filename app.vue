<template>
  <div>
     <NuxtPage/>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user'

onMounted(() => {
  // 只在客户端执行
  if (import.meta.client && typeof window !== "undefined") {
      const percentageOn1 = (window.screen.width / 1920) * 100;
      document.body.style.zoom = percentageOn1 + "%";
      
      // 初始化token状态
      const userStore = useUserStore()
      userStore.initializeTokenState()
  } 
});


</script>

<style lang="scss" scoped>

</style>
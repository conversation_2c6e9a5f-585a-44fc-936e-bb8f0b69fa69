// import { useLanguageStore } from '~/stores/language'
import { getNavbar, getLanguages, getHeaderOffer, getFooterData } from '~/api/public'
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

export const useLanguageDataRefresh = () => {
  // const languageStore = useLanguageStore()
  const { onLanguageChange } = useLanguageEventBus()

  // 响应式数据
  const navData = ref<navDataType[]>([])
  const languageList = ref<LanguageType[]>([])
  const headerOfferData = ref<headerOfferDataType>({ title: '', redirection_title: '' })
  const footerData = ref<footerDataType>({})

  // 数据获取函数
  const fetchData = async () => {
    try {
      // 获取导航栏列表数据
      const { data: navDataResult } = await getNavbar()
      navData.value = navDataResult
      
      // 获取语种列表数据
      const { data: languageListResult } = await getLanguages()
      languageList.value = languageListResult
      
      // 获取页眉优惠信息
      const { data: headerOfferDataResult } = await getHeaderOffer()
      headerOfferData.value = headerOfferDataResult
      
      // 获取底部页面内容
      const { data: footerDataResult } = await getFooterData()
      footerData.value = footerDataResult
    } catch (error) {
      console.error('🌐 数据获取失败:', error)
    }
  }

  // 监听语言变化并重新获取数据
  const watchLanguageChange = () => {
    // 使用事件总线监听语种变化
    onLanguageChange(async (newLanguage: string) => {
      try {
        await fetchData()
        console.warn('🌐 语种切换后数据已重新获取:', newLanguage)
      } catch (error) {
        console.error('🌐 语种切换后重新获取数据失败:', error)
      }
    })
    
    // 初始化时获取数据
    fetchData()
  }

  // 全局数据刷新函数，可以在任何地方调用
  const refreshGlobalData = async () => {
    try {
      await fetchData()
      console.warn('🌐 全局数据已刷新')
    } catch (error) {
      console.error('🌐 全局数据刷新失败:', error)
    }
  }

  return {
    navData,
    languageList,
    headerOfferData,
    footerData,
    fetchData,
    watchLanguageChange,
    refreshGlobalData
  }
}

// 类型定义
interface navDataType {
  name: string,
  link: string,
  logo_path: string,
  position: number,
  category_ids: string,
  children: Array<navDataType>
}

interface headerOfferDataType {
  title: string,
  redirection_title: string
}

interface footerDataType {
  [key: string]: string | number | boolean | object
}

interface LanguageType {
  code: string,
  name: string,
  id: number,
  direction: number,
  created_at: string | null,
  is_default: boolean,
  logo_path: string,
  logo_url: string,
  updated_at: string | null,
  pivot: [channel: number, locale_id: number]
} 
<template>
    <div class="page">
        <div class="older-info">
            <span style="margin-left: 1.8125rem;">2025-04-09 16:23:46</span>
            <span style="float: right;margin-right: 1.1875rem;">Order number:
                2025101512345</span>
        </div>
        <div class="product-info">
            <div v-for="(item, index) in productData" :key="index" class="box">
                <div style="padding: 2.375rem 0 0 1.8125rem;display: flex;">
                    <img :src="item.img" alt="">
                    <div style="margin:.1875rem 0 0 1.125rem ;display: grid;">
                        <span>{{ item.name }}</span>
                        <span class="price">{{ item.price }}</span>
                    </div>
                    <div class="number">
                        <span>x{{ item.number }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="hide" style="display: flex;justify-content: center;" @click="hide" >
            <el-icon v-if="!hideState" :size="24" style="padding-top: .75rem;padding-right: .3125rem;">
                <ArrowDownBold />
            </el-icon>
            <el-icon v-if="hideState" :size="24" style="padding-top: .75rem;padding-right: .3125rem;">
                <ArrowUpBold />
            </el-icon>
            <span v-if="!hideState" style="margin-left: .625rem;">Show more products ({{
                hideData.length }} more)</span>
            <span v-if="hideState" style="margin-left: .625rem;">Put away the product</span>
        </div>
        <div class="info">
            <div class="amount">Order total amount: $180.6</div>
            <div class="button">
                <div class="shipped">
                    Shipped
                </div>
                <div class="details">View details</div>
                <div class="details">Track</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const productData = ref([
    {
        img: '/_nuxt/assets/images/phone.png',
        name: 'Privacy Tempered Glass for iPhone 16 Pro Max Discounted',
        price: '$ 22.49',
        number: '18'
    },
    {
        img: '/_nuxt/assets/images/phone.png',
        name: 'Privacy Tempered Glass for iPhone 16 Pro Max Discounted',
        price: '$ 22.49',
        number: '68'
    },
])
const hideData = ref([
    {
        img: '/_nuxt/assets/images/phone.png',
        name: 'Privacy Tempered Glass for iPhone 16 Pro Max Discounted',
        price: '$ 22.49',
        number: '22'
    },
    {
        img: '/_nuxt/assets/images/phone.png',
        name: 'Privacy Tempered Glass for iPhone 16 Pro Max Discounted',
        price: '$ 22.41',
        number: '23'
    }
])
// 是否展开标识
const hideState = ref(false)

const hide = () => {
    if (!hideState.value) {
        hideData.value.forEach(i => {
            productData.value.push(i)
        })
        hideState.value = true
    } else {
        productData.value.splice(2)
        hideState.value = false
    }
}
</script>

<style scoped lang="scss">
.page {
    width: 100%;

    .older-info {
        height: 3rem;
        background: #EBEBEB;

        span {
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 500;
            font-size: 1rem;
            color: #3D3D3D;
            line-height: 3rem;
            text-align: left;
            font-style: normal;
        }
    }

    .product-info {
        // height: 23.625rem;
        background: #FFFFFF;
        border: .0625rem solid #EFEFEF;

        .box {
            height: 8.375rem;
            border-bottom: .0625rem solid #DBDBDB;

            span {
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 400;
                font-size: 1rem;
                color: #3D3D3D;
                text-align: left;
                font-style: normal;
            }

            .price {
                margin-top: .5rem;
                font-weight: 500;
                font-size: 1.25rem;
            }

            .number {
                width: 5.5rem;
                height: 2rem;
                border-radius: 1rem;
                border: .0625rem solid #CCCCCC;
                margin-left: auto;
                margin-right: 2.1875rem;
                margin-top: .625rem;
                text-align: center;

                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: .875rem;
                    color: #000000;
                    line-height: 2rem;
                    font-style: normal;
                }
            }
        }
    }

    .hide {
        height: 3rem;
        line-height: 3rem;
        background: #FFFFFF;
        span {
            font-family: PingFangSC, PingFang SC;
            font-weight: bold;
            font-size: .875rem;
            color: #000000;
            text-align: center;
            font-style: normal;
        }
    }

    .info {
        height: 4.8125rem;
        border-top: 1px solid#DBDBDB;
        background: #FFFFFF;
        font-family: PingFangSC, PingFang SC;
        color: #000000;
        font-style: normal;
        display: flex;
        justify-content: space-around;

        .amount {
            width: 37.5rem;
            font-weight: bold;
            font-size: 1.125rem;
            line-height: 4.8125rem;
            text-align: left;
        }

        .button {
            width: 27.5rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 400;
            font-size: 16px;
            font-style: normal;
            display: flex;
            justify-content: space-around;
            align-items: center;

            div {
                width: 7.25rem;
                height: 2.5rem;
                border-radius: .5rem;
                text-align: center;
                line-height: 2.5rem;
            }

            .shipped {
                background: #3D3D3D;
                color: #FFFFFF;
            }

            .details {
                color: #3D3D3D;
                border: 1px solid #979797;
            }
        }
    }
}
</style>
# 新闻页面语种监听功能

## 功能概述

新闻页面现在支持监听语种变化，当用户切换语言时，页面会自动重新获取新闻标签和新闻列表数据。

## 实现原理

### 1. 语言事件总线
使用 `useLanguageEventBus` composable 来监听语种变化事件：

```typescript
const { onLanguageChange } = useLanguageEventBus()
```

### 2. 语种变化处理函数
当检测到语种变化时，会执行以下操作：

```typescript
const handleLanguageChange = async (newLanguage: string) => {
  console.warn('🌐 新闻页面检测到语种变化:', newLanguage)
  
  // 重置状态
  btnList.value = []
  checkBtnId.value = 0
  newsList.value = []
  tagsError.value = false
  _error.value = false
  
  // 重新获取标签列表
  await getNewsTagsList()
  
  // 只有在成功获取到标签列表后才获取新闻列表
  if (btnList.value.length > 0) {
    await getNewsList()
  }
}
```

### 3. API 请求自动语言处理
`noTokenRequest` 工具类已经集成了语言监听功能，会自动在请求头中添加 `X-Locale` 参数：

```typescript
const languageStore = useLanguageStore()
const currentLanguage = computed(() => languageStore.language)
watch(currentLanguage, (newVal) => {
  if (newOptions.headers) {
    newOptions.headers['X-Locale'] = newVal
  }
})
```

## 功能特点

1. **自动监听**: 页面加载时自动注册语种变化监听器
2. **状态重置**: 语种变化时重置所有相关状态
3. **错误处理**: 包含完整的错误处理机制
4. **数据同步**: 确保标签列表和新闻列表数据与当前语言同步
5. **性能优化**: 只有在成功获取标签列表后才获取新闻列表

## 使用方式

1. 用户访问新闻页面
2. 页面自动加载当前语言的新闻数据
3. 用户切换语言（通过语言选择器）
4. 页面自动检测语种变化
5. 重新获取对应语言的新闻数据
6. 更新页面显示

## 技术实现

- **事件驱动**: 使用事件总线模式实现解耦的语种监听
- **响应式**: 利用 Vue 3 的响应式系统
- **异步处理**: 使用 async/await 处理异步数据获取
- **错误边界**: 完善的错误处理和用户反馈

## 注意事项

1. 监听器会在组件卸载时自动清理
2. API 请求会自动包含语言参数
3. 状态重置确保数据一致性
4. 控制台会输出语种变化的调试信息 
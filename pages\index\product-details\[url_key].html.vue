<template>
  <div class="product-details">
    <div class="header">
      <div class="header-left">
        <div class="header-left-top">
            <div class="img-bar">
                <el-affix target=".md" :offset="0" :style="{ width: '100%', height: `${mediaItems.length * 7.625+2.1}rem` }">
                  <div v-for="(item, index) in mediaItems" :key="item.id" :class="imgIndex==index?(item.type === 'video' ? 'video-item active' : 'img-item active'):(item.type === 'video' ? 'video-item' : 'img-item')" @click="changeImg(index)">
                    <video 
                      v-if="item.type === 'video'" 
                      :src="item.url" 
                      loop 
                      muted
                      preload="metadata"
                      @loadedmetadata="handleVideoLoaded"
                      @error="handleVideoError"
                    />
                    <img v-else :src="item.url" alt="">
                  </div>
                </el-affix>
            </div>
            <div class="md" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; background: transparent;"/>
          <div class="zsimg">
             <el-carousel ref="carousel" style="width: 40.8125rem;height: 40.8125rem;" :autoplay="false" arrow="never" indicator-position="none" :initial-index="imgIndex">
              <el-carousel-item v-for="item in mediaItems" :key="item.id" :name="imgIndex+''">
                <div style="width: 100%;height: 100%; display: flex;align-items: center;justify-content: center;">
                  <video 
                    v-if="item.type === 'video'" 
                    :src="item.url" 
                    loop 
                    controls
                    preload="metadata"
                    style="width: 40.8125rem;cursor: pointer;" 
                    @click="showPreview = true"
                    @loadedmetadata="handleVideoLoaded"
                    @error="handleVideoError"
                    @play="handleVideoPlay"
                    @pause="handleVideoPause"
                    @ended="handleVideoEnded"
                  />
                  <img v-else :src="item.url" alt="" style="width: 40.8125rem;" @click="showPreview = true">
                </div>
              </el-carousel-item>
            </el-carousel>
            <!-- 自定义媒体预览 -->
            <div v-if="showPreview" class="media-preview-overlay" @click="handlePreviewClose">
              <div class="media-preview-content" @click.stop>
                <div class="media-preview-header">
                  <button class="close-btn" :title="$t('productDetails.mediaPreview.close')" @click="handlePreviewClose">×</button>
                </div>
                <div class="media-preview-body">
                  <video 
                    v-if="mediaItems[imgIndex]?.type === 'video'" 
                    :src="mediaItems[imgIndex]?.url" 
                    controls
                    autoplay
                    style="max-width: 100%; max-height: 100%;"
                  />
                  <img 
                    v-else 
                    :src="mediaItems[imgIndex]?.url" 
                    alt="" 
                    style="max-width: 100%; max-height: 100%; object-fit: contain;"
                  >
                </div>
                <div class="media-preview-footer">
                  <button 
                    v-if="imgIndex > 0" 
                    class="nav-btn prev" 
                    :title="$t('productDetails.mediaPreview.previous')"
                    @click="imgIndex--"
                  >
                    ‹
                  </button>
                  <span class="media-counter">{{ imgIndex + 1 }} / {{ mediaItems.length }}</span>
                  <button 
                    v-if="imgIndex < mediaItems.length - 1" 
                    class="nav-btn next" 
                    :title="$t('productDetails.mediaPreview.next')"
                    @click="imgIndex++"
                  >
                    ›
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="header-left-bottom">
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item :title="$t('productDetails.description')" name="1">
              <!-- eslint-disable-next-line vue/no-v-html -->
              <div v-html="sanitizedDescription"/>
            </el-collapse-item>
            <el-collapse-item :title="$t('productDetails.specification')" name="2">
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.dimensions') }}
                </div>
                <div class="text-right">
                  <div v-for="item in specifications?.dimensions" :key="item.id">
                    {{ item.title }}
                  </div>
                </div>
              </div>
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.profile') }}
                </div>
                <div class="text-right">
                  {{ specifications?.profile?.formatted }}
                </div>
              </div>
              <div class="text">
                <div class="text-left">
                  {{ $t('productDetails.dropRating') }}
                </div>
                <div class="text-right">
                  {{ specifications?.drop_rating?.formatted }}
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="header-right">
        <el-affix target=".header-right-bottom" :offset="0" :style="{width: '30.6875rem', height: affixHeight+16.3 + 'rem' }">
          <div ref="affixContent" style="width: 30.6875rem;">
            <div v-if="loading" class="loading">
              <div class="loading-text">
                {{ $t('productDetails.loading') }}
              </div>
              <div class="loading-spinner"/>
            </div>
            <div v-else-if="error" class="error">
              <div class="error-message">{{ error }}</div>
            </div>
            <div v-else-if="!productDetail" class="error">
              {{ $t('productDetails.productNotFound') }}
            </div>
            <template v-else-if="productDetail">
              <div class="header-right-title" :title="productDetail.name">
                {{ productDetail.name }}
              </div>
              <div class="header-right-text" :title="productDetail.short_description">
                {{ productDetail.short_description }}
              </div>
              <div v-if="specialPriceData" class="header-right-shipping">
                <div class="header-right-shipping-text">
                  {{ $t('productDetails.shippingUpTo', { percent: specialPriceData.discount_percent }) }}
                </div>
                <div class="header-right-shipping-text">
                  {{ specialPriceData.end_date }}
                </div>
              </div>
              <div class="price">
                {{ currentPrice }}
              </div>
              <div v-if="productDetail.customer_group" class="header-right-text">
                {{ productDetail.customer_group }}
              </div>
            <div v-if="brands||devices" class="header-right-heading">
              {{ $t('productDetails.device') }}:
            </div>
            <div v-if="brands||devices" class="product-select">
              <div class="product-select-left">
                <el-select 
                  v-model="selectedBrand" 
                  :placeholder="$t('productDetails.selectBrand')" 
                  style="width: 100%"
                  :teleported="false"
                  @change="handleBrandChange"
                >
                  <el-option
                    v-for="brand in brands"
                    :key="brand.id"
                    :label="brand.name"
                    :value="brand.id"
                  />
                </el-select>
              </div>
              <div class="product-select-separator">
                |
              </div>
              <div class="product-select-right">
                <el-select 
                  v-model="selectedDevice" 
                  :placeholder="$t('productDetails.selectDevice')" 
                  style="width: 100%"
                  :teleported="false"
                  @change="handleDeviceChange"
                >
                  <el-option
                    v-for="device in filteredDevices"
                    :key="device.id"
                    :label="device.name"
                    :value="device.id"
                  />
                </el-select>
              </div>
            </div>
            <div v-if="availableColors.length > 0" class="header-right-heading">
              {{ $t('productDetails.color') }} :
            </div>
            <div v-if="availableColors.length > 0" class="color-bar">
              <div 
                v-for="(item, index) in availableColors" 
                :key="index"
                class="color-bar-item"
                :class="{ 'active': selectedColor === item?.color?.id }"
                @click="handleColorSelect(item?.color?.id)"
              >
                <div class="yuan" :style="{ backgroundColor: item?.color?.swatch_value }"/>
                <div class="text">{{ item?.color?.label }}</div>
              </div>
            </div>
            <div class="quantity">
              <div class="text">
                {{ $t('productDetails.quantity') }}：
              </div>
              <div class="count">
                <div style="font-size: 1.25rem;cursor: pointer;" @click="decreaseCount">-</div>
                <input v-model="count" type="text" min="1" @input="validateCount">
                <div style="font-size: 1.25rem;cursor: pointer;" @click="increaseCount">+</div>
              </div>
            </div>
            <div 
              class="btn add" 
              :class="{ 
                'in-cart': isInCart, 
                'disabled': !isColorSelected || isInCart 
              }" 
              @click="handleAddToCart"
            >
              {{ isInCart ? $t('productDetails.alreadyInCart', { count: cartQuantity }) : $t('productDetails.addToCart') }}
            </div>
            <div class="btn buy" @click="handleBuyNow">
              {{ $t('productDetails.buyNow') }}
            </div>
            <div class="checkout">
              {{ $t('productDetails.checkoutSecurely') }}
            </div>
            <div class="co">
              <img src="~/assets/images/detail/co1.png" alt="">
              <img src="~/assets/images/detail/co2.png" alt="">
              <img src="~/assets/images/detail/co3.png" alt="">
              <img src="~/assets/images/detail/co4.png" alt="">
              <img src="~/assets/images/detail/co5.png" alt="">
            </div>
            <div class="accept">
              {{ $t('productDetails.acceptedPayments') }}
            </div>
            <div class="ts">
              {{ $t('productDetails.interestFreePayments') }} <a href="#">{{ $t('productDetails.learnMore') }}</a>
            </div>
            <div class="bottom-img-list">
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/1.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/2.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/3.png" alt="">
              </div>
              <div class="bottom-img-item">
                <img src="~/assets/images/detail/4.png" alt="">
              </div>
            </div>
            </template>
          </div>
        </el-affix>
        <div class="header-right-bottom" style="position: absolute; bottom: 0; right: 0; width: 100%; height: 1px; background: transparent;"/>
      </div>
    </div>
    <div class="details_img">
      <img v-for="(item,index) in productBanners" :key="index" :src="item.image" alt="item.title">
    </div>
    <!-- 标题 -->
    <MLKTitle :title="$t('productDetails.relatedProducts')"/>
    <!-- 分割线 -->
    <el-divider style="width: 75rem;margin: 0 auto;" />
    <!-- 产品卡片组件 -->
    <div v-if="relatedProducts.length > 0" class="product-car">
      <MLKProductCard 
        v-for="item in relatedProducts" 
        :key="item.id" 
        :product-data="item"
        @add-to-cart="handleRelatedProductAddToCart"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getProductsDetail, getProductsrelated } from '~/api/channel'
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useWishlistStore } from '~/stores/wishlist'
import { ElMessage } from 'element-plus'
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

const route = useRoute()
const { t } = useI18n()
const cartStore = useShoppingCartStore()
const wishlistStore = useWishlistStore()
const productUrlKey = computed(() => route.params.url_key as string)

// 产品详情数据类型
interface ProductDetail {
  id: number
  name: string
  url_key: string
  type: string
  short_description: string
  description: string
  special_price: string
  end_date: string
  formatted_price: string
  wholesale_info?: string
  customer_group?: string
  brand?: string
  model?: string
  colors?: Array<{
    option_label: string
    swatch_value?: string
  }>
  images?: Array<{
    url: string
  }>
  videos?: Array<{
    id: number | string
    url: string
  }>
}

// 特殊价格数据类型
interface SpecialPriceData {
  discount_percent: number
  end_date: string
}

// 产品横幅图片类型
interface ProductBanner {
  image: string
  title?: string
}

// 产品详情数据
const productDetail = ref<ProductDetail | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

const customerGroupPrices = ref()
const type = ref()
const brandDeviceVariants = ref()
// 品牌和设备数据类型
interface Brand {
  id: number
  name: string
}

interface Device {
  id: number
  name: string
  parent_id: number
}

const brands = ref<Brand[]>([])
const devices = ref<Device[]>([])
const specifications = ref()
const variants = ref<Array<{
  id: number
  name: string
  option_label: string
  swatch_value?: string,
  customer_group_prices?: {
    formatted_price: string
  }[] | {
    formatted_price: string
  },
  color?: {
    admin_name: string,
    label: string
    id: number
    swatch_value: string
    sort_order: number
  },
  images?: Array<{
    url: string
  }>,
  videos?: Array<{
    id: number | string
    url: string
  }>
}>>([])

const affixContent = ref<HTMLElement | null>(null)
const affixHeight = ref(0)

const specialPriceData = ref<SpecialPriceData | null>(null)
const selectedBrand = ref<number>(0)
const selectedDevice = ref<number>(0)
const selectedColor = ref<number | null>(null)

const imgIndex = ref(0)

// 改进的高度计算函数
const updateAffixHeight = () => {
  if (affixContent.value) {
    // 使用 getBoundingClientRect 获取更精确的高度
    const rect = affixContent.value.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(affixContent.value)
    
    // 计算实际内容高度（包括 padding 和 border）
    const height = rect.height + 
      parseFloat(computedStyle.paddingTop) + 
      parseFloat(computedStyle.paddingBottom) + 
      parseFloat(computedStyle.borderTopWidth) + 
      parseFloat(computedStyle.borderBottomWidth)
    
    // 转换为 rem 单位（1rem = 16px）
    affixHeight.value = height / 16
  }
}

// 使用 ResizeObserver 监听元素大小变化
let resizeObserver: ResizeObserver | null = null

// 设置 ResizeObserver
const setupResizeObserver = () => {
  if (import.meta.client && affixContent.value) {
    // 清理之前的 observer
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
    
    // 创建新的 observer
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === affixContent.value) {
          updateAffixHeight()
        }
      }
    })
    
    // 开始观察
    resizeObserver.observe(affixContent.value)
  }
}

// 清理 ResizeObserver
const cleanupResizeObserver = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 监听相关数据变化
watch(
  [loading, productDetail, error, specialPriceData, selectedBrand, selectedDevice, selectedColor],
  () => {
    nextTick(() => {
      updateAffixHeight()
      // 重新设置 ResizeObserver
      setupResizeObserver()
    })
  },
  { immediate: true }
)

// 监听 affixContent 引用变化
watch(affixContent, (newElement) => {
  if (newElement) {
    nextTick(() => {
      updateAffixHeight()
      setupResizeObserver()
    })
  } else {
    cleanupResizeObserver()
  }
})


// 选中的品牌和设备
const filteredDevices = computed(() => {
  if (!selectedBrand.value || !devices.value.length) {
    return []
  }
  return devices.value.filter(device => device.parent_id === selectedBrand.value)
})

// 根据选中的品牌和设备过滤可用的颜色变体
const availableColors = computed(() => {
  // 如果没有品牌设备变体数据，返回所有变体
  if (!brandDeviceVariants.value) {
    return variants.value
  }
  
  // 如果没有选择品牌或设备，返回所有变体
  if (!selectedBrand.value || !selectedDevice.value) {
    return variants.value
  }
  
  const brandVariants = brandDeviceVariants.value[selectedBrand.value]
  if (!brandVariants) {
    return variants.value
  }
  
  const deviceVariants = brandVariants[selectedDevice.value]
  if (!deviceVariants || !Array.isArray(deviceVariants)) {
    return variants.value
  }
  
  // 根据变体ID筛选可用的颜色
  const filteredVariants = variants.value.filter(variant => 
    deviceVariants.includes(variant.id)
  )
  
  // 如果过滤后没有变体，返回所有变体作为后备
  return filteredVariants.length > 0 ? filteredVariants : variants.value
})

// 获取当前选中颜色的变体
const selectedVariant = computed(() => {
  if (!selectedColor.value || !availableColors.value.length) {
    return availableColors.value[0] || null
  }
  return availableColors.value.find(variant => variant.color?.id === selectedColor.value) || availableColors.value[0] || null
})

// HTML 内容安全处理函数
const sanitizeHtml = (html: string): string => {
  if (!html) return ''
  
  // 创建一个临时的 DOM 元素来解析 HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  
  // 移除所有 script 标签
  const scripts = tempDiv.querySelectorAll('script')
  scripts.forEach(script => script.remove())
  
  // 移除所有事件处理器属性
  const allElements = tempDiv.querySelectorAll('*')
  allElements.forEach(element => {
    const attrs = element.getAttributeNames()
    attrs.forEach(attr => {
      if (attr.startsWith('on') || attr.startsWith('javascript:')) {
        element.removeAttribute(attr)
      }
    })
  })
  
  return tempDiv.innerHTML
}

// 计算属性：安全的描述内容
const sanitizedDescription = computed(() => {
  if (!productDetail.value?.description) return ''
  
  // 在客户端环境下进行安全处理
  if (import.meta.client) {
    return sanitizeHtml(productDetail.value.description)
  }
  
  // 服务端环境下直接返回内容（服务端渲染时相对安全）
  return productDetail.value.description
})


// 媒体项类型定义
interface MediaItem {
  type: 'video' | 'image'
  url: string
  id: number | string
  index: number
}

// 合并视频和图片的计算属性
const mediaItems = computed<MediaItem[]>(() => {
  const items: MediaItem[] = []
  
  // 默认使用 productDetail 中的媒体内容，只有在选中颜色时才使用变体的媒体内容
  let currentImages = productDetail.value?.images || []
  let currentVideos = productDetail.value?.videos || []
  
  // 如果选中了颜色，则使用对应变体的媒体内容
  if (selectedColor.value && selectedVariant.value) {
    currentImages = selectedVariant.value.images || productDetail.value?.images || []
    currentVideos = selectedVariant.value.videos || productDetail.value?.videos || []
  }
  
  // 添加视频
  if (currentVideos.length > 0) {
    currentVideos.forEach((video, index) => {
      items.push({
        type: 'video',
        url: video.url,
        id: video.id,
        index: index
      })
    })
  }
  
  // 添加图片
  if (currentImages.length > 0) {
    currentImages.forEach((image, index) => {
      items.push({
        type: 'image',
        url: image.url,
        id: `img-${index}`,
        index: currentVideos.length + index
      })
    })
  }
  
  return items
})

// 当前选中颜色的价格
const currentPrice = computed(() => {
  // 安全获取客户组价格
  const safeCustomerGroupPrices = Array.isArray(customerGroupPrices.value) ? customerGroupPrices.value : [];
  const defaultPrice = safeCustomerGroupPrices.length > 0 ? (safeCustomerGroupPrices[0]?.formatted_price || '') : '';

  if (type.value === 'configurable' && selectedVariant.value) {
    // 如果选中了颜色变体，使用变体的价格
    const variantPrices = selectedVariant.value.customer_group_prices

    if (Array.isArray(variantPrices) && variantPrices.length > 0) {
      const price = variantPrices[0]?.formatted_price || defaultPrice
      return price
    } else if (variantPrices && typeof variantPrices === 'object' && 'formatted_price' in variantPrices) {
      const price = variantPrices.formatted_price || defaultPrice
      return price
    }
    return defaultPrice
  }
  // 否则使用默认价格
  return defaultPrice
})

// 检查商品是否已在购物车中
const isInCart = computed(() => {
  if (!productDetail.value) return false
  
  const selectedColorInfo = selectedVariant.value?.color
  const colorLabel = selectedColorInfo?.label || ''
  
  // 检查商品是否在购物车中，考虑颜色属性
  return cartStore.isInCart(productDetail.value.id, colorLabel)
})

// 获取购物车中该商品的数量
const cartQuantity = computed(() => {
  if (!productDetail.value) return 0
  
  const selectedColorInfo = selectedVariant.value?.color
  const colorLabel = selectedColorInfo?.label || ''
  
  // 获取购物车中该商品的数量，考虑颜色属性
  return cartStore.getItemQuantity(productDetail.value.id, colorLabel)
})

// 检查是否已选择颜色
const isColorSelected = computed(() => {
  return availableColors.value.length === 0 || selectedColor.value !== null
})

// 检查商品是否在心愿单中
const isInWishlist = computed(() => {
  if (!productDetail.value) return false
  return wishlistStore.isInWishlist(productDetail.value.id)
})

// 图片数组信息
const productBanners = ref<ProductBanner[]>([])
const handleChange = () =>{
  
}

const activeNames = ref(['1'])
// 走马灯组件
const carousel = ref()

// 点击图片导航栏执行方法
const changeImg = (index: number) => {
  if (carousel.value != null) {
    carousel.value.setActiveItem(index)
  }
  imgIndex.value = index
  
  // 停止当前播放的视频
  if (currentVideo.value && videoPlaying.value) {
    currentVideo.value.pause()
    videoPlaying.value = false
  }
  
}


const showPreview = ref(false)

// 视频播放状态管理
const videoPlaying = ref(false)
const currentVideo = ref<HTMLVideoElement | null>(null)

const count = ref(1)

// 数量控制方法
const decreaseCount = () => {
  if (count.value > 1) {
    count.value--
  }
}

const increaseCount = () => {
  count.value++
}

const validateCount = () => {
  // 确保数量不为负数或0
  if (count.value <= 0) {
    count.value = 1
  }
  // 确保是整数
  count.value = Math.floor(count.value)
}

// 相关产品数据
const relatedProducts = ref<RelatedProduct[]>([])

// 获取产品详情
const fetchProductDetail = async () => {
  if(import.meta.client){
    await nextTick()
  }
  
  try {
    loading.value = true
    error.value = null
    // 验证产品URL key是否存在
    if (!productUrlKey.value) {
      throw new Error('产品URL key不能为空')
    }
    const response = await getProductsDetail({ url_key: productUrlKey.value })
    if (!response.data) {
      throw new Error('API 响应为空，请检查网络连接')
    }
    if (response.success && response.data) {
      productDetail.value = response.data?.product
      productBanners.value = response.data?.product_banners?.images || []
      specialPriceData.value = response.data?.special_price
      specifications.value = response.data?.specifications
      customerGroupPrices.value = response.data?.customer_group_prices
      type.value = response.data?.type
      brands.value = response.data?.brands || []
      devices.value = response.data?.devices || []
      variants.value = response.data?.variants || []
      brandDeviceVariants.value = response.data?.brand_device_variants
      if (brands.value && brands.value.length > 0) {
        selectedBrand.value = brands.value[0].id
        const brandDevices = devices.value.filter(device => device.parent_id === selectedBrand.value)
        if (brandDevices.length > 0) {
          selectedDevice.value = brandDevices[0].id
        }
      }
      selectedColor.value = null
      await fetchRelatedProducts()
    } else {
      throw new Error('产品数据为空')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '获取产品详情失败'
  } finally {
    loading.value = false
  }
}

// 获取相关产品
const fetchRelatedProducts = async () => {
  try {
    if (!productUrlKey.value) {
      return
    }
    const response = await getProductsrelated({ id: productDetail.value?.id.toString() })
    if (!response.data) {
      relatedProducts.value = []
      return
    }
    if (response.data) {
      relatedProducts.value = response.data
    } else {
      relatedProducts.value = []
    }
  } catch (err) {
    console.error('获取相关产品失败:', err)
    relatedProducts.value = []
  }
}

// 处理添加到购物车
const handleAddToCart = async () => {
  try {
    // 检查产品详情是否存在
    if (!productDetail.value) {
      ElMessage.error(t('product.invalidProduct'))
      return
    }

    // 检查是否选择了颜色（如果有颜色选项）
    if (!isColorSelected.value) {
      ElMessage.warning(t('productDetails.pleaseSelectColor'))
      return
    }

    // 检查商品是否已在购物车中
    if (isInCart.value) {
      ElMessage.info(t('productDetails.productInCart'))
      return
    }

    // 获取选中的颜色信息
    const selectedColorInfo = selectedVariant.value?.color
    const colorLabel = selectedColorInfo?.label || ''

    // 处理价格
    const priceString = currentPrice.value
    const price = parseFloat(priceString.replace(/[^0-9.]/g, '')) || 0


    // 调用购物车store的添加方法
    const result = await cartStore.addToCart({
      id: productDetail.value.id.toString(),
      name: productDetail.value.name,
      base_image: mediaItems.value[0]?.url || productDetail.value.images?.[0]?.url || '',
      formatted_price: `$${price.toFixed(2)}`,
      short_description: productDetail.value.short_description,
      product_id: productDetail.value.id.toString(),
      price: price,
      selectedColor: colorLabel,
      attributes: {
        color: {
          option_label: colorLabel
        }
      }
    }, count.value)

    // 确保result存在且有success属性
    if (result && result.success) {
      ElMessage.success(t('product.addToCartSuccess'))
      console.warn('添加购物车成功')
    } else {
      const errorMessage = result?.message || t('product.addToCartFailed')
      ElMessage.error(errorMessage)
      console.error('添加购物车失败:', errorMessage)
    }
  } catch (error) {
    console.error('添加购物车时发生错误:', error)
    ElMessage.error(t('product.addToCartError'))
  }
}

// 处理立即购买
const handleBuyNow = () => {
  if (productDetail.value) {
    console.warn('立即购买:', productDetail.value)
    // 这里可以添加购买逻辑
  }
}

// 处理心愿单切换
const _handleWishlistToggle = async () => {
  if (!productDetail.value) {
    ElMessage.error('商品数据无效')
    return
  }

  try {
    if (isInWishlist.value) {
      // 如果已收藏，则取消收藏
      const wishlistItem = wishlistStore.getWishlistItem(productDetail.value.id)
      if (wishlistItem) {
        const result = await wishlistStore.removeFromWishlist(wishlistItem.id)
        if (result.success) {
          ElMessage.success(t('productCard.wishlistRemoved'))
        } else {
          ElMessage.error(result.message || t('productCard.wishlistRemoveFailed'))
        }
      }
    } else {
      // 如果未收藏，则添加收藏
      const result = await wishlistStore.addToWishlist({
        id: productDetail.value.id,
        url_key: productDetail.value.url_key,
        name: productDetail.value.name,
        formatted_base_price: productDetail.value.formatted_price,
        formatted_price: productDetail.value.formatted_price,
        base_image: productDetail.value.images?.[0]?.url || '',
        discount: {
          has_discount: false,
          regular_price: 0,
          is_special_price_active: false,
          special_price: 0,
          special_price_from: '',
          special_price_to: ''
        },
        in_stock: true
      })
      
      if (result.success) {
        ElMessage.success(t('productCard.wishlistAdded'))
      } else {
        ElMessage.error(result.message || t('productCard.wishlistAddFailed'))
      }
    }
  } catch (error) {
    console.error('收藏操作出错:', error)
    ElMessage.error(t('productCard.wishlistError'))
  }
}

// 处理品牌选择变化
const handleBrandChange = (value: number) => {
  selectedBrand.value = value
  // 根据选中的品牌自动选择第一个设备
  const brandDevices = devices.value.filter(device => device.parent_id === value)
  if (brandDevices.length > 0) {
    selectedDevice.value = brandDevices[0].id
  } else {
    selectedDevice.value = 0 // 如果该品牌下没有设备，才设置为0
  }
  
  // 重置颜色选择
  selectedColor.value = null
  imgIndex.value = 0
  
  console.warn('选择的品牌:', value, '自动选择的设备:', selectedDevice.value)
  console.warn('可用颜色:', availableColors.value)
}

// 处理设备选择变化
const handleDeviceChange = (value: number) => {
  selectedDevice.value = value
  
  // 重置颜色选择
  selectedColor.value = null
  imgIndex.value = 0
  
  console.warn('选择的设备:', value)
  console.warn('可用颜色:', availableColors.value)
}

// 处理颜色选择
const handleColorSelect = (colorId: number | undefined) => {
  if (colorId !== undefined) {
    selectedColor.value = colorId
    // 重置图片索引到第一张
    imgIndex.value = 0
    console.warn('选择的颜色:', colorId, '对应的变体:', selectedVariant.value)
    console.warn('变体价格数据:', selectedVariant.value?.customer_group_prices)
    console.warn('当前计算价格:', currentPrice.value)
  }
}

// 处理视频加载完成
const handleVideoLoaded = (event: Event) => {
  const video = event.target as HTMLVideoElement
  console.warn('视频加载完成:', video.src)
}

// 处理视频加载错误
const handleVideoError = (event: Event) => {
  const video = event.target as HTMLVideoElement
  console.error('视频加载失败:', video.src, event)
}

// 处理视频播放
const handleVideoPlay = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = true
  currentVideo.value = video
  console.warn('视频开始播放:', video.src)
}

// 处理视频暂停
const handleVideoPause = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = false
  console.warn('视频暂停:', video.src)
}

// 处理视频结束
const handleVideoEnded = (event: Event) => {
  const video = event.target as HTMLVideoElement
  videoPlaying.value = false
  console.warn('视频播放结束:', video.src)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!showPreview.value) return
  
  switch (event.key) {
    case 'Escape':
      showPreview.value = false
      break
    case 'ArrowLeft':
      if (imgIndex.value > 0) {
        imgIndex.value--
      }
      break
    case 'ArrowRight':
      if (imgIndex.value < mediaItems.value.length - 1) {
        imgIndex.value++
      }
      break
  }
}

// 处理预览关闭
const handlePreviewClose = () => {
  showPreview.value = false
  // 重置视频状态
  if (currentVideo.value) {
    currentVideo.value.pause()
    videoPlaying.value = false
  }
}

// 相关产品数据类型
interface RelatedProduct {
  id: number
  url_key: string
  name: string
  base_image: string
  formatted_price: string
  short_description: string
  price?: number
  color?: Array<{
    option_label: string
    swatch_value?: string
  }>
  selectedColor?: string
}

// 处理相关产品添加到购物车
const handleRelatedProductAddToCart = async (product: RelatedProduct) => {
  try {
    console.warn('相关产品添加到购物车:', product)
    
    // 确保价格是数字类型
    const price = product.price || parseFloat(product.formatted_price.replace(/[^0-9.]/g, ''))
    
    // 调用购物车store的添加方法
    const result = await cartStore.addToCart({
      id: product.id.toString(),
      name: product.name,
      base_image: product.base_image,
      formatted_price: `$${price.toFixed(2)}`,
      short_description: product.short_description,
      product_id: product.id.toString(),
      price: price,
      selectedColor: product.selectedColor || '',
      attributes: {
        color: {
          option_label: product.selectedColor || ''
        }
      }
    }, 1)
    
    // 确保result存在且有success属性
    if (result && result.success) {
      ElMessage.success(t('product.addToCartSuccess'))
      console.warn('相关产品添加购物车成功')
    } else {
      const errorMessage = result?.message || t('product.addToCartFailed')
      ElMessage.error(errorMessage)
      console.error('相关产品添加购物车失败:', errorMessage)
    }
  } catch (error) {
    console.error('相关产品添加购物车时发生错误:', error)
    ElMessage.error(t('product.addToCartError'))
  }
}

// 获取语言事件总线
const { onLanguageChange } = useLanguageEventBus()

// 监听路由参数变化
watch(() => route.params.url_key, (newUrlKey) => {
  if (newUrlKey) {
    fetchProductDetail()
  }
}, { immediate: true })

// 监听语种变化并重新获取数据
onLanguageChange(async () => {
  await fetchProductDetail()
})

// 调用动画效果和获取产品详情
onMounted(() => {
  // 只在客户端执行
  if (import.meta.client) {
    // 监听键盘事件
    window.addEventListener('keydown', handleKeydown);
  }
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('keydown', handleKeydown);
    
    // 清理视频资源
    if (currentVideo.value) {
      currentVideo.value.pause()
      currentVideo.value.src = ''
      currentVideo.value.load()
    }
    
    // 清理 ResizeObserver
    cleanupResizeObserver()
  }
})


</script>

<style lang="scss" scoped>
.product-details{
  width: 100%;
  
  .loading, .error {
    text-align: center;
    padding: 2rem;
    font-size: 1.125rem;
    color: #6C6C6C;
  }
  
  .loading {
    .loading-text {
      margin-bottom: 1rem;
      font-weight: 500;
    }
    
    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #ff4757;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .error {
    color: #ff4757;
    text-align: center;
    
    .error-message {
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
  }
  .header{
    margin: 4.375rem auto;
    width: 91.25rem;
    display: flex;
    justify-content: space-between;
    .header-left{
      width: 56.4375rem;
      // height: 69.3125rem;
      .header-left-top{
        width: 56.375rem;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .img-bar{
          width: 6.875rem;
          .video-item{
            cursor: pointer;
            width: 6.875rem;
            height: 6.875rem;
            background: #fff;
            border-radius: .5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: .75rem;
            transition: all 0.3s ease;
            position: relative;
            
            &:hover {
              background: #f5f5f5;
            }
            
            &.active{
              background: #F6F6F6;
            }
            
            video{
              width: 4.6875rem;
              height: 5.8125rem;
              border-radius: .25rem;
              object-fit: cover;
              pointer-events: none;
            }
            
            &::after {
              content: '▶';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 1.5rem;
              text-shadow: 0 0 4px rgba(0,0,0,0.8);
              pointer-events: none;
            }
          }
          .img-item{
            cursor: pointer;
            width: 6.875rem;
            height: 6.875rem;
            background: #fff;
            border-radius: .5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: .75rem;
            &:last-child{
              margin-bottom: none;
            }
            &.active{
              background: #F6F6F6;
            }
            img{
              width: 4.6875rem;
            }
          }
        }
        .zsimg{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 47.75rem;
          height: 47.75rem;
          background: #F6F6F6;
          border-radius: .8125rem;
          margin-left: 1.75rem;
          cursor: zoom-in;

          //主预览图宽高相同
          ::v-deep .el-carousel__container {
            height: 100%;
          }
          video {
            border-radius: .5rem;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
          
          img {
            border-radius: .5rem;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }
      }
      .header-left-bottom{
        margin-top: 2.4375rem;
        width: 100%;
        border-radius: .5rem;
        border: 1px solid #EBEBEB;
        overflow: hidden;
        padding: 0 1.5rem;
        box-sizing: border-box;
        .text{
          display: flex;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 13px;
          color: #565656;
          text-align: left;
          font-style: normal;
          padding:.625rem 0;
          border-top: 1px solid #EBEBEB;
          .text-left{
            width: 12.5rem;
          }
        } 
      }
      
    }
    .header-right{
      width: 30.6875rem;
      position: relative;
      .header-right-title{
        font-family: Helvetica;
        font-size: 2.5rem;
        margin-top: .4375rem;
        color: #000000;
        line-height: 2.5rem;
        font-style: normal;
        text-transform: none;
        margin-bottom: .5rem;
      }
      .header-right-text{
        width: 30.625rem;
        height: 1.5rem;
        font-family: AppleSystemUIFont;
        font-size: .875rem;
        color: #6C6C6C;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: .4375rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .header-right-shipping{
        width: 30.625rem;
        height: 3rem;
        background: #F3F3F3;
        margin-top: 1rem;
        border-radius: .375rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 .9375rem;
        box-sizing: border-box;
        .header-right-shipping-text{
          height: 1.25rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: .875rem;
          color: #565656;
          line-height: 1.25rem;
          font-style: normal;
        }
      }
      .price{
        height: 3rem;
        font-family: Helvetica;
        font-size: 2rem;
        color: #000000;
        line-height: 3rem;
        font-style: normal;
        text-transform: none;
        margin-top: 1.0625rem;
      }
      .header-right-heading{
        height: 1.3125rem;
        font-family: AppleSystemUIFont;
        font-size: 1.125rem;
        color: #0C0C0C;
        line-height: 1.3125rem;
        font-style: normal;
        margin-top: 1.1875rem;
      }
      .product-select{
        width: 30.625rem;
        margin-top: .75rem;
        height: 3rem;
        border: .0625rem solid #565656;
        border-radius: 3rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 .5rem;
       
        .product-select-left{
          flex: 1;
          padding: 0 .5rem;
          box-sizing: border-box;
          height: 2.5rem;
          display: flex;
          align-items: center;
          ::v-deep .el-select__wrapper {
            border: 0 !important;
            box-shadow: none !important;
          }
          
        }
        
        .product-select-separator{
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1rem;
          color: #565656;
          padding: 0 .5rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .product-select-right{
          flex: 1;
          padding: 0 .5rem;
          box-sizing: border-box;
          height: 2.5rem;
          display: flex;
          align-items: center;
          ::v-deep .el-select__wrapper {
            border: 0 !important;
            box-shadow: none !important;
          }
         
        }
      }
      .color-bar{
        width: 30.6875rem;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
        .color-bar-item{
          width: 14.8125rem;
          height: 3.5rem;
          border-radius: .5rem;
          border: .0625rem solid #D5D5D5;
          display: flex;
          align-items: center;
          justify-content: space-around;
          margin-top: .75rem;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #7F83F7;
            box-shadow: 0 2px 8px rgba(127, 131, 247, 0.2);
          }
          
          &.active {
            border-color: #7F83F7;
            background-color: rgba(127, 131, 247, 0.1);
            box-shadow: 0 2px 8px rgba(127, 131, 247, 0.3);
            
            .yuan {
              border-color: #7F83F7;
              box-shadow: 0 0 0 2px rgba(127, 131, 247, 0.3);
            }
            
            .text {
              color: #7F83F7;
              font-weight: 600;
            }
          }
          
          .yuan{
            width: 1.75rem;
            height: 1.75rem;
            background: #7F83F7;
            border-radius: 50%;
            border: 3px solid #CCCCCC;
            transition: all 0.3s ease;
            margin: 0 1.25rem;
          }
          .text{
            flex: 1;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: .9375rem;
            color: #0C0C0C;
            text-align: left;
            font-style: normal;
            transition: all 0.3s ease;
          }
        }
      }
      .quantity{
        display: flex;
        align-items: center;
        margin-top: 1.5rem;
        margin-bottom: 1.75rem;
        .text{
          height: 1.5rem;
          font-family: Helvetica;
          font-size: 1.125rem;
          color: #0C0C0C;
          line-height: 1.5rem;
          font-style: normal;
          text-transform: none;
        }
        .count{
          display: flex;
          align-items: center;
          height: 3rem;
          border: 1px solid #ccc;
          div{
            width: 3rem;
            height: 3rem;
            line-height: 3rem;
            font-family: AppleSystemUIFont;
            font-size: 18px;
            color: #6F6F6F;
            font-style: normal;
            text-transform: none;
            text-align: center;
            border: 1px solid #ccc;
            &:last-child{
              border-right: 0;
              border-top:0;
              border-bottom:0;
            }
            &:first-child{
              border-left: 0;
              border-top:0;
              border-bottom:0;
            }
          }
          input{
            width: 3rem;
            height: 100%;
            line-height: 3rem;
            font-family: AppleSystemUIFont;
            font-size: 18px;
            color: #6F6F6F;
            font-style: normal;
            text-transform: none;
            text-align: center;
            border: 0;
            outline: none;
            background-color: transparent;
          }
        }
      }
      .btn{
        width: 22.375rem;
        height: 3.625rem;
        line-height: 3.625rem;
        border-radius: .5rem;
        font-family: Helvetica;
        font-size: 1rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin: auto;
        margin-top: .75rem;
        cursor: pointer;
        transition: all 0.3s ease;
       
        &.add{
          color: #000000;
          background: #FFBA60;
          
          &:hover:not(.disabled) {
            background: #FFA726;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 186, 96, 0.3);
          }
          
          &.in-cart {
            background: #4CAF50;
            color: #FFFFFF;
            cursor: default;
            
            &:hover {
              background: #4CAF50;
              transform: none;
              box-shadow: none;
            }
          }
          
          &.disabled {
            background: #E0E0E0;
            color: #9E9E9E;
            cursor: not-allowed;
            
            &:hover {
              background: #E0E0E0;
              transform: none;
              box-shadow: none;
            }
          }
        }
        
        &.buy{
          color: #FFFFFF;
          background: #000000;
          
          &:hover {
            background: #333333;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
        }
      }
      .checkout{
        font-family: AppleSystemUIFont;
        font-size: 1.125rem;
        color: #565656;
        margin:1.5rem auto;
        font-style: normal;
        text-align: center;
      }
      .co{
        margin-top: .5rem;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        img{
          width: 4.125rem;
          height: 2.25rem;
        }
      }
      .accept{
        margin-top: 1.5rem;
        height: 1.3125rem;
        font-family: SFNS;
        font-weight: 400;
        font-size: 1.125rem;
        color: #3D3D3D;
        line-height: 1.3125rem;
        text-align: center;
        font-style: normal;
      }
      .ts{
        margin-top: 1.6875rem;
        height: 1.5rem;
        font-family: AppleSystemUIFont;
        font-size: .875rem;
        color: #6C6C6C;
        line-height: 1.5rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        a{
          text-decoration:underline;
           color: #6C6C6C;
          span{
            color: #000000;
          }
        }
      }
      .bottom-img-list{
        margin:1.0625rem auto;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2.3125rem;
        .bottom-img-item{
          width: 2.0625rem;
          height: 2.0625rem;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .details_img {
    width: 87.5rem;
    margin: 1.25rem auto;

    img {
      border-radius: 1rem;
      width: 100%;
      margin-bottom: 1rem;
    }
    .Rectangle_1 {
      width: 87.5rem;
      height: 31.625rem;
      background: #D8D8D8;
      border-radius: 1rem;
      margin: 2.5rem 0;
      // border: 1px solid #979797;
    }

    .Rectangle_2 {
      width: 87.5rem;
      height: 43.75rem;
      background: #D8D8D8;
      border-radius: 1rem;
      margin: 2.5rem 0;
      // border: 1px solid #979797;
    }
  }

  .product-car{
    width: 84.75rem;
    margin: 4.1875rem auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  

  
  // 媒体预览样式
  .media-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .media-preview-content {
      position: relative;
      width: 90%;
      height: 90%;
      background: #000;
      border-radius: 0.5rem;
      display: flex;
      flex-direction: column;
      
      .media-preview-header {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 10;
        padding: 1rem;
        
        .close-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          font-size: 2rem;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
      
      .media-preview-body {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
      }
      
      .media-preview-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        
        .nav-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          font-size: 2rem;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
          
          &.prev {
            transform: translateX(-50%);
          }
          
          &.next {
            transform: translateX(50%);
          }
        }
        
        .media-counter {
          color: white;
          font-size: 1rem;
          background: rgba(0, 0, 0, 0.5);
          padding: 0.5rem 1rem;
          border-radius: 1rem;
        }
      }
    }
  }
}
</style>
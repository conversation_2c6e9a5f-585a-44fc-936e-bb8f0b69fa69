<template>
    <div class="coupons-page">
        <div class="coupons-title">
            <span>My Coupons</span>
        </div>
        <div class="coupons-box">
            <div class="box" v-for="(item,index) in data" :key="index">
                <div class="box-title">
                    ${{ item.amount }} OFF
                </div>
                <div class="box-content">
                    Exclusive discounts for new users This coupon can be used for first-time purchases over ¥ 300 and is applicable to all product categories.
                </div>
                <div class="box-user">
                    NEWUSER50
                </div>
                <div class="box-introduce">
                    <div class="top">*Each account is limited to one use</div>
                    <div class="bottom">Valid until: December 31, 2024</div>
                </div>
                <div class="box-button">
                    Use
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const data = ref([
    {
        amount:'100'
    },
    {
        amount:'50'
    }
])
</script>

<style scoped lang="scss">
.coupons-page {
    width: 100%;
    height: 43.75rem;
    background: #FFFFFF;
    border-radius: .9375rem;
    .coupons-title {
        padding: 1.25rem 0;
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }
    .coupons-box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        .box {
            width: 31.875rem;
            // height: 23.4375rem;
            border-radius: .5rem;
            border: .0625rem solid #EBEBEB;
            .box-title {
                height: 3.75rem;
                background: #3D3D3D;
                border-radius: .5rem .5rem 0 0;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 600;
                font-size: 1.5rem;
                color: #FFBA60;
                line-height: 3.75rem;
                text-align: center;
                font-style: normal;
            }
            .box-content {
                margin: 1.5625rem 1.25rem 2.0625rem 1.25rem;
                width: 29.375rem;
                height: 3.75rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 300;
                font-size: 1rem;
                color: #3D3D3D;
                line-height: 1.1875rem;
                text-align: left;
                font-style: normal;
            }
            .box-user {
                width: 100%;
                height: 3.5rem;
                background: rgba(255,243,223,0.3);
                font-family: SFProDisplay, SFProDisplay;
                font-weight: bold;
                font-size: 1rem;
                color: #3D3D3D;
                line-height: 3.5rem;
                letter-spacing: .0625rem;
                text-align: center;
                font-style: normal;
            }
            .box-introduce {
                margin: 1.5625rem 1.25rem 2.0625rem 1.25rem;
                width: 29.375rem; 
                font-size: 1rem;
                color: #3D3D3D;
                line-height: 1.25rem;
                text-align: left;
                font-style: normal;
                font-family: SFProDisplay, SFProDisplay;
                .top{
                    height: 1.25rem;
                    font-weight: 300;
                }
                .bottom{
                    height: 1.25rem;
                    font-weight: 500;
                }
            }
            .box-button {
                margin: .625rem;
                width: 5.8125rem;
                height: 2.5rem;
                background: #FFBA60;
                border-radius: .5rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 500;
                font-size: 1.125rem;
                color: #3D3D3D;
                line-height: 2.5rem;
                text-align: center;
                font-style: normal;
                float: right;
            }
        }
    }
}
</style>
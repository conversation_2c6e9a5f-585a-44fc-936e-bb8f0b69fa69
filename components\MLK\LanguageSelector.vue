<template>
  <div>
    <el-select 
      v-model="value" 
      :loading="loading"
      :teleported="false"
      :placeholder="$t('header.selectLanguage')" 
      style="width: 140px;border-radius: .625rem;" 
      @change="select"
    >
      <el-option
        v-for="item in languagelists"
        :key="item.code"
        :label="item.name"
        :value="item.code"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { useLanguageStore } from '~/stores/language';

interface LanguageType {
  code: string,
  name: string,
  id: number,
  direction: number,
  created_at: string | null,
  is_default: boolean,
  logo_path: string,
  logo_url: string,
  updated_at: string | null,
  pivot: [channel: number, locale_id: number]
}

const languageStore = useLanguageStore()

const props = defineProps({
  languagelist: {
    type: Array as PropType<LanguageType[]>,
    default: () => []
  },
})

const languagelists = ref<LanguageType[]>([])
const selectedLanguage = ref(languageStore.language)
const selectedLanguageData = ref<LanguageType>()
const isDropdownOpen = ref(false)
const loading = ref(false)

// 初始化选中语言
const value = ref(languageStore.language)

// 初始化时设置i18n语言
onMounted(() => {
  // 确保在客户端环境下执行
  if (import.meta.client) {
    // 初始化时同步语言状态
    const currentLanguage = languageStore.language
    // 如果当前语言不是默认的 'en'，则触发语言切换
    if (currentLanguage !== 'it') {
      languageStore.setLanguage(currentLanguage)
    }
  }
})

// 监听语言列表变化
watch(() => props.languagelist, (newList) => {
  if (newList && newList.length > 0) {
    languagelists.value = newList
    // 更新选中语言数据
    selectedLanguageData.value = languagelists.value.find(item => item?.code === selectedLanguage.value)
    
    // 确保 value 与当前语言同步
    if (languagelists.value.some(item => item.code === selectedLanguage.value)) {
      value.value = selectedLanguage.value
    }
  }
}, { immediate: true })

// 监听选中语言变化
watch(selectedLanguage, (newLang) => {
  selectedLanguageData.value = languagelists.value.find(item => item?.code === newLang)
})

// 语言选择处理
const select = async (code: string) => {
  console.warn('🌐 用户选择语言:', code)
  
  if (!code) {
    console.warn('🌐 语言代码为空，跳过切换')
    return
  }
  
  loading.value = true
  
  try {
    selectedLanguage.value = code
    isDropdownOpen.value = false
    selectedLanguageData.value = languagelists.value.find(item => item?.code === selectedLanguage.value)
    
    // 调用 store 的 setLanguage 方法，这会同时更新 i18n 语言
    languageStore.setLanguage(code)
    
    console.warn('🌐 语言切换成功:', code)
  } catch (error) {
    console.error('🌐 语言切换失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.language-selector {
  position: relative;
  cursor: pointer;
  
  .selected-language {
    border-radius: 5px;
    border: 1px solid #CCCCCC;
    padding: 0 .3125rem;
  }
  
  .language-dropdown {
    width: 100%;
    padding: 0 .3125rem;
    box-sizing: border-box;
    cursor: pointer;
    position: absolute;
    top: 1.4375rem;
    left: 0;
    z-index: 10;
  }
}
</style>
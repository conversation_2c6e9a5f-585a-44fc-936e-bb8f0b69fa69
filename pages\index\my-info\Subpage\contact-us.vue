<template>
 <div class="contactUs-page">
    <div class="contactUs-title">
        <span>Contact Us</span>
    </div>
    <div class="contactUs-tip">
        <span>Email & Whatsapp</span>
    </div>
    <div class="contactUs-info">
        <div class="info-box">
            <div class="contacts">
                <el-icon style="margin-top: .125rem;" color="#2694FF" :size="24"><Stamp /></el-icon>
                <span>joy</span>
            </div>
            <div class="contacts" style="margin-top: .9375rem;">
                <el-icon style="margin-top: .125rem;" color="#2694FF" :size="24"><ChatDotRound /></el-icon>
                <span>861388888888</span>
                <div class="box">
                    <div>Chat with us</div>
                </div>
            </div>
        </div>
    </div>
    <div class="email-title">
        <span>Email Us</span>
    </div>
    <div class="email-input">
        <div class="input-title">How can we help you?</div>
        <div style="margin-left: 2.5rem;height: 21.5rem;">
            <el-row>
                <el-col :span="12"><el-input v-model="input" style="width: 31.6875rem;height: 3.625rem;" placeholder="First Name" /></el-col>
                <el-col :span="12"><el-input v-model="input" style="width: 31.6875rem;height: 3.625rem;" placeholder="First Name" /></el-col>
            </el-row>
            <el-row style="margin-top: 1.25rem;">
                <el-col :span="24">
                    <el-input v-model="input" style="width: 65.625rem;height: 3.625rem;" placeholder="First Name" />
                </el-col>
            </el-row>
            <el-row style="margin-top: 1.25rem;">
                <el-col :span="24">
                    <el-input v-model="input" style="width: 65.625rem;height: 3.625rem;" placeholder="Email" />
                </el-col>
            </el-row>
            <el-row style="margin-top: 1.25rem;">
                <el-col :span="24">
                    <el-input  v-model="input" style="width: 65.625rem;height: 3.625rem;" type="textarea" :rows="4" placeholder="Demands" />
                </el-col>
            </el-row>
        </div>
        <div class="tips">
            Tips: Please do not include your credit card number or CVV in this message.  
        </div>
    </div>
    <div class="button">
        Send Email
    </div>
 </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const input = ref('')
</script>

<style scoped lang="scss">
.contactUs-page {
    width: 100%;
    height: 60.1875rem;
    background: #FFFFFF;
    border-radius: .5rem;
    border: .0625rem solid #EBEBEB;
    .contactUs-title {
        padding: 1.25rem 0;
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }
    .contactUs-tip {
        width: 100%;
        height: 3.125rem;
        background-color: #EBEBEB;
        border: .0625rem solid #EBEBEB;
        span {
            margin-left: 2.375rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1.125rem;
            color: #3D3D3D;
            line-height: 3.125rem;
            text-align: left;
            font-style: normal;
        }
    }
    .contactUs-info {
        height: 6.25rem;
        background: #F8FCFF;
        border: .0625rem solid #EBEBEB;
        .info-box{
            margin: 1.25rem 0 0 2.375rem;
            // margin-left: 2.375rem;
            .contacts {
                height: 1.5rem;
                display: flex;
               span {
                    margin-left: .625rem;
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 600;
                    font-size: 1.125rem;
                    color: #3D3D3D;
                    line-height: 1.5rem;
                    text-align: left;
                    font-style: normal;
               }
               .box {
                    margin-left: .625rem;
                    width: 6.1875rem;
                    height: 1.5rem;
                    border-radius: .3125rem;
                    border: .0625rem solid #2694FF;
                    div  {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: .875rem;
                        color: #2694FF;
                        line-height: 1.5rem;
                        text-align: center;
                        font-style: normal;
                    }
                }
            }
        }
    }
    .email-title {
        height: 3.625rem;
        background: #F7F7F7;
        span {
            margin-left: 2.5rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1.25rem;
            line-height: 3.625rem;
            color: #3D3D3D;
            text-align: left;
            font-style: normal;
        }
    }
    .email-input {
        .input-title {
            margin: 1.25rem 0 1.25rem 2.5rem;
            height: 1.25rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 400;
            font-size: 1rem;
            color: #3D3D3D;
            line-height: 1.25rem;
            text-align: left;
            font-style: normal;
        }
        .tips {
            margin-left: 2.5rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 400;
            font-size: 1rem;
            color: #3D3D3D;
            text-align: left;
            font-style: normal;
        }
    }
    .button {
        margin: 3.125rem auto;
        width: 12.25rem;
        height: 3.6875rem;
        background: #3D3D3D;
        border-radius: .5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 1.125rem;
        color: #FFFFFF;
        line-height: 3.6875rem;
        text-align: center;
        font-style: normal;
    }
}
</style>
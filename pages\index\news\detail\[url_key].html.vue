<template>
    <div class="news_details_page">
        <!-- 标头 -->
        <MLKTechnical />
        <div class="news_details_nav">
            <!-- 导航条 -->
            <el-breadcrumb separator="/" style="margin-top: 1.25rem;">
                <el-breadcrumb-item :to="{ path: '/home' }">{{ $t('news.detail.home') }}</el-breadcrumb-item>
                <el-breadcrumb-item :to="{ path: '/news' }">
                    {{ $t('news.detail.news') }}
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="news_details_content">
            <div class="content_title">
                <!-- 标题 -->
                <MLKTitle :title="newsData?.title" />
                <!-- 分割线 -->
                <el-divider style="width: 75rem;margin: 0 auto;" />
                <div class="content_title_times">
                    <div v-for="item in newsData?.tags" :key="item.id" class="label">{{ item.name }}</div>
                    <div class="times" style="margin-left: 1.875rem;">
                        <el-icon style="margin-top: .3125rem;" :size="24">
                            <Clock />
                        </el-icon>
                        <div>{{ newsData?.published_at }}</div>
                    </div>
                    <div class="share">
                        <el-icon style="margin: .3125rem 0 0 .25rem;" :size="24" color="white">
                            <Share />
                        </el-icon>
                    </div>
                </div>
            </div>
            <div class="content_text">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <div class="news-content" v-html="sanitizedContent"/>
            </div>
        </div>
        <!-- 分割线 -->
        <el-divider style="margin: 1.875rem auto;" />
        <!-- 相关文章模块 -->
        <div class="news_RelatedArticles">
            <!-- 标题 -->
            <MLKTitle :title="$t('news.detail.relatedArticles')"  style="margin-top: 5rem;"/>
            <div class="RelatedArticles_content">
                <MLKNewsRelatedArticles :related-news-data="newsData?.related_posts || []" />
            </div>
        </div>
        <!-- 分割线 -->
        <el-divider style="margin: 6.25rem auto;" />
        <!-- 购物卡片模块 -->
        <div class="shopping_card">
            <!-- 标题 -->
            <MLKTitle :title="$t('news.detail.youMayAlsoLike')" />
            <el-divider style="width:75rem;margin: 1.875rem auto;" />
            <div class="card_text">
                <MLKProductCard 
                    v-for="item in newsData?.recommended_products" 
                    :key="item.id" 
                    :product-data="item" 
                    @add-to-cart="handleAddToCart"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getNewDetail  } from '~/api/news'

// 导入购物车store
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

// 定义相关文章类型
interface RelatedPost {
    id: number
    title: string
    url_key: string
    description: string
    thumbnail: string
    published_at: string
    tags: {
        id: number
        name: string
        slug: string
    }[]
}

// 定义新闻详情数据类型
interface NewsDetail {
    id: number
    title: string
    content: string
    html_content?: string
    description: string
    published_at: string
    tags: {
        id: number
        name: string
        slug: string
    }[]
    thumbnail: string
    url_key: string
    related_posts?: RelatedPost[]
    recommended_products?: {
      id: number
        url_key: string
        name: string
        base_image: string
        formatted_price: string
        short_description: string
    }[]
}

// 页面名称
definePageMeta({
    name: 'detail'
})
const route = useRoute()
const url_key = route.params.url_key

// 获取语言事件总线
const { onLanguageChange } = useLanguageEventBus()

// 定义响应式数据
const newsData = ref<NewsDetail | null>(null)

// HTML 内容安全处理函数
const sanitizeHtml = (html: string): string => {
    if (!html) return ''
    
    // 创建一个临时的 DOM 元素来解析 HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    
    // 移除所有 script 标签
    const scripts = tempDiv.querySelectorAll('script')
    scripts.forEach(script => script.remove())
    
    // 移除所有事件处理器属性
    const allElements = tempDiv.querySelectorAll('*')
    allElements.forEach(element => {
        const attrs = element.getAttributeNames()
        attrs.forEach(attr => {
            if (attr.startsWith('on') || attr.startsWith('javascript:')) {
                element.removeAttribute(attr)
            }
        })
    })
    
    return tempDiv.innerHTML
}

// 计算属性：安全的内容
const sanitizedContent = computed(() => {
    if (!newsData.value) return ''
    
    // 优先使用 html_content，如果没有则使用 content
    const content = newsData.value.html_content || newsData.value.content || ''
    
    // 在客户端环境下进行安全处理
    if (import.meta.client) {
        return sanitizeHtml(content)
    }
    
    // 服务端环境下直接返回内容（服务端渲染时相对安全）
    return content
})

// 添加路由参数验证
const validateRouteParams = () => {
    if (!url_key) {
        // 参数无效时跳转到新闻列表页
        navigateTo('/news')
        return false
    }
    return true
}

const getNewDetailData = async () => {
  if (import.meta.client) {
    await nextTick()
  }
  try {
      if (!validateRouteParams()) return
      const res = await getNewDetail({ url_key: url_key as string })
      // 处理响应数据
      if (res && res.success) {
          // 这里可以设置响应式数据
        newsData.value = res.data.news
      }
  } catch {
      // 可以添加错误提示或跳转逻辑
      ElMessage.error($t('news.getNewsFailed'))
  }
}

// 获取购物车store实例
const cartStore = useShoppingCartStore()

// 处理添加购物车
const handleAddToCart = async (product: {
    id: number
    name: string
    base_image: string
    formatted_price: string
    short_description: string
    product_id?: string
    price?: number
    selectedColor?: string
}) => {
    try {
        // 调用购物车store的添加方法
        const result = await cartStore.addToCart({
            id: product.id.toString(),
            product_id: product.product_id || product.id.toString(),
            name: product.name,
            price: product.price || parseFloat(product.formatted_price.replace(/[^0-9.]/g, '')),
            base_image: product.base_image,
            formatted_price: product.formatted_price,
            short_description: product.short_description,
            selectedColor: product.selectedColor
        }, 1)
        
        if (result.success) {
            // 显示成功提示
            ElMessage.success($t('news.detail.addToCartSuccess'))
        } else {
            // 显示错误提示
            ElMessage.error(result.message || $t('news.detail.addToCartFailed'))
        }
    } catch {
        ElMessage.error($t('news.detail.addToCartError'))
    }
}

// 监听语种变化并重新获取数据
onLanguageChange(async () => {
    await getNewDetailData()
})

onMounted(() => {
    getNewDetailData()

})

</script>

<style scoped lang="scss">
.news_details_page {
    width: 100%;
    // height: 100rem;

    .news_details_nav {
        margin: .625rem auto;
        width: 75rem;
        height: 3.125rem;
    }

    .news_details_content {
        margin: .625rem auto;
        width: 75rem;

        .content_title {
            // height: 9.375rem;

            .content_title_times {
                margin-top: 1.25rem;
                width: 100%;
                height: 2.1875rem;
                display: flex;
                gap: 1.25rem;
                .label {
                    width: 5.375rem;
                    height: 100%;
                    background-color: #3D3D3D;
                    border-radius: .5rem;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 2.1875rem;
                    text-align: center;
                    font-style: normal;
                }

                .times {
                    height: 100%;
                    line-height: 2.1875rem;
                    display: flex;

                    div {
                      margin-left: .9375rem;
                      font-family: PingFangSC, PingFang SC;
                      font-weight: 500;
                      font-size: 1rem;
                      color: #565656;
                      line-height: 2.1875rem;
                      text-align: left;
                      font-style: normal;
                    }
                }

                .share {
                    width: 2.1875rem;
                    height: 2.1875rem;
                    background-color: black;
                    border-radius: 50%;
                    margin-left: auto;
                    margin-right: 0;
                }
            }
        }

        .content_text {
            margin: 1.25rem 0;
            
            .news-content {
                font-family: PingFangSC, PingFang SC;
                font-size: 1rem;
                line-height: 1.6;
                color: #333;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
        }
    }
    .news_RelatedArticles {
        margin: 1.25rem auto;
        width: 75rem;
        // height: 100px;
        // background-color: #3D3D3D;
        .RelatedArticles_content {
            margin-top: .625rem;
        }
    }
    .shopping_card{
        margin: 1.25rem auto;
        width: 84.75rem;
        height: 62.5rem;
        .card_text {
            margin-top: 3.125rem;
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
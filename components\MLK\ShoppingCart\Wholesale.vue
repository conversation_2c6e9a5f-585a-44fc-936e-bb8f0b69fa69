<template>
  <div class="cart-item">
    <div class="product-title">
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        {{ cartItemsWithCheck[0].name }}
      </el-checkbox>
    </div>
    <el-checkbox-group v-model="checkList" @change="handleCheckedChange">
      <div v-for="item in cartItemsWithCheck" :key="item.cart_item_id" class="shopping-item">
        <div class="shopping-item-left">
          <el-checkbox :value="item.cart_item_id" />
          <div class="img">
            <img :src="item.product.base_image" :alt="item.product.name">
          </div>
          <div class="product">
            <div class="name">
              {{ item.attributes?.brand?.option_label || t('shoppingCartRetail.brandEmpty') }}
            </div>
            <div class="text">
               {{ t('shoppingCartRetail.color') }}:{{ item.attributes?.device?.option_label || t('shoppingCartRetail.deviceEmpty') }} ({{ item.attributes?.color?.option_label || t('shoppingCartRetail.colorEmpty') }})
              <span>US：{{ item.formatted_base_price }}</span>
            </div>
          </div>
        </div>
        <div class="shopping-item-right">
          <div class="count-box">
            <div class="count-box-top">
              <div class="minus" @click="decreaseQuantity(item as any)">
                <el-icon :size="14" style="margin-top: .3125rem;"><Minus /></el-icon>
              </div>
              <input 
                v-model="item.quantity"
                type="number" 
                min="1" 
                max="999" 
                @change="updateQuantity(item)"
                @blur="validateQuantity(item)"
                @keyup.enter="updateQuantity(item)"
              >
              <div class="add" @click="increaseQuantity(item as any)">
                <el-icon :size="14" style="margin-top: .3125rem;"><Plus /></el-icon>
              </div>
            </div>
            <div class="price">
              {{ t('shoppingCartRetail.subtotal') }}:US$ {{ (item.price * item.quantity).toFixed(2) }}
            </div>
          </div>
          <div class="delect" @click="removeItem(item)">
            <img src="~/assets/images/shopping/delect.png" :alt="t('shoppingCartRetail.delete')">
          </div>
        </div>
      </div>
    </el-checkbox-group>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { CheckboxValueType } from 'element-plus'
import { useShoppingCartStore } from '~/stores/shopping-cart'

// I18n
const { t } = useI18n()

const props = defineProps({
  item: {
    type: Array,
    default: () => [],
  },
  checkAlls: {
    type: Boolean,
    default: false,
  },
  checkIsIndeterminate: {
    type: Boolean,
    default: false,
  },
})
console.warn(props.item)
const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkList = ref<string[]>([])
// 监听checkAlls
watch(
  () => props.checkAlls,
  (newVal: boolean) => {
    if (!props.checkIsIndeterminate) {
      checkAll.value = newVal
      checkList.value = newVal ? cartItemsWithCheck.value.map(item => item.cart_item_id) : []
      isIndeterminate.value = false
    }
  }
)

const emit = defineEmits(['checkListChange','update-cart'])
watch(checkList, (newVal: string[]) => {
  emit('checkListChange', newVal)
})

// 定义购物车商品类型
interface CartItemWithCheck {
  cart_item_id: string
  quantity: number
  sku: string
  type: string
  name: string
  price: number
  base_price: number
  total: number
  base_total: number
  tax_percent: number
  weight:string
  tax_amount: number
  base_tax_amount: number
  discount_percent: number
  discount_amount: number
  base_discount_amount: number
  product_id: number
  product: {
    product_id: number
    type: string
    name: string
    url_key: string
    base_image: string
  }
  attributes?: {
    color?: {
      option_label?: string
    }
    brand?: {
      option_label?: string
    }
    device?: {
      option_label?: string
    }
  }
  formatted_price: string
  formatted_base_price: string
  formatted_total: string
  formatted_base_total: string
}

// const items = ref([
//   { id: 1, name: 'Apple iPhone 11', color: 'Black Obsidian(Black)', price: 1, img: '~/assets/images/shopping/1.png', count: 1 },
//   { id: 2, name: 'Apple iPhone 12', color: 'White', price: 2, img: '~/assets/images/shopping/1.png', count: 1 },
//   { id: 3, name: 'Apple iPhone 13', color: 'Red', price: 3, img: '~/assets/images/shopping/1.png', count: 1 },
// ])

// 购物车store
const cartStore = useShoppingCartStore()

const cartItemsWithCheck = computed<CartItemWithCheck[]>(() => {
  const data = props.item && props.item.length > 0 ? props.item : cartStore.cartItems
  return (data as CartItemWithCheck[]).map(item => {
    return { ...item }
  })
})
console.warn('cartItemsWithCheck',cartItemsWithCheck.value);


const handleCheckAllChange = (val: CheckboxValueType) => {
  checkList.value = val ? cartItemsWithCheck.value.map(item => item.cart_item_id) : []
  isIndeterminate.value = false
}

const handleCheckedChange = (val: CheckboxValueType[]) => {
  checkAll.value = val.length === cartItemsWithCheck.value.length
  isIndeterminate.value = val.length > 0 && val.length < cartItemsWithCheck.value.length
}

// 移除商品
const removeItem = async (item: CartItemWithCheck) => {
  try {
    await ElMessageBox.confirm(t('shoppingCartRetail.removeConfirmText'), t('shoppingCartRetail.removeConfirm'), {
      confirmButtonText: t('shoppingCartRetail.delete'),
      cancelButtonText: t('shoppingCartRetail.remove'),
      type: 'warning'
    })
    
    const result = await cartStore.removeFromCart(item.cart_item_id)
    if (result && result.success) {
      ElMessage.success(result.message || t('shoppingCartRetail.removeSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.removeFailed'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除购物车商品失败:', error)
      ElMessage.error(t('shoppingCartRetail.removeFailed'))
    }
  }
}

const validateQuantity = (item: CartItemWithCheck) => {
  const newQuantity = parseInt(item.quantity.toString())
  if (isNaN(newQuantity) || newQuantity < 1) {
    item.quantity = 1
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return false
  }
  
  // 限制最大数量为999
  if (newQuantity > 999) {
    item.quantity = 999
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return false
  }
  
  return true
}

const updateQuantity = async (item: CartItemWithCheck) => {
  if (!validateQuantity(item)) {
    return
  }
  
  const newQuantity = parseInt(item.quantity.toString())
  
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, newQuantity)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.updateSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.updateFailed'))
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error(t('shoppingCartRetail.updateFailed'))
  }
}
// 增加数量
const increaseQuantity = async (item: CartItemWithCheck) => {
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, item.quantity + 1)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.updateSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.updateFailed'))
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error(t('shoppingCartRetail.updateFailed'))
  }
}
// 减少数量
const decreaseQuantity = async (item: CartItemWithCheck) => {
   if (item.quantity <= 1) {
    ElMessage.warning(t('shoppingCartRetail.quantityError'))
    return
  }
  
  try {
    const result = await cartStore.updateQuantity(item.cart_item_id, item.quantity - 1)
    if (result.success) {
      ElMessage.success(t('shoppingCartRetail.updateSuccess'))
      emit('update-cart')
    } else {
      ElMessage.error(result.message || t('shoppingCartRetail.updateFailed'))
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error(t('shoppingCartRetail.updateFailed'))
  }
}

</script>

<style lang="scss" scoped>
.empty-cart {
  background: #FFFFFF;
  border-radius: .9375rem;
  padding: 3rem;
  text-align: center;
  margin: 1.5rem auto;
  width: 48.1875rem;
  
  .empty-message {
    .empty-icon {
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
    
    p {
      font-size: 1.125rem;
      color: #666;
      margin-bottom: 1.5rem;
    }
  }
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
    font-family: SFProDisplay, SFProDisplay;
    font-weight: 600;
    font-size: 1.125rem;
    color: #000000;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
::v-deep .el-checkbox__label{
  font-family: SFProDisplay, SFProDisplay;
    font-weight: 500;
    font-size: 1.125rem;
    color: #000000;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
.cart-item{
  margin-top: 1.5rem;
  width: 48.1875rem;
  border-bottom: 1px solid #EBEBEB;
  .shopping-item{
    margin: 1.125rem 0;
    width: 48.1875rem;
    height: 3.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .shopping-item-left{
      display: flex;
      align-items: center;
      .img{
        width: 3.625rem;
        height: 3.625rem;
        margin-left: 1.0625rem;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .product{
        height: 100%;
        margin-left: 1rem;
        .name{
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .text{
          margin-top: 1rem;
          height: 1.25rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: .875rem;
          color: #959595;
          line-height: 1.25rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .shopping-item-right{
      display: flex;
      align-items: center;
      .count-box{
        .count-box-top{
          width: 7.625rem;
          height: 2rem;
          border-radius: .25rem;
          margin: 0 auto;
          border: 1px solid #D9D9D9;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          padding: 0 .5625rem;
          overflow: hidden;
          .minus{
            width: 1.5rem;
            height: 1.5rem;
            line-height: 1.5rem;
            text-align: center;
            cursor: pointer;
          }
          input{
            width: 2.5rem;
            text-align: center;
            border: none;
            outline: none;
          }
          .add{
             width: 1.5rem;
            height: 1.5rem;
            line-height: 1.5rem;
            text-align: center;
            cursor: pointer;
          }
        }
        .price{
          margin-top: .5rem;
          height: 1.25rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .875rem;
          color: #000000;
          line-height: 1.25rem;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
      .delect{
        width: 1.5rem;
        height: 1.5rem;
        margin-left: 1rem;
        cursor: pointer;
        img{
          width: 100%;
          height: 100%;
        }
      }
    }


  }
}
</style>
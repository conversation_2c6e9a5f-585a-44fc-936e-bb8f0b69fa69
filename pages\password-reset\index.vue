<template>
  <NuxtLayout>
    <div class="login-container">
        <div class="login-content">
            <div class="login-title">
                <span>Forgot Password with MLK+</span>
            </div>
            <div class="login-input">
                <el-form ref="ruleFormRef" :model="form" label-width="auto" :rules="rules">
                    <div class="login-box">
                        <span>Email</span>
                        <div class="login-email">
                             <el-form-item prop="email">
                                <el-input v-model="(form as any).email" style="height: 3.5rem;" />
                             </el-form-item>
                        </div>
                    </div>
                </el-form>
                <div class="remember-me" style="margin-top: 1.75rem;">
                    <a href="/login" style="float: right;color: #262626;">Login?</a>
                </div>
            </div>
            <div class="login-button">
                <el-button 
                    type="primary" 
                    class="login-button-style" 
                    @click="resetPassword(ruleFormRef)"
                >
                    Reset Password
                </el-button>
            </div>
            <div class="login-info">
                <span>New to MLK+? </span>
                <div class="login-info-link"><a href="/register">Create an Account</a></div>
                <div class="login-info-link"><a href="/register-wholesale">Create a Wholesale Account</a></div>
            </div>
        </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import type { FormRules,FormInstance } from 'element-plus'
import { forgotPassword } from '@/api/user'

definePageMeta({
  layout:"personal-center"  
})
interface formType { 
  email: string
}

const form = ref<formType>({
  email: ''
})

const rules = ref<FormRules<formType>>({
  email: [
    { required: true, message: 'Please input email', trigger: 'blur' },
    { type: 'email', message: 'Please enter the correct email address', trigger: 'blur' }
  ]
})

const ruleFormRef = ref<FormInstance>()

const resetPassword = async (formEl: FormInstance | undefined) => { 
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            try {
                const res = await forgotPassword({ email: form.value.email })
                if(res.success){
                    ElMessageBox.alert(res.message, 'success', { type: 'success' })
                }else{
                    ElMessageBox.alert(res.message, 'error', { type: 'error' })
                }
            } catch (error) {
                ElMessageBox.alert((error as Error).message, 'error', { type: 'error' })
            }
        } else {
            console.warn('error submit!', fields)
        }
    })
}


</script>


<style lang="scss" scoped>
// 输入框内容样式
::v-deep .el-input__wrapper {
    background-color: #F6F6F6;
}
::v-deep .el-input__inner {
    font-size: 1.0625rem;
}
::v-deep .el-icon svg {
    width: 1.125rem;
    height: 1.125rem;
}
::v-deep .el-checkbox.el-checkbox--large .el-checkbox__label {
    font-family: Helvetica;
    font-size: 1rem;
    color: #262626;
    text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
    font-style: normal;
    text-transform: none;
}
::v-deep .el-checkbox__inner:hover{
    border-color: #262626;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
    background-color: #262626;
}
.login-container {
  width: 100%;
  height: 100vh;
  background-color: #F1F1F1;
  display: grid;
  place-items: center;
  .login-content {
    width: 35.75rem;
    background-color: #FFFFFF;
    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
    border-radius: .9375rem;
    .login-title{
        width: 24.875rem;
        height: 3.5rem;
        margin: 2.0625rem auto;
        font-family: Arial, Arial;
        font-weight: 900;
        font-size: 2.5rem;
        color: #262626;
        line-height: 3.5rem;
        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
        text-align: left;
        font-style: normal;
    }
    .login-input{
        margin: 4.4375rem auto;
        width: 24.875rem;
        .login-box {
            width: 100%;
            span {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: .875rem;
                color: #262626;
                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                text-align: left;
                font-style: normal;
            }
            .login-email {
                margin-top: .75rem;
                font-size: 1rem;
            }
        }
        .remember-me{ 
            height: 2.5rem;
            line-height: 2.5rem;
            a {
                font-family: Helvetica;
                font-size: 1rem;
                color: #262626;
                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .login-button{
        margin: 1.625rem auto;
        width: 20.0625rem;
        height: 3.5rem;
        .login-button-style{
            width: 100%;
            height: 100%;
            background: #121212;
            box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
            border-radius: .5rem;
            transition: all 0.15s ease-in-out;
            &:hover {
                background: #2a2a2a;
                box-shadow: 0px .375rem .75rem 0px rgba(0,0,0,0.15);
            }
            &:active {
                background: #0a0a0a;
                box-shadow: 0px .125rem .25rem 0px rgba(0,0,0,0.2);
                transform: translateY(1px);
            }
            &:disabled {
                background: #666666;
                cursor: not-allowed;
                opacity: 0.6;
                &:hover {
                    background: #666666;
                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                    transform: none;
                }
                &:active {
                    background: #666666;
                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                    transform: none;
                }
            }
        }
    }
    .login-info {
        margin: 2.4375rem auto;
        width: 20.0625rem;
        height: 3.5rem; 
        font-family: PingFangSC, PingFang SC;
        text-align: center;
        font-weight: 400;
        font-size: 1rem;
        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
        font-style: normal;
        span {
            color: #999999;
        }
        .login-info-link{
            margin-top: .3125rem;
            a {
                color: #262626;
                text-decoration-line: underline;
            }
        }
    }
  }
}
</style>
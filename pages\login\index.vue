<template>
  <NuxtLayout>
    <div class="login-container">
        <div class="login-content">
            <div class="login-title">
                <span>Sign In with MLK+</span>
            </div>
            <div class="login-input">
                <el-form ref="ruleFormRef" :model="form" label-width="auto" :rules="rules">
                    <div class="login-box">
                        <span>Email</span>
                        <div class="login-email">
                             <el-form-item prop="email">
                                <el-input v-model="(form as any).email" style="height: 3.5rem;" placeholder="Enter your email" />
                             </el-form-item>
                        </div>
                    </div>
                     <div class="login-box" style="margin-top: 1.75rem;">
                        <span>Password</span>
                        <div class="login-email">
                             <el-form-item prop="password">
                                <el-input v-model="(form as any).password" type="password" style="height: 3.5rem;" show-password/>
                             </el-form-item>
                        </div>
                    </div>
                </el-form>
                <div class="remember-me" style="margin-top: 1.75rem;">
                    <el-checkbox v-model="rememberMe" label="Remember me" size="large" />
                    <a href="/password-reset" style="float: right;color: #262626;">Forgot Password?</a>
                </div>
            </div>
            <div class="login-button">
                <el-button 
                    type="primary" 
                    class="login-button-style" 
                    :loading="store.loading"
                    :disabled="store.loading"
                    @click="userLogin(ruleFormRef)"
                >
                    {{ store.loading ? 'logging in...' : 'Sign In' }}
                </el-button>
            </div>
            <div class="login-info">
                <span>New to MLK+? </span>
                <div class="login-info-link"><a href="/register">Create an Account</a></div>
                <div class="login-info-link"><a href="/register-wholesale">Create a Wholesale Account</a></div>
            </div>
        </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">

import { useUserStore } from '~/stores/user'
import type { FormRules,FormInstance } from 'element-plus'

definePageMeta({
  layout:"personal-center"  
})
interface formType { 
  email: string,
  password: string
}

const form = ref<formType>({
  email: '',
  password: ''
})

const rules = ref<FormRules<formType>>({
  email: [
    { required: true, message: 'Please input email', trigger: 'blur' },
    { type: 'email', message: 'Please enter the correct email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please input password', trigger: 'blur' },
    { min: 6, max: 16, message: 'Password length must be 6-16 characters', trigger: 'blur' }
  ]
})

const ruleFormRef = ref<FormInstance>()

interface loginResType {
    data?: {
        expires_at?: string,
        token?: string,
        customer?: {
            created_at?: string,
            date_of_birth?: string,
            email?: string,
            first_name?: string,
            gender?: string,
            id?: string,
            status?: number,
            profile?: string,
            phone?: string,
            name?: string,
            last_name?: string,
            image_url?: string,
            is_wholesale?: string,
            group?: {
                code?: string,
                created_at?: string,
                is_user_defined?: number,
                name?: string,
                updated_at?: string,
            }
        }
    },
    message?: string,
    success?: boolean
}

const router = useRouter()

const userLogin = async (formEl: FormInstance | undefined) => { 
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            try {
                // 使用 store 中的 userLogin 方法
                const res = await store.userLogin(form.value as formType) as unknown as loginResType
                
                if (res && res.success) {
                    // 登录成功后的处理
                    if (res.data) {
                        // 存储token和过期时间
                        if (res.data.token) {
                            store.setToken(res.data.token)
                            // 同时保存到localStorage
                            localStorage.setItem('token', res.data.token)
                        }
                        if (res.data.expires_at) {
                            store.setExpiresAt(res.data.expires_at)
                            // 同时保存到localStorage
                            localStorage.setItem('expires_at', res.data.expires_at)
                        }
                        // 使用 store 中的 setUserInfo 方法设置用户信息
                        store.setUserInfo(res.data.customer ?? {})
                        // 使用 store 中的 setLoginStatus 方法设置登录状态
                        store.setLoginStatus(true)
                    }
                    
                    router.push('/home')
                    console.warn('用户信息', store.userInfo)
                    
                    if (rememberMe.value) { 
                        // 保存账号密码到缓存
                        localStorage.setItem('savedEmail', form.value.email)
                        localStorage.setItem('savedPassword', form.value.password)
                        localStorage.setItem('rememberMe', 'true')
                    } else {
                        // 清除保存的账号密码
                        localStorage.removeItem('savedEmail')
                        localStorage.removeItem('savedPassword')
                        localStorage.removeItem('rememberMe')
                    }
                    
                    // 登录成功提示
                    ElMessage.success(res.message || '登录成功')
                } else {
                    ElMessage.error(res.message || '登录失败')
                }
            } catch (error) {
                // 处理异常
                console.error('登录失败', error)
                ElMessage.error('登录失败，请检查邮箱和密码')
            }
        } else {
            console.warn('error submit!', fields)
        }
    })
}

const rememberMe = ref<boolean>(false)

// 获取 store 实例
const store = useUserStore()

// 页面加载时检查是否已登录
onMounted(() => {
    // 如果已经登录，直接跳转到首页
    // if (store.isLoggedIn) {
    //     router.push('/home')
    // }
    
    // 使用新的token状态初始化方法
    store.initializeTokenState()
    
    // 检查是否有保存的账号密码
    const savedRememberMe = localStorage.getItem('rememberMe')
    if (savedRememberMe === 'true') {
        rememberMe.value = true
        const savedEmail = localStorage.getItem('savedEmail')
        const savedPassword = localStorage.getItem('savedPassword')
        
        if (savedEmail) {
            form.value.email = savedEmail
        }
        if (savedPassword) {
            form.value.password = savedPassword
        }
    }
})

// 监听"记住我"状态变化
watch(rememberMe, (newValue) => {
    if (!newValue) {
        // 当用户取消勾选"记住我"时，清除已保存的账号密码
        localStorage.removeItem('savedEmail')
        localStorage.removeItem('savedPassword')
        localStorage.removeItem('rememberMe')
    }
})

</script>


<style lang="scss" scoped>
// 输入框内容样式
::v-deep .el-input__wrapper {
    background-color: #F6F6F6;
}
::v-deep .el-input__inner {
    font-size: 1.0625rem;
}
::v-deep .el-icon svg {
    width: 1.25rem;
    height: 1.25rem;
}
::v-deep .el-input .el-input__icon{
    width: 1.25rem;
    height: 1.25rem;
}
::v-deep .el-checkbox.el-checkbox--large .el-checkbox__label {
    font-family: Helvetica;
    font-size: 1rem;
    color: #262626;
    text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
    font-style: normal;
    text-transform: none;
}
::v-deep .el-checkbox__inner:hover{
    border-color: #262626;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
    background-color: #262626;
}
::v-deep .el-checkbox__inner:after{
    border:.125rem solid transparent;
    border-left: 0;
    border-top: 0;
}
.login-container {
  width: 100%;
  height: 130vh;
  background-color: #F1F1F1;
  display: grid;
  place-items: center;
  .login-content {
    // margin: 13.75rem auto 23.4375rem auto;
    width: 35.75rem;
    height: 51.25rem;
    background-color: #FFFFFF;
    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
    border-radius: .9375rem;
    .login-title{
        width: 24.875rem;
        height: 3.5rem;
        margin: 2.0625rem auto;
        font-family: Arial, Arial;
        font-weight: 900;
        font-size: 2.5rem;
        color: #262626;
        line-height: 3.5rem;
        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
        text-align: left;
        font-style: normal;
    }
    .login-input{
        margin: 4.4375rem auto;
        width: 24.875rem;
        height: 15.625rem;
        // background-color: #F6F6F6;
        .login-box {
            width: 100%;
            span {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: .875rem;
                color: #262626;
                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                text-align: left;
                font-style: normal;
            }
            .login-email {
                margin-top: .75rem;
                font-size: 1rem;
            }
        }
        .remember-me{ 
            height: 2.5rem;
            line-height: 2.5rem;
            a {
                font-family: Helvetica;
                font-size: 1rem;
                color: #262626;
                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                font-style: normal;
                text-transform: none;
            }
            // color:#262626
        }
    }
    .login-button{
        margin: 10.625rem auto 0 auto;
        width: 20.0625rem;
        height: 3.5rem;
        .login-button-style{
            width: 100%;
            height: 100%;
            background: #121212;
            box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
            border-radius: .5rem;
            transition: all 0.15s ease-in-out;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 1.125rem;
            color: #FFFFFF;
            letter-spacing: .125rem;
            text-align: left;
            font-style: normal;
            &:hover {
                background: #2a2a2a;
                box-shadow: 0px .375rem .75rem 0px rgba(0,0,0,0.15);
            }
            
            &:active {
                background: #0a0a0a;
                box-shadow: 0px .125rem .25rem 0px rgba(0,0,0,0.2);
                transform: translateY(1px);
            }
            
            &:disabled {
                background: #666666;
                cursor: not-allowed;
                opacity: 0.6;
                
                &:hover {
                    background: #666666;
                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                    transform: none;
                }
                
                &:active {
                    background: #666666;
                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                    transform: none;
                }
            }
        }
    }
    .login-info {
        margin: 2.4375rem auto;
        width: 20.0625rem;
        height: 3.5rem; 
        font-family: PingFangSC, PingFang SC;
        text-align: center;
        font-weight: 400;
        font-size: 1rem;
        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
        font-style: normal;
        span {

            color: #999999;
        }
        .login-info-link{
            margin-top: .3125rem;
            a {
                color: #262626;
                text-decoration-line: underline;
            }
        }
    }
  }
}
</style>
<template>
  <div class="footer-newsletter">
    <div class="newsletter-title">{{ $t('subscribe.title') }}</div>
    <div class="newsletter-desc">{{ $t('subscribe.description') }}</div>
    <div class="newsletter-policy">
      {{ $t('subscribe.policy') }}
    </div>

    <div class="newsletter-input-container">
      <!-- <el-from 
      style="height: 4rem;
      line-height: 4rem;
    width: 27.5rem;"
        :model="ruleForm"
        label-width="auto">
        <el-form-item
prop="email" :rules="[
          {
            required: true,
            type: 'email',
            message: $t('subscribe.emailValidation'),
            trigger: ['blur', 'change'],
          },
        ]"> -->
          <el-input
  v-model="ruleForm.email"  class="newsletter-input"
        type="email" placeholder-class="newsletter-input-placeholder"
                  :placeholder="$t('subscribe.emailPlaceholder')"/>
        <!-- </el-form-item>
      </el-from> -->
      
      
      <el-button class="newsletter-subscribe-btn" @click="subscribe">{{ $t('subscribe.signUp') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';
const ruleForm = ref({
  email:''
})
const emit = defineEmits(['message-to-parent']);
const subscribe = () => {
  emit('message-to-parent', ruleForm.value.email);
  ruleForm.value.email=''
}

</script>

<style lang="scss" scoped>

.footer-newsletter {
  width: 100%;
  padding: 0 0 5.63rem 0;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .newsletter-title {
    font-family: 'AlimamaShuHeiTi-Bold', sans-serif;
    font-size: 0.9375rem;
    font-weight: 600;
    line-height: 1.13rem;
    letter-spacing: 0.0625rem;
    text-align: center;
    color: #121212;
  }

  .newsletter-desc {
    margin-top: 1.38rem;
    font-family: 'AlimamaShuHeiTi-Bold', sans-serif;
    font-size: 1.875rem;
    font-weight: 600;
    line-height: 2.31rem;

    text-align: center;
    color: #121212;
  }

  .newsletter-policy {
    font-size: 0.94rem;
    font-family: Helvetica, Arial, sans-serif;
    font-weight: 400;
    line-height: 1.13rem;
    letter-spacing: 0.0625rem;
    text-align: center;
    color: #121212;
    margin-top: 1.38rem;
  }

  .newsletter-input-container {
    height: 4rem;
    width: 35.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2.08rem;
    margin-left: auto;
    margin-right: auto;

    .newsletter-input {
      height: 4rem;
      line-height: 4rem;
      flex: 1;
      padding-left: 1rem;
      background-color: #f5f5f5;
      border-radius: 0.5rem 0 0 0.5rem;

    }

    .newsletter-input-placeholder {
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.31rem;
      letter-spacing: 0.13rem;
      color: #656565;
      padding-left: 1rem;

    }

    .newsletter-subscribe-btn {
      height: 100%;
      width: 8rem;
      border-radius: 0 0.5rem 0.5rem 0;
      background-color: #121212;
      color: white;
      font-size: 1.13rem;
      font-family: Helvetica, Arial, sans-serif;
      font-weight: 400;
      line-height: 1.31rem;
      letter-spacing: 0.13rem;
      text-align: center;
    }
  }
}

.newsletter-input :deep(.el-input__wrapper) {
  padding: 0;
  border: none !important;
  box-shadow: none !important;
  background-color: transparent;

}

.newsletter-input :deep(.el-input__inner) {
  border: none !important;
  outline: none !important;
}
</style>
<template>
    <div class="RelatedArticles_page">
        <div v-for="item in relatedNewsData" :key="item.id" class="box" @click="goToNewsDetail(item.id)">
            <div style="width:21.75rem;margin: 1rem 1.25rem;">
                <div class="img">
                    <img :src="item.thumbnail">
                </div>
                <div class="text" :title="item.title">
                    {{ item.title }}
                </div>
                <div class="time">
                    <div class="left">
                      <div class="new_love">
                        <img
                            :src="useAssetsImage('images/new-love.png')"
                        >
                      </div>
                        
                        <span v-for="tag in item.tags" :key="tag.id">{{ tag.slug }}</span>
                    </div>
                    <div class="right">
                        <el-icon :size="24">
                            <Clock />
                        </el-icon>
                        <div class="time_text">{{ item.published_at }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 使用 useAssets composable
const { useAssetsImage } = useAssets()

interface RelatedPost {
    id: number
    title: string
    thumbnail: string
    published_at: string
    tags: {
        id: number
        name: string
        slug: string
    }[]
}

defineProps<{
  relatedNewsData: RelatedPost[]
}>()

// 跳转到新闻详情页面
const router = useRouter()
const goToNewsDetail = (id: number) => {
    router.push(`/news/detail/${id}`)
}
</script>

<style scoped lang="scss">
.RelatedArticles_page {
    width: 100%;
    height: 25rem;
    display: flex;
    gap: 1.25rem;
    .box {
        width: 24.1875rem;
        background: #FFFFFF;
        border-radius: .5rem;
        border: .0625rem solid #EBEBEB;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .img {
          width: 21.75rem;
          height: 13.75rem;
          img{
              width: 100%;
              height: 100%;
          }
        }

        .text {
            margin-top: 1.25rem;
            height: 3.25rem;
            font-weight: 500;
            font-size: 1rem;
            color: #3D3D3D;
            text-align: left;
            font-style: normal;
            display: -webkit-box;
            -webkit-box-orient:vertical;
            -webkit-line-clamp:2;
            line-clamp:2;
            overflow:hidden;
        }
        .time {
            margin-top: 1.25rem;
            height: 1.5rem;
            display: flex;
            justify-content: space-between;
            .left {
                width: 5.625rem;
                font-family: SFProDisplay, SFProDisplay;
                font-weight: 500;
                font-size: .9375rem;
                color: #000000;
                text-align: left;
                font-style: normal;
                display: flex;
                justify-content: space-between;
                .new_love{
                  width: 1.25rem;
                  height: 1.25rem;
                  background: #333333;
                  border-radius: .3125rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  img{
                    width: 80%;
                    height: 80%;
                  }
                }
               
            }
            .right {
                display: flex;
                justify-content: space-between;
                .time_text{
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 500;
                  font-size: 1rem;
                  color: #565656;
                  text-align: left;
                  margin-left: .5625rem;
                  font-style: normal;
                }
            }
        }
    }
}
</style>
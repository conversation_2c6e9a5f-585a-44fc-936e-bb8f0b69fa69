{"hello": "Bonjour", "test": {"title": "Page de test i18n", "description": "Cette page est utilisée pour tester la fonctionnalité d'internationalisation", "languageSwitcher": "<PERSON><PERSON><PERSON><PERSON> de langue", "basicTranslations": "Traductions de base", "currentInfo": "Informations actuelles", "urlInfo": "Informations URL", "currentLocale": "Langue actuelle", "defaultLocale": "Langue par défaut", "availableLocales": "Langues disponibles", "currentUrl": "URL actuelle", "baseUrl": "URL de base", "footer": "Ceci est une page de test pour la fonctionnalité i18n", "pageTitle": "Page de test i18n", "hello": "Bonjour", "welcome": "Bienvenue", "goodbye": "Au revoir"}, "retailCart": {"selectAll": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "itemsTotal": "Articles {count} au total", "orderSummary": "<PERSON><PERSON><PERSON><PERSON> de la commande", "subtotal": "Sous-total", "saved": "Économisé", "tax": "Taxe", "shipping": "<PERSON><PERSON><PERSON>", "total": "Total", "freeShipping": "Livraison gratuite à partir de 79,00 US$", "promotionalCode": "Code promotionnel", "enterCouponCode": "Entrer le code coupon", "selectMyCoupons": "Sélectionner mes coupons", "checkoutNow": "COMMANDER MAINTENANT", "estimateShipping": "<PERSON><PERSON><PERSON><PERSON> les frais de livraison", "shippingWeight": "Poids d'expédition : {weight} kg", "weAccept": "Nous acceptons", "pleaseEnterCouponCode": "Veuillez entrer un code coupon", "couponAppliedSuccessfully": "Coupon appliqué avec succès", "couponApplicationFailed": "Échec de l'application du coupon", "cartIsEmpty": "<PERSON> panier est vide, impossible de commander", "pleaseLoginFirst": "Veuillez d'abord vous connecter", "removeConfirmText": "Êtes-vous sûr de vouloir supprimer tous les articles sélectionnés ?", "removeConfirm": "Confirmer la <PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "wholesaleCart": {"selectAll": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "itemsTotal": "Articles {count} au total", "orderSummary": "<PERSON><PERSON><PERSON><PERSON> de la commande", "subtotal": "Sous-total", "saved": "Économisé", "total": "Total", "freeShipping": "Livraison gratuite à partir de 79,00 US$", "promotionalCode": "Code promotionnel", "enterCouponCode": "Entrer le code coupon", "selectMyCoupons": "Sélectionner mes coupons", "checkoutNow": "COMMANDER MAINTENANT", "estimateShipping": "<PERSON><PERSON><PERSON><PERSON> les frais de livraison", "shippingWeight": "Poids d'expédition : {weight} kg", "weAccept": "Nous acceptons", "pleaseEnterCouponCode": "Veuillez entrer un code coupon", "couponAppliedSuccessfully": "Coupon appliqué avec succès", "couponApplicationFailed": "Échec de l'application du coupon", "cartIsEmpty": "<PERSON> panier est vide, impossible de commander", "pleaseLoginFirst": "Veuillez d'abord vous connecter", "searchProducts": "Rechercher des produits...", "noSearchResults": "Aucun produit trouvé", "searchTip": "Essayez différents mots-clés ou vérifiez votre orthographe", "emptyCart": "Le panier est vide", "goShopping": "Aller faire des courses"}, "header": {"search": "<PERSON><PERSON><PERSON>", "wishlist": "Liste de souhaits", "wholesale": "GROS", "cart": "<PERSON><PERSON>", "user": "Utilisa<PERSON>ur", "selectLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "personalCenter": {"secureCheckout": "PAIEMENT SÉCURISÉ", "user": "Utilisa<PERSON>ur", "continueShopping": "CONTINUER LES ACHATS"}, "home": {"findDevice": "TROUVER VOTRE APPAREIL", "selectBrand": "Sélectionner la marque", "selectDevice": "Sélectionner l'appareil", "shopByBrand": "ACHETER PAR MARQUE", "inspirationText": "Entrez dans notre zone d'inspiration et ressentez le pouls d'une belle vie.", "help": "Aide"}, "productCard": {"addToCart": "A<PERSON>ter au panier", "inCart": "<PERSON><PERSON> le panier", "selectColor": "Veuillez sélectionner la couleur", "wholesaleLogin": "gros :", "login": "Se connecter", "toView": "pour voir", "removeFromWishlist": "<PERSON><PERSON><PERSON> de la liste de souhaits", "addToWishlist": "Ajouter à la liste de souhaits", "wishlistAdded": "Produit ajouté à la liste de souhaits", "wishlistRemoved": "<PERSON><PERSON><PERSON> de la liste de souhaits", "wishlistAddFailed": "Échec de l'ajout à la liste de souhaits", "wishlistRemoveFailed": "Échec du retrait de la liste de souhaits", "wishlistError": "Erreur lors de l'opération de liste de souhaits", "selectColorFirst": "Veuillez d'abord sélectionner la couleur du produit", "new": "NOUVEAU"}, "newArrival": {"filter": "Filtre", "brand": "Marque", "device": "Appareil", "allBrands": "Toutes les Marques", "allDevices": "Tous les Appareils", "loading": "Chargement...", "loadingNewProducts": "Chargement des nouveaux produits...", "noNewProducts": "Aucun nouveau produit", "noNewProductsDesc": "Désolé, il n'y a pas de nouveaux produits disponibles pour le moment", "addToCartSuccess": "Le produit a été ajouté au panier.", "addToCartFailed": "Échec de l'ajout", "addToCartError": "Échec de l'ajout au panier"}, "swiper": {"shopNow": "ACHETER MAINTENANT"}, "shoppingCartRetail": {"emptyCart": "Le panier est vide", "goShopping": "Aller faire des courses", "freeShipping": "LIVRAISON GRATUITE", "color": "<PERSON><PERSON><PERSON>", "weight": "Poids", "subtotal": "Sous-total", "subtotalWeight": "Poids du sous-total", "delete": "<PERSON><PERSON><PERSON><PERSON>", "brand": "Marque", "device": "Appareil", "price": "Prix", "quantity": "Quantité", "minus": "<PERSON>ins", "add": "Ajouter", "remove": "<PERSON><PERSON><PERSON><PERSON>", "updateQuantity": "Mettre à jour la quantité", "quantityError": "Erreur de quantité", "removeConfirm": "Confirmer la <PERSON>", "removeConfirmText": "Êtes-vous sûr de vouloir supprimer cet article ?", "removeSuccess": "Suppression réussie", "removeFailed": "Échec de la suppression", "updateSuccess": "Mise à jour réussie", "updateFailed": "Échec de la mise à jour", "brandEmpty": "La marque est vide", "deviceEmpty": "L'appareil est vide", "colorEmpty": "La couleur est vide"}, "wishlist": {"loginConfirmTitle": "<PERSON><PERSON>", "loginConfirmMessage": "Vous n'êtes pas connecté. Voulez-vous aller à la page de connexion ?", "confirm": "Confirmer", "cancel": "Annuler", "pleaseLoginFirst": "Veuillez d'abord vous connecter", "addSuccess": "Ajouté avec succès à la liste de souhaits", "addFailed": "Échec de l'ajout à la liste de souhaits", "removeSuccess": "Supprimé avec succès", "removeFailed": "Échec de la suppression", "moveToCartSuccess": "Déplacé avec succès vers le panier", "moveToCartFailed": "Échec du déplacement vers le panier", "clearSuccess": "Liste de souhaits vidée avec succès", "clearFailed": "Échec du vidage de la liste de souhaits", "fetchDataFailed": "Échec de la récupération des données de la liste de souhaits"}, "wishlistPage": {"title": "<PERSON> <PERSON><PERSON> de souh<PERSON>s", "emptyTitle": "Votre liste de souhaits est vide", "emptyDescription": "Aucun article n'a encore été ajouté à votre liste de souhaits", "goShopping": "Aller faire des courses", "selectAll": "<PERSON><PERSON>", "addToCart": "A<PERSON>ter au panier", "viewDetails": "Voir les détails", "inStock": "En stock", "outOfStock": "Rupture de stock", "save": "Économiser", "addSelectedToCart": "Ajouter la sélection au panier", "removeSelected": "Supprimer la sélection", "removeConfirmTitle": "Confirmer la <PERSON>", "removeConfirmMessage": "Êtes-vous sûr de vouloir supprimer les {count} articles sélectionnés de votre liste de souhaits ?", "removeSuccess": "Article supprimé de la liste de souhaits", "removeFailed": "Échec de la suppression", "addToCartSuccess": "Ajouté avec succès {count} articles au panier", "addToCartFailed": "Échec de l'ajout au panier", "batchAddToCartError": "Erreur survenue lors de l'ajout en lot au panier", "batchRemoveError": "Erreur survenue lors de la <PERSON> en lot", "removeSelectedSuccess": "Supprimé avec succès {count} articles", "addToCartTitle": "A<PERSON>ter au panier", "selectVariant": "Sélectionner une variante", "selectColor": "<PERSON><PERSON><PERSON><PERSON>ner une couleur", "selectBrand": "Sé<PERSON><PERSON>ner une marque", "selectDevice": "Sélectionner un appareil", "quantity": "Quantité", "cancel": "Annuler", "confirm": "Confirmer", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "product": {"addToCart": "A<PERSON>ter au panier", "removeFromWishlist": "<PERSON><PERSON><PERSON> de la liste de souhaits", "addToWishlist": "Ajouter à la liste de souhaits", "addToCartSuccess": "Produit ajouté au panier", "addToCartFailed": "Échec de l'ajout au panier", "addToCartError": "Erreur survenue lors de l'ajout au panier", "invalidProduct": "Données de produit invalides", "wishlistToggleError": "Erreur survenue lors de l'opération de liste de souhaits", "shoppingCart": "<PERSON><PERSON>"}, "productDetails": {"loading": "Chargement...", "productNotFound": "Produit introuvable ou échec du chargement", "retry": "<PERSON><PERSON><PERSON><PERSON>", "networkError": "Erreur de connexion réseau, veuillez vérifier votre réseau et réessayer", "description": "Description", "specification": "Spécification", "dimensions": "Dimensions", "profile": "Profil", "dropRating": "Évaluation de chute", "device": "Appareil", "selectBrand": "Sélectionner la marque", "selectDevice": "Sélectionner l'appareil", "color": "<PERSON><PERSON><PERSON>", "quantity": "Quantité", "addToCart": "A<PERSON>ter au panier", "alreadyInCart": "<PERSON><PERSON><PERSON><PERSON> dans le panier ({count})", "buyNow": "Acheter maintenant", "checkoutSecurely": "PAIEMENT SÉCURISÉ AVEC", "acceptedPayments": "Paiements acceptés", "interestFreePayments": "4 paiements sans intérêt de 16,00 $ avec Klarna", "learnMore": "En savoir plus", "shippingUpTo": "Livraison jusqu'à {percent}% de réduction", "pleaseSelectColor": "Veuillez d'abord sélectionner la couleur du produit", "productInCart": "Le produit est déjà dans le panier", "relatedProducts": "Vous pourriez aussi aimer", "brands": "Marques", "colors": "Couleurs", "mediaPreview": {"close": "<PERSON><PERSON><PERSON>", "previous": "Précédent", "next": "Suivant"}}, "news": {"loadingTags": "Chargement des tags", "loadTagsFailed": "Échec du chargement des tags, veuillez actualiser la page et réessayer", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "getTagsFailed": "Échec de la récupération de la liste des tags", "getNewsFailed": "Échec de la récupération de la liste des actualités", "detail": {"home": "Accueil", "news": "Actualités", "relatedArticles": "Articles connexes", "youMayAlsoLike": "Vous pourriez aussi aimer", "addToCartSuccess": "Le produit a été ajouté au panier.", "addToCartFailed": "Échec de l'ajout", "addToCartError": "Échec de l'ajout au panier"}}, "discover": {"showAll": "TOUT AFFICHER", "more": "plus"}, "footer": {"newsletter": "NEWSLETTER", "stayUpdated": "Restez informé des nouvelles collections MLK+ :", "emailPlaceholder": "E-mail", "subscribe": "S'ABONNER", "companyName": "MLK+ OUSAND S.R.L.", "companyAddress": "<PERSON><PERSON>, 47 - 00197 Roma, ITALIA", "taxId": "P. I. 13085581000", "copyright": "Copyright © 2025 MLK+. Tous droits réservés."}, "subscribe": {"title": "Rejoignez la communauté MLK+", "description": "Abonnez-vous à la newsletter", "policy": "Si vous êtes un amateur de technologie et de design, inscrivez-vous à notre newsletter et restez informé des actualités.", "emailPlaceholder": "Entrez votre adresse e-mail", "signUp": "S'INSCRIRE", "emailValidation": "<PERSON><PERSON><PERSON><PERSON> saisir une adresse e-mail correcte"}, "search": {"home": "Accueil", "showHideFilters": "Afficher/Masquer les filtres", "byRating": "Par évaluation", "hideAll": "MASQUER TOUT", "showAll": "AFFICHER TOUT", "addToCartSuccess": "Le produit a été ajouté au panier.", "addToCartFailed": "Échec de l'ajout", "addToCartError": "Échec de l'ajout au panier"}}
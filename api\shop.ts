import httpRequest from "~/utils/request";


interface dataType{
  product_id?: number,
  quantity?: number,
  cart_item_id?: string,
  color?: string,
  size?: string
}
interface addCartType{
  product_id: number,
  quantity: number,
  selected_configurable_option?: number,
  super_attribute?: {
    [key: string]: string
  }
}

// 获取购物车内容
export const getCart = async () => {
    return httpRequest.get('/api/mlk/cart')
}

// 购物车添加
export const getShoppingCartAdd = async (data:addCartType) => {
    return httpRequest.post('/api/mlk/cart/add',data)
}

// 购物车删除
export const getShoppingCartRemove = async (data:dataType) => {
    return httpRequest.post('/api/mlk/cart/remove',data)
}

// 更新购物车商品数量
export const getShoppingCartUpdate = async (data:dataType) => {
    return httpRequest.post('/api/mlk/cart/update', data)
}

// 清空购物车
export const getShoppingCartClear = async () => {
    return httpRequest.post('/api/mlk/cart/clear')
}

// 购物车批量删除
export const getShoppingCartBatchRemove = async (data:dataType) => {
    return httpRequest.post('/api/mlk/cart/remove-selected', data)
}

// 应用优惠券
export const getShoppingCartApplyCoupon = async (data:dataType) => {
    return httpRequest.post('/api/mlk/cart/coupon/apply', data)
}

// 购物车结账流程： 保存配送方式
export const getShoppingCartSaveShippingMethod = async (data:dataType) => {
    return httpRequest.post('/api/mlk/checkout/shipping-methods', data)
}

// 购物车支付方式图标查询
export const getShoppingCartPaymentIcons = async () => {
    return httpRequest.get('/api/mlk/common/payment-icons')
}

// 购物车结账流程： 保存支付方式
export const getShoppingCartSavePaymentMethod = async (data:dataType) => {
    return httpRequest.post('/api/mlk/checkout/payment-methods', data)
}


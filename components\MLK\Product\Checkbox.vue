<template>
    <div class="check_page">
        <el-collapse accordion>
            <el-collapse-item>
                <template #title>
                    <div style="width: 100%;display: flex;justify-content: space-between;margin: auto 0;">
                        <div class="check_title">
                            {{ title }}
                        </div>
                        <div class="check_number">
                            {{ checkedCities.length ? checkedCities.length : 0 }}
                        </div>
                    </div>
                </template>
                <template #default>
                    <div style="width: 100%;height: 100%;margin-top: .625rem;"> 
                        <el-checkbox-group v-model="checkedCities" @change="handleChange">
                            <el-checkbox v-for="item in props.devices" :key="item.id" :label="item.name" :value="item.id" style="margin: 0 1.5rem;" size="large">
                                {{ item.name }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </template>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script lang="ts" setup>
import type { CheckboxValueType } from 'element-plus'
interface Device {
  id: string | number;
  name: string;
  parent_id: number;
}

const props = defineProps<{
  // 标题
  title?: string;
  devices?: Device[];
}>();

const checkedCities = ref([])

const emit = defineEmits<{
  (e: 'changeDevices', value: { parent_id: number | null; devices: (string | number)[] }): void
}>()

const handleChange = (value: CheckboxValueType[]) => {
  const newCheckedCities = {
    parent_id: props.devices?.[0]?.parent_id ?? null,
    devices: value.filter(v => typeof v === 'string' || typeof v === 'number')
  }
  emit('changeDevices', newCheckedCities)
}
</script>

<style lang="scss" scoped>
.check_page {
    width:31.25rem;
    height: 3.125rem;
    margin-bottom: .625rem;
    ::v-deep .el-collapse-item__header{
        width: 30.8125rem;
        height: 3rem;
        border: .0625rem solid #BABABA;
        border-radius: .5rem;
    }
    ::v-deep .el-collapse-item__wrap{ 
        background-color: #FFFFFF;
        border: 1px solid #EBEBEB;
        border-radius: .5rem;
        position:sticky;
        z-index: 99;
    }
    ::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
        color: black;
    }
    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color:black;
        border-color:black;
    }
    ::v-deep .el-checkbox.el-checkbox--large {
        height: 30px;
    }
    .check_title {
        width: 3.0625rem;
        padding-left: .625rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 1.125rem;
        color: #0C0C0C;
        line-height: 3rem;
        text-align: left;
        font-style: normal;
    }
    .check_number {
        width: 1.4375rem;
        font-family: AppleSystemUIFont;
        font-size: 1.25rem;
        color: #6F6F6F;
        line-height: 3rem;
        text-align: right;
        font-style: normal;
        text-transform: none;
        margin-right: 1.25rem;
    }
}
</style>
import httpRequest from "~/utils/request";

interface dataType{
  id?: number,
  product_id?: number,
}

// 获取心愿单列表
export const getWishList = async () => {
  return httpRequest.get('/api/mlk/wishlist')
}

// 添加商品到心愿单
export const wishlistAdd = async (data:dataType) => {
  return httpRequest.post('/api/mlk/wishlist',data)
}

// 将心愿单商品移动到购物车
export const moveToCart = async (data:dataType) => {
  return httpRequest.post('/api/mlk/wishlist/move-to-cart',data)
}

// 删除单个心愿单商品
export const wishlisDelete = async (data:dataType) => {
  return httpRequest.post('/api/mlk/wishlist/delete',data)
}

// 清空心愿单
export const wishlistClear = async () => {
  return httpRequest.post('/api/mlk/wishlist/clear')
}

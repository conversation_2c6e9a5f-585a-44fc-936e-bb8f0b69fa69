import { useLanguageStore } from '~/stores/language'

export const useLanguageEventBus = () => {
  const languageStore = useLanguageStore()
  
  // 创建事件总线
  const eventBus = new Map<string, ((language: string) => void)[]>()
  
  // 监听语种变化
  const onLanguageChange = (callback: (language: string) => void) => {
    if (!eventBus.has('languageChange')) {
      eventBus.set('languageChange', [])
    }
    eventBus.get('languageChange')?.push(callback)
  }
  
  // 触发语种变化事件
  const emitLanguageChange = (newLanguage: string) => {
    const callbacks = eventBus.get('languageChange') || []
    callbacks.forEach(callback => {
      try {
        callback(newLanguage)
      } catch (error) {
        console.error('🌐 语种变化事件处理失败:', error)
      }
    })
  }
  
  // 监听store中的语言变化
  watch(() => languageStore.language, (newLanguage) => {
    emitLanguageChange(newLanguage)
  })
  
  // 移除监听器
  const removeLanguageChangeListener = (callback: (language: string) => void) => {
    const callbacks = eventBus.get('languageChange') || []
    const index = callbacks.indexOf(callback)
    if (index > -1) {
      callbacks.splice(index, 1)
    }
  }
  
  return {
    onLanguageChange,
    emitLanguageChange,
    removeLanguageChangeListener
  }
} 
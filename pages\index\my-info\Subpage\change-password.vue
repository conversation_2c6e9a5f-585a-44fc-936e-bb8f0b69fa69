<template>
  <div class="changePassword-page">
    <div class="form-box">
        <el-form
          ref="ruleFormRef"
          style="max-width: 50.3125rem;"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          label-position="top"
        >
          <el-form-item prop="email" label="email" style="width: 100%;">
            <el-input v-model="ruleForm.email"/>
          </el-form-item>
          <el-form-item prop="newPassword" label="newPassword" style="width: 100%;">
           <el-input
             v-model="ruleForm.newPassword"
             :type="showNewPassword ? 'text' : 'password'"
           >
             <template #suffix>
               <el-icon
                 :size="20"
                 style="cursor: pointer"
                 @click="showNewPassword = !showNewPassword"
               >
                 <component :is="showNewPassword ? View : Hide" />
               </el-icon>
             </template>
           </el-input>
          </el-form-item>
          <el-form-item prop="confirmPassword" label="confirmPassword" style="width: 100%;">
           <el-input
             v-model="ruleForm.confirmPassword"
             :type="showConfirmPassword ? 'text' : 'password'"
           >
             <template #suffix>
               <el-icon
                 :size="20"
                 style="cursor: pointer"
                 @click="showConfirmPassword = !showConfirmPassword"
               >
                 <component :is="showConfirmPassword ? View : Hide" />
               </el-icon>
             </template>
           </el-input>
          </el-form-item>
          <el-button type="primary"  @click="submitForm(ruleFormRef)">
              Create
          </el-button>
        </el-form>
      </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { View, Hide } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { changePassword } from '@/api/user'
import { useUserStore } from '@/stores/user'

interface RuleForm {
  email: string,
  newPassword:string,
  confirmPassword: string,
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  email: '',
  newPassword: '',
  confirmPassword:''
})

const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const rules = reactive<FormRules<RuleForm>>({
  email: [{required: true,message: 'Please input email address',trigger: 'blur',},
        {
          type: 'email',
          message: 'Please input correct email address',
          trigger: ['blur', 'change'],
    }],
  newPassword: [
    { required: true, message: 'Please input newPassword', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: 'Please input confirmPassword', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== ruleForm.newPassword) {
          callback(new Error('两次输入的新密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        await ElMessageBox.confirm(
          'confirm password change?',
          'Tips',
          {
            confirmButtonText: 'confirm',
            cancelButtonText: 'cancel',
            type: 'warning',
          }
        ).then(async () => {
          const res = await changePassword({
            email: ruleForm.email,
            password: ruleForm.newPassword,
            password_confirmation: ruleForm.confirmPassword,
          })
          if(res.success){
            ElMessage({
              message: res.message,
              type: 'success',
            })
            useUserStore().clearCache()
            navigateTo('/login')
          }else{
            ElMessageBox.alert(res.message, 'error', { type: 'error' })
          }
        })
      } catch (err: unknown) {
        if (err && typeof err === 'object' && 'message' in err) {
          ElMessageBox.alert((err as { message: string }).message, 'error', { type: 'error' })
        }
      }
    } else {
      console.warn('error submit!', fields)
    }
  })
}
</script>

<style scoped lang="scss">
.changePassword-page{
  width: 67.125rem;
  height: 64.1875rem;
  background: #FFFFFF;
  border-radius: .5rem;
  padding: 2.6875rem 14.1875rem;
  box-sizing: border-box;
  .form-box{
    width: 100%;
    .el-form{
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.25rem;
      ::v-deep .el-form-item{
        .el-input{
          height: 3.5rem !important;
          line-height: 3.5rem !important;
        }
        .el-select{
          height: 3.5rem !important;
          line-height: 3.5rem !important;
          .el-select__wrapper{
            height: 100%;
          }
        }
      }
      .el-button{
        margin: 5.375rem auto;
        width: 21.1875rem;
        height: 3.5rem;
        background: #3D3D3D;
        border-radius: 8px;
      }
      
    }

  }
}
</style>
<template>
    <div class="login-page">
        <div class="login-box">
            <div class="login-info">
                <div class="login-info-left">
                    <div class="login-info-left-img"><img style="width: 3rem;height: 3rem;" src="~/assets/images/login/user.png" alt=""></div>
                    <div class="login-info-left-edit"><img style="width: 1.5rem;height: 1.5rem;" src="~/assets/images/login/edit.png" alt=""></div>
                </div>
                <div class="login-info-right">
                    <div class="login-info-right-name">
                        <span style="width: 5.625rem;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">Hi,{{ userInfo.name }}</span>
                        <img v-if="isWholesaleUser" width="75px" src="~/assets/images/login/wholesaler.png" alt="">
                    </div>
                    <div class="login-info-right-email">
                        <img width="24px" src="~/assets/images/login/email.png" alt="">
                        <span style="width: 9.375rem;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;text-align: left;padding-left: .625rem;">{{ userInfo.email }}</span>
                    </div>
                    <div class="login-info-right-password" @click="handleChangePassword">
                        <img width="24px" src="~/assets/images/login/password.png" alt="">
                        <span style="margin-left: .3125rem">Change Password</span>
                        <img width="24px" src="~/assets/images/login/next.png" alt="">
                    </div>
                    <div class="login-info-right-logout" @click="handleLogout">
                        <img width="24px" src="~/assets/images/login/logout.png" alt="">
                        <span>logout</span>
                    </div>
                </div>
            </div>
            <div class="coupons-info">
                <div class="coupons-info-left">
                    <img src="~/assets/images/login/coupon.png" alt="">
                    <div>My Coupons</div>
                    <div class="coupons-info-left-num">
                        <div class="Available">
                            <div style="font-weight: 400;font-size: 1rem;">Available</div>
                            <div style="margin-top: .625rem;font-weight: bold;font-size: 1.25rem;">10</div>
                        </div>
                        <div class="Expired">
                            <div style="font-weight: 400;font-size: 1rem;">Expired</div>
                            <div style="margin-top: .625rem;font-weight: bold;font-size: 1.25rem;">0</div>
                        </div>
                    </div>
                </div>
                <div class="coupons-info-right" @click="handleMyCoupons">
                    <img src="~/assets/images/login/right.png" alt="">
                </div>
            </div>
            <div class="account-info">
                <div class="title">Account Manager</div>
                <div class="customer">
                    <img width="48px" src="~/assets/images/login/customer.png" alt="">
                    <div>Joy</div>
                </div>
                <div class="phone">
                    <div>Tel. and Whatsapp: *************</div>
                </div>
                <div class="email">
                    <div>Email: <EMAIL></div>
                </div>
            </div>
        </div>
        <div class="login-title">
            <span>My Orders</span>
            <div style="float: right;margin-right: 25px;">
                <el-icon>
                    <ArrowRightBold />
                </el-icon>
            </div>
        </div>
        <div class="login-tab">
            <MLKMyPurchaseDetails />
        </div>
        <div class="bottom-box">
            <MLKMyPurchaseDetails />
        </div>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const store = useUserStore()
const isWholesaleUser = computed(() => store.isWholesaleUser())
const { userInfo } = storeToRefs(store)
console.warn('userInfo===========================>', userInfo.value)

const handleChangePassword = () => {
    router.push('/password-reset')
}
const handleMyCoupons = () => {
    // router.push('/my-info/coupons')
}
const handleLogout = async () => {
    const result = await store.logout()
    console.warn('📄 登录页面: 登出结果:', result)
    ElMessage.success('登出成功')
    router.push('/login')
}
</script>

<style scoped lang="scss">

.login-page {
    width: 100%;
     .login-box {
        height: 16.6875rem;
        background: #FFFFFF;
        border-radius: .5rem;
        display: flex;
        justify-content: space-around;
        align-items: center;
        .login-info{
            width: 20.625rem;
            height: 12.1875rem;
            background: #FFFFFF;
            border-radius: .5rem;
            border: .0625rem solid #BABABA;
            display: flex;
            justify-content: space-between;
            .login-info-left {
                width: 6.25rem;
                height: 100%;
                .login-info-left-img {
                    width: 3rem;
                    height: 3rem;
                    margin: 3.8125rem 0 0 1.625rem;
                }
                .login-info-left-edit {
                    width: 1.5rem;
                    height: 1.5rem;
                    margin: .75rem 0 0 2.375rem;
                }
            }
            .login-info-right {
                width: 10.625rem;
                height: 100%;
                margin-right: 2.1875rem;
                .login-info-right-name {
                    width: 100%;
                    margin-top: 1.625rem;
                    height: 1.3125rem;
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: bold;
                    font-size: 1.125rem;
                    color: #3D3D3D;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    justify-content: space-between;
                }
                .login-info-right-email {
                    width: 100%;
                    margin-top: 1.625rem;
                    height: 1.3125rem;
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #3D3D3D;
                    line-height: 1.3125rem;
                    text-align: right;
                    font-style: normal;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .login-info-right-password {
                    width: 115%;
                    margin-top: .875rem;
                    height: 1.3125rem;
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #3D3D3D;
                    line-height: 1.3125rem;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .login-info-right-logout {
                    width: 50%;
                    margin-top: .875rem;
                    height: 1.3125rem;
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #3D3D3D;
                    line-height: 1.3125rem;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;
                    transition: color 0.3s ease;
                    
                    &:hover {
                        color: #ff4757;
                    }
                }
            }
        }
        .coupons-info {
            width: 20.625rem;
            height: 12.1875rem;
            background: #FFFFFF;
            border-radius: .5rem;
            border: .0625rem solid #BABABA;
            display: flex;
            justify-content: space-around;
            align-items: center;
            .coupons-info-left {
                width: 11.25rem;
                height: 10.625rem;
                margin-left: 1.5rem;
                div {
                    font-family: SFProDisplay, SFProDisplay;
                    font-weight: 600;
                    font-size: 1.25rem;
                    color: #3D3D3D;
                    text-align: left;
                    font-style: normal;
                }
                .coupons-info-left-num {
                    width: 9.125rem;
                    height: 3.125rem;
                    margin-top: 1.3125rem;
                    display: flex;
                    justify-content: space-between;
                }
            }
            .coupons-info-right {
                width: 8.75rem;
                height: 10.625rem;
                display: flex;
                justify-content: space-around;
                align-items: center;
            }
        }
        .account-info {
            width: 20.625rem;
            height: 12.1875rem;
            background: #FFFFFF;
            border-radius: .5rem;
            border: .0625rem solid #BABABA;
            div {
                font-family: SFProDisplay, SFProDisplay;
                color: #3D3D3D;
                text-align: left;
                font-style: normal
            }
            .title {
                width: 11.25rem;
                height: 1.5rem;
                margin: 1.5rem auto 0 auto;
                font-weight: 600;
                font-size: 1.25rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .customer {
                width: 5.375rem;
                height: 3rem;
                margin: .875rem auto 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                font-size: 1rem;
            }
            .phone {
                width: 16rem;
                height: 1.1875rem;
                margin: .875rem 3rem 0 1.625rem;
                font-weight: 400;
                font-size: 1rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .email {
                width: 16rem;
                height: 1.1875rem;
                margin: .875rem 3rem 0 1.625rem;
                font-weight: 400;
                font-size: 1rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    
    .login-title {
        margin-top: 30px;
        padding: 1.25rem 0;
        height: 3.5rem;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }

    .login-tab {
        background-color: #FFFFFF;
        border-top: .0625rem solid #EBEBEB;
    }

    .bottom-box {
        margin-top: 1.875rem;
    }
}
</style>
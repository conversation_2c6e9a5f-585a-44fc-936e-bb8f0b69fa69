import { useUserStore } from '~/stores/user'

export default defineNuxtPlugin(() => {
  // 只在客户端执行
  if (import.meta.client) {
    const userStore = useUserStore()
    
    // 应用启动时初始化token状态
    console.warn('🔐 Token管理器: 开始初始化用户状态')
    userStore.initializeTokenState()
    console.warn('🔐 Token管理器: 用户状态初始化完成, 登录状态:', userStore.isLoggedIn)
    
    // 监听页面可见性变化，当页面重新可见时检查token状态
    const handleVisibilityChange = () => {
      if (!document.hidden && userStore.token) {
        console.warn('页面重新可见，检查token状态...')
        // 检查token是否即将过期
        if (userStore.isTokenExpiringSoon(1)) {
          console.warn('检测到token即将过期，尝试续期...')
          userStore.autoRefreshToken()
        }
      }
    }
    
    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听用户活动，在用户活动时检查token状态
    let userActivityTimeout: NodeJS.Timeout | null = null
    
    const handleUserActivity = () => {
      if (userActivityTimeout) {
        clearTimeout(userActivityTimeout)
      }
      
      userActivityTimeout = setTimeout(() => {
        if (userStore.token && userStore.isTokenExpiringSoon(1)) {
          console.warn('用户活动后检测到token即将过期，尝试续期...')
          userStore.autoRefreshToken()
        }
      }, 5000) // 5秒后检查
    }
    
    // 添加用户活动监听
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, true)
    })
    
    // 清理函数
    onUnmounted(() => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity, true)
      })
      if (userActivityTimeout) {
        clearTimeout(userActivityTimeout)
      }
    })
  }
}) 
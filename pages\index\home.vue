<template>
  <div class="homePage">
    <div class="top">
      <ClientOnly>
        <MLKSwiper 
          v-if="!isLoading && bannerList.length > 0" 
          ref="swiperRef"
          :key="`swiper-${bannerList.length}-${swiperKey}`"
          class="swiper-img"  
          :data-list="bannerList" 
        />
      </ClientOnly>
      <div class="top-contennt card">
        <div>{{ $t('home.findDevice') }}</div>
        <el-select v-model="selectedBrand" :teleported="false" :placeholder="$t('home.selectBrand')" size="default" style="width: 11.5rem;height: 3rem;">
          <el-option v-for="item in brandOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="selectedDevice" :teleported="false" :placeholder="$t('home.selectDevice')" size="default" style="width: 11.5rem;height: 3rem;" @change="handleDeviceChange">
          <el-option v-for="item in deviceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <!-- 新商品选择 -->
    <MLKTitle :title="newArrivalsData?.title" />
    <MLKHomeCarousel
:data-list="newArrivalsData?.data"/>
    <MLKTitle :title="featuredCollectionsData?.title" style="margin-top: 7.375rem"/>
    <MLKHomeMerryGoRound
:data-list="featuredCollectionsData?.data?.images" style="margin-top: 3.875rem"/>
    <MLKTitle :title="$t('home.shopByBrand')" />
    <div class="logo">
      <img v-for="item in brandImgs" :key="item.id" class="brand-img" :src="item.url" :alt="item.label">
    </div>
    <div class="logo_1">
      <img style="width: 100%;height: 10.9375rem;" :src="indexCenterBannerList[0]?.image" :alt="indexCenterBannerList[0]?.title">
    </div>
    <MLKTitle :title="hotSalesData?.title" style="margin-top: 8.9375rem;" />
    <div
      style="width: 94.375rem;margin: 0 auto;display: flex;align-items: center;justify-content: space-between;gap: 0 2.75rem;">
      <MLKProduct v-for="item in hotSalesData?.data || []" :key="item?.id || Math.random()" :product-data="item" />
    </div>
    <MLKTitle :title="magsafeAccessoriesData?.title" style="margin-top: 8.9375rem;" />
    <div
      style="width: 94.375rem;margin: 0 auto;display: flex;align-items: center;justify-content: space-between;gap: 0 2.75rem;">
      <MLKProduct v-for="item in magsafeAccessoriesData?.data || []" :key="item?.id || Math.random()" :product-data="item"/>
    </div>
    <MLKTitle :title="additionalItemsData?.title" style="margin-top: 8.9375rem;" />
    <div
      style="width: 94.375rem;margin: 0 auto;display: flex;align-items: center;justify-content: space-between;gap: 0 2.75rem;">
      <MLKProduct v-for="item in additionalItemsData?.data || []" :key="item?.id || Math.random()" :product-data="item"/>
    </div>
    <div class="log-muse">
      <MLKLogo style="width: 8.0625rem;height: 5.3688rem;" />
      
      <div class="loop-scroll">
        <p>{{ $t('home.inspirationText') }}</p>
      </div>

    </div>
    <div style="position: relative;">
      <ClientOnly>
        <MLKSwiperMuse 
          v-if="indexFootBannerList.length > 0"
          ref="swiperMuseRef" 
          :key="`muse-${indexFootBannerList.length}-${swiperKey}`" 
          :data-list="indexFootBannerList" 
          style="min-width: 100vw;height: 39.5rem;margin-top: 3.25rem;"
        />
      </ClientOnly>
    </div>
    <MLKDisCover style="margin-top: 7.125rem;" :about-data="aboutUsData" :news-data="newsData" />
    <MLKSubscribe style="margin-top: 13.9375rem;" @message-to-parent="subscribeEmail"/>
    <!-- help -->
    <ClientOnly>
      <transition name="el-fade-in-linear">
       <div v-if="isShowSideBar" class="help" @click="showHelp=true">
        <div class="img">
          <img src="~/assets/images/help.png" alt="help">
        </div>
        <div class="text">
          {{ $t('home.help') }}?
        </div>     
      </div>
      </transition>
      <!-- 右侧导航栏 -->
      <transition name="el-fade-in-linear">
        <div v-if="isShowSideBar" class="right-nav">
          <div class="right-nav-bnt">
            <img src="~/assets/images/right-bar/Phone.png" alt="phone">
          </div>
          <div class="right-nav-bnt">
            <img src="~/assets/images/right-bar/WeChat.png" alt="WeChat">
          </div>
          <div class="right-nav-bnt">
            <img src="~/assets/images/right-bar/wt.png" alt="wt">
          </div>
        </div>
      </transition>
    </ClientOnly>
    <MLKHelp v-if="showHelp" style="position: fixed;top: 6.25rem;left: calc(50% - 10.9375rem);z-index: 1000;" @close="showHelp=false" @close-help="showHelp=false"/>

  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { getHomePageData } from "~/api/channel"
import { emailSubscribe, getBrandList } from "~/api/public"
import { useUserStore } from '~/stores/user'
import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言store
const languageStore = useLanguageStore()
const userStore = useUserStore()



// 添加客户端检查
const isClient = ref(false)

const showHelp = ref(false)
interface RootObject {
  id: number;
  title: string;
  url_key: string;
  content?: string;
  image?: string;
}

// 产品数据类型，与 MLKProduct 组件兼容
interface ProductData {
  id: number;
  name: string;
  url_key: string;
  price: number;
  base_image: string;
  formatted_price: string;
  description: string;
  discount?: {
    has_discount: boolean;
    discount_percentage: number;
  };
}

interface HomepageType {
  new_arrivals?: {
    title?: string;
    data?: RootObject[]
  }
  featured_collections?: {
    title?: string;
    data?: {
      images?: RootObject[];
    }
  }
  hot_sales?: {
    title?: string;
    data?: ProductData[];
  }
  magsafe_accessories?: {
    title?: string;
    data?: ProductData[];
  }
  additional_items?: {
    title?: string;
    data?: ProductData[];
  }
  index_foot_banners?: {
    images?: RootObject[];
  }
  index_center_banner?: {
    data?: {
      images?: RootObject[];
    };
  };
  index_banner?: {
    data?: {
      images?: RootObject[];
    };
  };
  about_us?: {
    title?: string;
    data?: RootObject[];
  };
  news?: {
    title?: string;
    data?: RootObject[];
  };
}

interface homeDataType{
  homepage: HomepageType,
  is_wholesale: boolean
}

// 获取首页数据方法
const getHomeData = async (): Promise<homeDataType> => {
  if (import.meta.client) {
    await nextTick()
  }
  try {
    const res = await getHomePageData()
    // 处理不同的数据结构
    if (res && typeof res === 'object') {
      // 如果 res 本身就是数据
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((res as any).homepage || (res as any).is_wholesale !== undefined) {
        return res as unknown as homeDataType
      }
      // 如果 res 有 data 属性
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((res as any).data) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return (res as any).data as homeDataType
      }
    }
    // 如果都不匹配，返回空数据
    return {
      homepage: {},
      is_wholesale: false
    }
  } catch (error) {
    console.error('API 调用失败:', error)
    throw error // 重新抛出错误，让上层处理
  }
}

const bannerList = ref<RootObject[]>([])
const newArrivalsData = ref<HomepageType['new_arrivals']>({})
const featuredCollectionsData = ref<HomepageType['featured_collections']>({})
const hotSalesData = ref<HomepageType['hot_sales']>({})
const magsafeAccessoriesData = ref<HomepageType['magsafe_accessories']>({})
const additionalItemsData = ref<HomepageType['additional_items']>({})
const indexFootBannerList = ref<RootObject[]>([])
const aboutUsData = ref<HomepageType['about_us']>({})
const newsData = ref<HomepageType['news']>({})
const indexCenterBannerList = ref<RootObject[]>([]) 
const isLoading = ref(true)

const brandImgs = ref<BrandImgType[]>([])

// 使用 async/await 确保数据加载完成
const loadData = async () => {
  try {
    const data = await getHomeData()
    const { homepage,is_wholesale } = data
    userStore.isWholesale = is_wholesale
    // 轮播图数据
    bannerList.value = homepage?.index_banner?.data?.images || []
    // 新品数据
    newArrivalsData.value = homepage?.new_arrivals || {}
    // 特色系列数据
    featuredCollectionsData.value = homepage?.featured_collections || {}
    // 中间图片
    indexCenterBannerList.value = homepage?.index_center_banner?.data?.images || []
    // 热销产品数据
    hotSalesData.value = homepage?.hot_sales || {}
    // 配件产品数据
    magsafeAccessoriesData.value = homepage?.magsafe_accessories || {}
    
    // 额外产品数据
    additionalItemsData.value = homepage?.additional_items || {}
    // 底部轮播图数据
    indexFootBannerList.value = homepage?.index_foot_banners?.images || []
    // about us 数据
    aboutUsData.value = homepage?.about_us || {}
    // news 数据
    newsData.value = homepage?.news || {}
    
    isLoading.value = false
  } catch {
    // 失败后重试
    setTimeout(() => {
      loadData()
    }, 1000)
  }
}
// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
  loadData()
  
}, { immediate: true })


const selectedBrand = ref<number | string>('')
const selectedDevice = ref<number | string>('')
// 控制侧边导航栏显示隐藏
const isShowSideBar = ref(false)

// 监听品牌选择变化，更新设备选项
watch(selectedBrand, (newBrandId) => {
  if (newBrandId && brandData.value.length > 0) {
    const selectedBrandData = brandData.value.find(brand => brand.id === newBrandId)
    if (selectedBrandData && selectedBrandData.devices) {
      deviceOptions.value = selectedBrandData.devices.map(device => ({
        value: device.id,
        label: device.label
      }))
      // 重置设备选择
      selectedDevice.value = ''
    } else {
      deviceOptions.value = []
      selectedDevice.value = ''
    }
  } else {
    deviceOptions.value = []
    selectedDevice.value = ''
  }
})
// 选择型号后跳转筛选页面
const handleDeviceChange = (value: number) => {
  const route = useRouter()
  route.push({
    path: '/search',
    query: {
      brand: selectedBrand.value,
      device: value
    }
  })
}

// 添加缺失的响应式变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperMuseRef = ref<{ manualInitSwiper: () => void; forceRerender: () => void; getSwiperInstance: () => any } | null>(null)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperRef = ref<{ manualInitSwiper: () => void; isInitialized: any } | null>(null)
const swiperKey = ref(0)

// 在组件挂载时加载数据
onMounted(() => {
  isClient.value = true
  loadData()
  getBrandListData()
  
  // 只在客户端添加滚动监听
  if (import.meta.client) {
    window.addEventListener("scroll", handleScroll);
    
    // 页面可见性变化监听
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // 延迟检查，确保DOM完全渲染
        setTimeout(() => {
          // 检查主轮播图
          if (swiperRef.value && !swiperRef.value.isInitialized?.value) {
            console.warn('页面刷新后重新初始化主轮播图')
            swiperRef.value?.manualInitSwiper()
          }
          
          // 检查 SwiperMuse
          if (swiperMuseRef.value) {
            const status = swiperMuseRef.value?.getSwiperInstance()
            if (status && !status.isInitialized && status.hasData) {
              console.warn('页面刷新后重新初始化 SwiperMuse')
              swiperMuseRef.value?.manualInitSwiper()
            }
          }
          
          // 检查品牌数据是否丢失，如果丢失则重新获取
          if (brandOptions.value.length === 0 || brandImgs.value.length === 0) {
            console.warn('页面刷新后品牌数据丢失，重新获取')
            getBrandListData()
          }
          
          // 如果品牌数据已恢复，重新设置设备选项
          if (selectedBrand.value && brandData.value.length > 0) {
            const selectedBrandData = brandData.value.find(brand => brand.id === selectedBrand.value)
            if (selectedBrandData && selectedBrandData.devices) {
              deviceOptions.value = selectedBrandData.devices.map(device => ({
                value: device.id,
                label: device.label
              }))
            }
          }
        }, 100)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 页面加载完成后的处理
    window.addEventListener('load', () => {
      console.warn('页面完全加载完成')
      setTimeout(() => {
        // 检查主轮播图
        if (swiperRef.value && !swiperRef.value.isInitialized?.value) {
          console.warn('页面加载完成后初始化主轮播图')
          swiperRef.value?.manualInitSwiper()
        }
        
        // 检查 SwiperMuse
        if (swiperMuseRef.value) {
          const status = swiperMuseRef.value?.getSwiperInstance()
          if (status && !status.isInitialized && status.hasData) {
            console.warn('页面加载完成后初始化 SwiperMuse')
            swiperMuseRef.value?.manualInitSwiper()
          }
        }
        
        // 检查品牌数据是否丢失，如果丢失则重新获取
        if (brandOptions.value.length === 0 || brandImgs.value.length === 0) {
          console.warn('页面加载完成后品牌数据丢失，重新获取')
          getBrandListData()
        }
        
        // 如果品牌数据已恢复，重新设置设备选项
        if (selectedBrand.value && brandData.value.length > 0) {
          const selectedBrandData = brandData.value.find(brand => brand.id === selectedBrand.value)
          if (selectedBrandData && selectedBrandData.devices) {
            deviceOptions.value = selectedBrandData.devices.map(device => ({
              value: device.id,
              label: device.label
            }))
          }
        }
      }, 200)
    })

    // 清理函数
    onUnmounted(() => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    })
  }
})

interface SubscribeType{
  data:string
}

interface DeviceType {
  id: number;
  label: string;
}

interface BrandListType {
  id: number;
  label: string;
  devices?: DeviceType[];
  swatch_value_url?: string;
  // 根据实际API返回的数据结构添加其他字段
}

interface BrandImgType {
  id: number;
  label: string;
  url: string;
}

interface OptionType {
  value: number;
  label: string;
}

const brandOptions = ref<OptionType[]>([])
const deviceOptions = ref<OptionType[]>([])
const brandData = ref<BrandListType[]>([]) // 存储完整的品牌数据
// 获取产品和品牌下拉框数据列表
const getBrandListData = async(retryCount = 0) => {
  try {
    const res: IResultData<BrandListType[]> = await getBrandList()
    if (res && res.success && Array.isArray(res.data)) {
      // 存储完整的品牌数据
      brandData.value = res.data
      // 设置品牌选项
      brandOptions.value = res.data.map((item:BrandListType) => ({
        value: item.id,
        label: item.label
      }))
      brandImgs.value = res.data.map((item:BrandListType) => ({
        id: item.id,
        label: item.label,
        url: item.swatch_value_url || '',
      }))
      // 如果brandImgs的长度大于6位，就删除之后的所有数据
      if (brandImgs.value.length > 6) {
        brandImgs.value = brandImgs.value.slice(0, 6)
      }
      console.warn('品牌数据获取成功，品牌数量:', brandData.value.length)
    } else {
      console.warn('获取品牌列表失败或返回数据为空，数据类型:', typeof res?.data, '数据内容:', res?.data);
      // 如果数据不是数组，尝试其他可能的数据结构
      if (res?.data && typeof res.data === 'object' && !Array.isArray(res.data)) {
        // 可能是 { brands: [...] } 这样的结构
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const brands = (res.data as any).brands || (res.data as any).data || [];
        if (Array.isArray(brands)) {
          brandData.value = brands
          brandOptions.value = brands.map((item:BrandListType) => ({
            value: item.id,
            label: item.label
          }))
          brandImgs.value = brands.map((item:BrandListType) => ({
            id: item.id,
            label: item.label,
            url: item.swatch_value_url || '',
          }))
          // 如果brandImgs的长度大于6位，就删除之后的所有数据
          if (brandImgs.value.length > 6) {
            brandImgs.value = brandImgs.value.slice(0, 6)
          }
          console.warn('品牌数据获取成功（备用结构），品牌数量:', brandData.value.length)
        }
      }
      
      // 如果数据仍然为空且重试次数少于3次，则重试
      if (brandData.value.length === 0 && retryCount < 3) {
        console.warn(`品牌数据为空，第${retryCount + 1}次重试...`)
        setTimeout(() => {
          getBrandListData(retryCount + 1)
        }, 1000 * (retryCount + 1)) // 递增延迟
      }
    }
  } catch (error) {
    console.error('获取品牌列表时发生错误:', error);
    // 如果发生错误且重试次数少于3次，则重试
    if (retryCount < 3) {
      console.warn(`获取品牌列表失败，第${retryCount + 1}次重试...`)
      setTimeout(() => {
        getBrandListData(retryCount + 1)
      }, 1000 * (retryCount + 1)) // 递增延迟
    }
  }
}

// 订阅邮箱
const subscribeEmail = async(msg) => {
  const data = {
    email:msg
  }
  const res:IResultData<SubscribeType>= await emailSubscribe(data)
  if (res.success) {
    ElMessage({
      message:res.message,
      grouping: true,
      type: 'success',
    })
  } else {
     ElMessage({
      message:res.message,
      grouping: true,
      type: 'error',
    })
  }
}

const handleScroll = () => {
  // 只在客户端执行
  if (!import.meta.client) return
  
  const currentScroll = window.pageYOffset //表示当前滚动的位置
  if (currentScroll >= 2500) {
    // this.$refs.testref.offsetTop - 100
    isShowSideBar.value = true;
  } else {
    isShowSideBar.value = false;
  }
}

</script>

<style lang="scss" scoped>
.homePage {
  .top {
    width: 100%;
    margin: 0 auto;
    margin-bottom: 11.25rem;

    .swiper-img {
      width: 100%;
      aspect-ratio: 1920/790;
      background-color: #000;

      &.img-active {
        height: 7.5625rem;
      }
    }

    .top-contennt {
      width: 44.88rem;
      height: 6.13rem;
      padding: 0 2.3rem;
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin: -3.065rem auto;
      background-color: #fff;
      position: relative;
      z-index: 5;

  

      // 使用深度选择器确保样式能够应用到Element Plus内部元素
      :deep(.el-select) {
        .el-select__wrapper,
        .el-tooltip__trigger {
          width: 100% !important;
          height: 100% !important;
        }
      }

      // 备用方案：直接针对所有可能的内部元素
      :deep(.el-select__wrapper) {
        width: 100% !important;
        height: 100% !important;
      }

      :deep(.el-tooltip__trigger) {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  .logo {
    width: 100%;
    height: 4.875rem;
    margin: 3.75rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 5.625rem;

    .brand-img {
      height: 4.875rem;
    }
  }

  .logo_1 {
    width: 100%;
    height: 10.9375rem;
  }

  .log-muse {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 8.75rem;

    .muse-text {
      width: 31.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 1rem;
      color: #000000;
      font-style: normal;
    }
  }
  .help{
    cursor: pointer;
    width: 6.25rem;
    height: 6.25rem;
    border-radius: 50%;
    background: #FFFFFF;
    box-shadow: 4px 6px 20px 0px rgba(190,190,190,0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    left: 1rem;
    top: 31.25rem;
    z-index: 10;
    img{
      width: 2.75rem;
      height: 2.75rem;
      .img{
        width: 100%;
        height: 100%;
      }
    }
    .text{
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 1.25rem;
      color: #000000;
      font-style: normal;
    }
  }
  .right-nav{
      cursor: pointer;
      width: 6.12rem;
      height: 16.4375rem;
      border-radius: 1.25rem 0 0 1.25rem;
      background: #FFFFFF;
      box-shadow: .25rem .375rem 1.25rem 0rem rgba(190,190,190,0.5);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: fixed;
      right: 1rem;
      top: 26.875rem;
      z-index: 10;
      gap: 2.8125rem;
    img{
      width: 2.75rem;
      height: 2.75rem;
      .img{
        width: 100%;
        height: 100%;
      }
    }
  }
}
.loop-scroll {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  width: 31.25rem; /* 或者你希望的宽度 */
  height: 3.125rem; /* 或者你希望的固定高度 */
}
 
.loop-scroll p {
  position: absolute;
  width: 100%;
  animation: loopScroll 10s linear infinite;
}
 
@keyframes loopScroll {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

</style>
<template>
  <footer class="footer-wrapper">
    <div class="footer-main">
      <div class="footer-columns w-full">
         <div v-for="(value, key) in footerlinks" :key="key" class="footer-col">
          <div class="footer-col-title font-helve-bold">{{key}}</div>
          <ul>
            <li v-for="item in value" :key="item.sort_order"><NuxtLink :to="item.url" style="cursor: pointer;">{{item.title}}</NuxtLink> </li>
          </ul>
        </div>
        <div class="footer-col">
          <div class="footer-col-title font-helve-bold">{{ $t('footer.newsletter') }}</div>
          <ul>
            <li>
              <span style="display: inline-block;text-decoration: none !important;">{{ $t('footer.stayUpdated') }}</span>
            </li>
            <li>
              <el-input class="subscribe-input" type="email" :placeholder="$t('footer.emailPlaceholder')"/>
            </li>
            <li>
              <div style="display: flex">
                <el-button class="subscribe-btn">{{ $t('footer.subscribe') }}</el-button>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="footer-columns w-full footer-bottom">
      <div class="footer-col">
        <div class="footer-social flex-row items-center">
          <span class="footer-social-icon"><img src="~/assets/images/icon-twitter.svg" alt="twitter"></span>
          <span class="footer-social-icon"><img src="~/assets/images/icon-facebook.svg" alt="facebook"></span>
          <span class="footer-social-icon"><img src="~/assets/images/icon-tiktok.svg" alt="tiktok"></span>
          <span class="footer-social-icon"><img src="~/assets/images/icon-ins.svg" alt="instagram"></span>
        </div>
        <img class="footer-logo" src="~/assets/images/logo.png">
      </div>
      <div class="footer-col">
        <span class="footer-copyright">
          <br>{{ $t('footer.companyName') }}
          <br>{{ $t('footer.companyAddress') }}
          <br>{{ $t('footer.taxId') }}
          <br>{{ $t('footer.copyright') }}
      </span>
      </div>
      <div class="footer-col"/>
      <div class="footer-col"/>
      <div class="footer-col" style="flex: 2">
        <div class="footer-pay-icons">
          <img src="~/assets/images/icon-pay-visa.png" alt="Visa" class="pay-icon" >
          <img src="~/assets/images/icon-pay-master.png" alt="Mastercard" class="pay-icon" >
          <img src="~/assets/images/icon-pay-paypal.png" alt="PayPal" class="pay-icon" >
          <img src="~/assets/images/icon-pay-apple.png" alt="Apple Pay" class="pay-icon" >
          <img src="~/assets/images/icon-pay-amex.png" alt="American Express" class="pay-icon" >
        </div>
      </div>
      </div>
  </footer>
</template>

<script setup lang="ts">
import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言store
const languageStore = useLanguageStore()

// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
}, { immediate: true })

const props = defineProps({
  footerdata: {
    type: Object,
     default() {
      return {};
    }
  }
})
const footerlinks = props.footerdata.footer_links


</script>
<style scoped lang="scss">

.footer-wrapper {
  background: #F1F1F1;
  font-size: .875rem;
  padding: 3.75rem;
  margin: 0;
  width: 100%;
  box-sizing: border-box;

  .footer-main {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0rem;
    width: 100%;
    box-sizing: border-box;
  }

  .footer-columns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .footer-col {
    flex: 1;
    min-width: 0;
    max-width: calc(20% - 1rem);
    box-sizing: border-box;

    &-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1.69rem;
      color: #18141E;
      letter-spacing: 1px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 1rem;
        color: #18141E;
        font-size: .875rem;
        font-family: Helvetica, Arial, sans-serif;
        text-decoration: underline;
        a{
          color: #18141E;
          font-size: .875rem;
          font-family: Helvetica, Arial, sans-serif;
          text-decoration: underline;
        }
      }
    }
  }

  .subscribe-input {
    margin-top: 1rem;
    background-color: white;
    flex: 1;
    border-radius: 0.5rem;
    height: 3.5rem;
    padding: 1.59rem;

    :deep(.el-input__wrapper) {
      padding: 0;
      border: none !important;
      box-shadow: none !important;
    }
    :deep(.el-input__inner) {
      border: none !important;
      outline: none !important;
    }
  }

  .subscribe-btn {
    margin-top: 1rem;
    flex: 1;
    border-radius: 0.5rem;
    height: 3.5rem;
    background-color: #121212;
    font-family: "PingFang SC", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-weight: 600;
    font-size: 1.13rem;
    letter-spacing: 0.13rem;
    color: white;
  }

  .footer-social {
    margin-top: 5.5rem;
    gap: 3.69rem;

    &-icon {
      margin-right: 2.25rem;
      font-size: 1.125rem;
      color: #121212;
      font-weight: bold;
      display: inline-block;
    }
  }

  .footer-logo {
    display: flex;
    align-items: flex-start;
    width: 10.09rem;
    margin-bottom: -2rem;
    // height: 3.56rem;
    img {
      height: 2rem;
      filter: brightness(0) invert(1);
    }
  }

  .footer-copyright {
    color: #999999;
    font-size: 0.83rem;
    line-height: 1.25rem;
    font-weight: 400;
    font-family: "苹方-简 常规体", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
  }

  .footer-bottom {
    align-items: flex-end;
  }

  .footer-pay-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 5.5rem;

    .pay-icon {
      width: 3.63rem;
      opacity: 0.7;
      transition: opacity 0.2s ease;
      // &:hover {
      //   opacity: 1;
      // }
    }
  }
}

/* @media (max-width: 768px) {
  .footer-wrapper {
    padding: 2rem 1rem;
  }
  .footer-columns {
    flex-direction: column;
    gap: 2rem;
  }
  .footer-col {
    max-width: 100%;
    min-width: 100%;
  }
  .footer-bottom {
    flex-direction: column;
    gap: 2rem;
  }
  .footer-pay-icons {
    justify-content: center;
  }
} */

</style>

# 语言一致性修复说明

## 问题描述

`navData` 数据请求的语种和其他几个数据请求语种不对应 `X-Locale` 的问题。

## 问题根源

1. **navData 使用事件总线机制**：在 `useLanguageDataRefresh.ts` 中，`navData` 通过 `useLanguageEventBus` 监听语言变化并重新获取数据。

2. **其他数据请求没有使用事件总线**：其他API请求（如购物车、用户信息等）只依赖于 `request.ts` 和 `noTokenRequest.ts` 中的 `watch` 监听，但这个监听可能不够及时。

3. **时序问题**：当语言切换时，事件总线的触发和请求工具的 `watch` 监听可能存在时序差异。

## 修复方案

### 1. 统一事件总线监听机制

修改了 `utils/noTokenRequest.ts` 和 `utils/request.ts`，让它们都使用事件总线机制来确保语言切换的一致性：

```typescript
// 在 noTokenRequest.ts 和 request.ts 中都添加了
import { useLanguageEventBus } from '~/composables/useLanguageEventBus'

// 在请求函数中添加事件总线监听
const { onLanguageChange } = useLanguageEventBus()

// 使用事件总线监听语言变化，确保及时更新
onLanguageChange((newLanguage: string) => {
  if (newOptions.headers) {
    newOptions.headers['X-Locale'] = newLanguage
    console.warn('🌐 noTokenRequest/request 语言已更新:', newLanguage)
  }
})

// 保留原有的 watch 监听作为备用
watch(currentLanguage, (newVal) => {
  if (newOptions.headers) {
    newOptions.headers['X-Locale'] = newVal
  }
})
```

### 2. 确保所有API请求使用统一的语言处理

现在所有API请求都使用相同的事件总线机制：
- `navData` 相关请求（`noTokenRequest`）
- 购物车、用户信息等请求（`httpRequest`）
- 新闻、公共数据等请求（`noTokenRequest`）

### 3. 双重保障机制

- **主要机制**：事件总线监听，确保语言切换时立即更新
- **备用机制**：`watch` 监听，作为额外的保障

## 修复效果

1. **语言切换一致性**：所有数据请求现在都会在语言切换时立即更新 `X-Locale` 头部
2. **时序同步**：事件总线确保所有监听器在同一时间接收到语言变化事件
3. **调试友好**：添加了控制台日志，便于调试语言切换过程

## 测试建议

1. 切换语言时检查控制台日志，确认所有请求工具都正确更新了语言
2. 验证 `navData` 和其他数据请求的语种是否一致
3. 检查网络请求的 `X-Locale` 头部是否正确设置

## 相关文件

- `utils/noTokenRequest.ts` - 无token请求工具
- `utils/request.ts` - 带token请求工具
- `composables/useLanguageEventBus.ts` - 语言事件总线
- `stores/language/index.ts` - 语言存储
- `composables/useLanguageDataRefresh.ts` - 语言数据刷新

修复完成时间：2024年12月 
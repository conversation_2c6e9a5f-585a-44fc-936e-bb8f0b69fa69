# MLKPLUS Frontend

## 购物车功能使用说明

### 购物车Store功能

购物车store (`stores/shopping-cart/index.ts`) 提供了完整的购物车管理功能，支持未登录时本地缓存和已登录时服务器同步。

#### 主要功能

1. **添加商品到购物车** - 自动判断登录状态
2. **移除购物车商品** - 支持本地和服务器操作
3. **更新商品数量** - 实时同步
4. **购物车数据持久化** - 本地存储
5. **购物车状态检查** - 商品是否在购物车中
6. **颜色和尺寸支持** - 支持商品的颜色和尺寸选项

#### 颜色功能更新 (最新)

购物车现在支持商品颜色选择功能：

- **颜色选择** - 用户必须选择颜色才能添加到购物车
- **颜色存储** - 选中的颜色信息会保存到购物车中
- **颜色区分** - 同一商品的不同颜色会被视为不同的购物车项目
- **颜色检查** - 购物车状态检查会考虑颜色信息

#### 使用示例

```vue
<script setup>
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { ElMessage } from 'element-plus'

const cartStore = useShoppingCartStore()

// 添加商品到购物车（包含颜色）
const handleAddToCart = async (product, selectedColor) => {
  const result = await cartStore.addToCart({
    id: product.id,
    product_id: product.product_id,
    name: product.name,
    price: product.price,
    image: product.image,
    color: selectedColor, // 选中的颜色
    size: selectedSize    // 选中的尺寸（可选）
  }, quantity.value)
  
  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 检查商品是否已在购物车中（考虑颜色）
const isProductInCart = computed(() => {
  return cartStore.isInCart(product.product_id, selectedColor.value, selectedSize.value)
})

// 获取购物车中该商品的数量（考虑颜色）
const cartQuantity = computed(() => {
  return cartStore.getItemQuantity(product.product_id, selectedColor.value, selectedSize.value)
})

// 移除购物车商品
const handleRemoveFromCart = async (itemId) => {
  const result = await cartStore.removeFromCart(itemId)
  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 更新商品数量
const handleUpdateQuantity = async (itemId, newQuantity) => {
  const result = await cartStore.updateQuantity(itemId, newQuantity)
  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 获取购物车统计信息
const totalItems = computed(() => cartStore.totalItems)
const totalPrice = computed(() => cartStore.totalPrice)
</script>

<template>
  <div>
    <!-- 颜色选择 -->
    <div class="color-selector">
      <div 
        v-for="(color, index) in product.colors" 
        :key="index"
        class="color-option"
        :class="{ active: selectedColorIndex === index }"
        :style="{ backgroundColor: color.option_label }"
        @click="selectColor(index)"
      >
        {{ color.option_label }}
      </div>
    </div>
    
    <!-- 购物车图标显示商品数量 -->
    <div class="cart-icon">
      <span class="cart-count">{{ totalItems }}</span>
    </div>
    
    <!-- 添加购物车按钮 -->
    <button 
      @click="handleAddToCart(product, selectedColor)"
      :disabled="!isColorSelected || isProductInCart"
    >
      {{ isProductInCart ? '已在购物车' : (isColorSelected ? '添加到购物车' : '请选择颜色') }}
    </button>
  </div>
</template>
```

#### Store API 说明

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `addToCart` | `product, quantity` | `{success, message}` | 添加商品到购物车 |
| `removeFromCart` | `itemId` | `{success, message}` | 移除购物车商品 |
| `updateQuantity` | `itemId, quantity` | `{success, message}` | 更新商品数量 |
| `fetchCartData` | - | - | 获取购物车数据 |
| `clearCart` | - | - | 清空购物车 |
| `isInCart` | `productId, color?, size?` | `boolean` | 检查商品是否在购物车 |
| `getItemQuantity` | `productId, color?, size?` | `number` | 获取商品数量 |

#### 计算属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `cartItems` | `CartItem[]` | 购物车商品列表 |
| `totalItems` | `number` | 购物车商品总数 |
| `totalPrice` | `number` | 购物车总价 |
| `isLoggedIn` | `boolean` | 用户登录状态 |

#### 注意事项

1. **登录状态自动判断** - store会自动检查用户token状态
2. **数据持久化** - 未登录时的购物车数据会保存在localStorage中
3. **颜色必选** - 如果商品有颜色选项，用户必须选择颜色才能添加到购物车
4. **颜色区分** - 同一商品的不同颜色会被视为不同的购物车项目
5. **API支持** - 后端API已更新以支持颜色和尺寸参数

#### 测试功能

访问 `/test-category` 页面可以测试购物车颜色功能，包括：
- 颜色选择
- 添加到购物车
- 从购物车移除
- 购物车状态检查
- 购物车商品列表显示

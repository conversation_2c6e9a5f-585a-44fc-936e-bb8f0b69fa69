<template>
  <div class="orderDeteils-page">
    <div class="top">
      <div class="title">
        Order Details
      </div>
      <div class="img">
        <img src="~/assets/images/cart.png" alt="">
      </div>
      <div class="shipped">
        Shipped
      </div>
    </div>
    <div class="box">
      <div class="box-top">
        <div class="box-top-header">
          Order #2023101512345
        </div>
        <div class="box-top-th">
          <div class="th-item">Order Date</div>
          <div class="th-item">Payment Method</div>
          <div class="th-item">Shipping Method</div>
        </div>
        <div class="box-top-td">
          <div class="td-item">October 15, 2023, 12:30:45</div>
          <div class="td-item">Credit Card (Visa ending in 4567)</div>
          <div class="td-item">Standard Shipping (3-5 business days)</div>
        </div>
      </div>
      <div class="box-center">
        <div class="box-center-item">
          <div class="th">Shipping Address</div>
          <div class="td"><PERSON></div>
          <div class="td">123 Main Street</div>
          <div class="td">Apt 4B</div>
          <div class="td">New York, NY 10001</div>
          <div class="td">United States</div>
          <div class="td">Phone: (*************</div>
        </div>
        <div class="box-center-item">
          <div class="th">Billing Address</div>
          <div class="td">John Doe</div>
          <div class="td">123 Main Street</div>
          <div class="td">Apt 4B</div>
          <div class="td">New York, NY 10001</div>
          <div class="td">United States</div>
          <div class="td">Phone: (*************</div>
        </div>
      </div>
      <div class="order-items">
        <div class="order-item">
          <div class="order-item-title">
            Order Items
          </div>
          <div class="order">
            <div class="order-left">
              <div class="img">
                <img src="~/assets/images/phone-black.png" alt="">
              </div>
              <div class="text-box">
                <div class="text fontweight">Smart Phone Pro MAX 256GB Black</div>
                <div class="text">SKU: SP-PM-256-BK</div>
                <div class="text">$599.00 x 1</div>
              </div>
            </div>
            <div class="order-right">
              <div class="price">
                $599.00
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-bottom">
        <div class="box-bottom-item">
          <div class="text">
            Subtotal
          </div>
          <div class="text">
            $599.00
          </div>
        </div>
        <div class="box-bottom-item">
          <div class="text fontweight500">
            Shipping
          </div>
          <div class="text fontweight500">
            $12.95
          </div>
        </div>
        <div class="box-bottom-item">
          <div class="text fontweight500">
            Tax
          </div>
          <div class="text fontweight500">
            $48.96
          </div>
        </div>
        <div class="box-bottom-item">
          <div class="text fs20 fontweight500">
            Total
          </div>
          <div class="text fs20 fontweight500">
            $660.91
          </div>
        </div>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.orderDeteils-page{
  width: 65.5rem;
  min-height: 64.0625rem;
  background: #FFFFFF;
  border-radius: .5rem;
  padding: 1.5rem;
  box-sizing: border-box;
  .top{
    display: flex;
    align-items: center;
    .title{
      margin-left: 1.125rem;
      height: 1.8125rem;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: bold;
      font-size: 1.5rem;
      color: #3D3D3D;
      line-height: 1.8125rem;
      text-align: left;
      font-style: normal;
    }
    .img{
      margin-left: .6875rem;
      width: 1.5rem;
      height: 1.5rem;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .shipped{
        margin-left: .375rem;
        height: 1rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 400;
        font-size: .875rem;
        color: #3D3D3D;
        line-height: 1rem;
        text-align: left;
        font-style: normal;
      }
  }
  .box{
    width: 100%;
    .box-top{
      margin-top: 1.375rem;
      width: 100%;
      border: .0625rem solid #EBEBEB;
      .box-top-header{
        padding-left: 1.125rem;
        box-sizing: border-box;
        width: 100%;
        height: 3.1875rem;
        background: #F7F7F7;
        border: 1px solid #EBEBEB;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.25rem;
        color: #3D3D3D;
        line-height: 3.1875rem;
        text-align: left;
        font-style: normal;
      }
      .box-top-th{
        width: 100%;
        height: 3.1875rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom:1px solid #EBEBEB;
        .th-item{
          flex: 1;
          box-sizing: border-box;
          padding-left: 1.125rem;
          width: 88px;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 600;
          font-size: 1.125rem;
          color: #3D3D3D;
          font-style: normal;
        }
      }
      .box-top-td{
        width: 100%;
        height: 3.1875rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom:1px solid #EBEBEB;
        .td-item{
          flex: 1;
          box-sizing: border-box;
          padding-left: 1.125rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1rem;
          color: #3D3D3D;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .box-center{
      margin-top: 1.25rem;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .box-center-item{
        width: 30rem;
        border: 1px solid #EBEBEB;
        .th{
          width: 100%;
          height: 3.1875rem;
          line-height: 3.1875rem;
          background: #F7F7F7;
          border-bottom: 1px solid #EBEBEB;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1.125rem;
          color: #3D3D3D;
          text-align: left;
          padding-left: 1.125rem;
          font-style: normal;
          box-sizing: border-box;
        }
        .td{
          width: 100%;
          height: 3.1875rem;
          line-height: 3.1875rem;
          border-bottom: 1px solid #EBEBEB;
          text-align: left;
          padding-left: 1.125rem;
          font-style: normal;
          box-sizing: border-box;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 16px;
          color: #3D3D3D;
        }
      }
    }
    .order-items{
      margin-top: 1.3125rem;
      box-sizing: border-box;
      padding: 1.125rem 1.5rem;
      width: 100%;
      background: #FFFFFF;
      border: 1px solid #EBEBEB;
      .order-item{
        .order-item-title{
          width: 100%;
          height: 1.75rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1.25rem;
          color: #3D3D3D;
          line-height: 1.75rem;
          text-align: left;
          font-style: normal;
        }
        .order{
          margin-top: .8125rem;
          width: 100%;
          height: 4.25rem;
          display: flex;
          justify-content: space-between;
          .order-left{
            display: flex;
            align-items: center;
            .img{
              width: 4.25rem;
              height: 4.25rem;
              img{
                width: 100%;
                height: 100%;
              }
            }
            .text-box{
              margin-left: 1.6875rem;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .text{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 1rem;
                color: #3D3D3D;
                text-align: left;
                font-style: normal;
                &.fontweight{
                  font-weight: 500;
                }
              }
            }
          }
          .order-right{
            .price{
              font-family: SFProDisplay, SFProDisplay;
              font-weight: bold;
              font-size: 1.25rem;
              color: #3D3D3D;
              font-style: normal;
            }
          }
        }
      }
    }
    .box-bottom{
      width: 100%;
      margin-top: 1.25rem;
      .box-bottom-item{
        width: 100%;
        height: 3.125rem;
        background: #F7F7F7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 1.5rem;
        box-sizing: border-box;
        margin-bottom: .1875rem;
        .text{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #3D3D3D;
          text-align: left;
          font-style: normal;
          &.fs20{
            font-size: 1.25rem;
          }
          &.fontweight500{
             font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
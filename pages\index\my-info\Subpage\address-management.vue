<template>
  <div class="addressManagement-page">
    <div class="top">
      <div class="title">
        Address Management
      </div>
      <div class="add">
        + Add New Address
      </div>
    </div>
    <div class="address-list">
      <div v-for="item in 3 " :key="item" class="address-item">
        <div class="default">
          Default
        </div>
        <div class="address-title">
          Shipping Address
        </div>
        <div class="address-title">
          <PERSON>
        </div>
        <div class="address-text">
          123 Main Street
        </div>
        <div class="address-text">
          Apt 4B
        </div>
        <div class="address-text">
          10001 | New York, NY 10001
        </div>
        <div class="address-text">
          United States
        </div>
        <div class="address-text">
          Phone: (*************
        </div>
        <div class="address-bottom">
          <a href="#">Edit</a>
          <span>Delete</span>
        </div>
      </div>
      <div class="address-add">
        <div class="add-img">
          <el-icon color="#fff" size="30"><Plus /></el-icon>
        </div>
        <div class="add-text">
          Add a New Address
        </div>
      </div>
    </div>
   
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="scss" scoped>
.addressManagement-page {
  width: 70.3125rem;
  min-height: 821px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 1.875rem;
  box-sizing: border-box;

  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      height: 1.8125rem;
      font-family: SFProDisplay, SFProDisplay;
      font-weight: bold;
      font-size: 1.5rem;
      color: #3D3D3D;
      line-height: 1.8125rem;
      text-align: left;
      font-style: normal;
      padding-left: 1.3125rem;
    }
    .add{
      cursor: pointer;
      width: 10.875rem;
      height: 2rem;
      line-height: 2rem;
      border-radius: 8px;
      border: 1px solid #979797;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 1rem;
      color: #3D3D3D;
      text-align: center;
      font-style: normal;
    }
  }
  .address-list{
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    .address-item{
      margin-top: 1.4375rem;
      padding: 1.5rem;
      box-sizing: border-box;
      width: 32.5rem;
      height: 21.375rem;
      background: #FFFFFF;
      border: 1px solid #EBEBEB;
      position: relative;
      .default{
        position: absolute;
        right:1.5rem;
        top: 1.5rem;
        width: 3.75rem;
        height: 1.75rem;
        background: #3D3D3D;
        border-radius: .5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 400;
        font-size: 1rem;
        color: #FFBA60;
        line-height: 1.75rem;
        text-align: center;
        font-style: normal;
      }
      .address-title{
        height: 1.5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1.25rem;
        color: #3D3D3D;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        margin-bottom: 1.125rem;
      }
      .address-text{
        height: 1.1875rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 500;
        font-size: 1rem;
        color: #979797;
        line-height: 1.1875rem;
        text-align: left;
        font-style: normal;
        margin-bottom: 1.125rem;
      }
      .address-bottom{
        a{
          text-decoration: underline;
        }
        span{
          cursor: pointer;
          padding-left: 1.5rem;
        }
      }
    }
    .address-add{
      cursor: pointer;
      box-sizing: border-box;
      margin-top: 1.4375rem;
      width: 32.5rem;
      height: 21.375rem;
      background: #FFFFFF;
      border: .0625rem dashed #BABABA;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .add-img{
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #000;
      }
      .add-text{
        margin-top: 1.0625rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 1.25rem;
        color: #3D3D3D;
        text-align: left;
        font-style: normal;
      }
    }
  }
 

}
</style>
# Footer组件国际化实现

## 概述

本文档描述了为Footer组件 (`components/MLK/Footer.vue`) 添加的国际化支持。

## 实现的功能

### 1. 模板国际化

#### 新闻订阅区域
- 新闻订阅标题：`{{ $t('footer.newsletter') }}`
- 订阅提示文本：`{{ $t('footer.stayUpdated') }}`
- 邮箱输入框占位符：`:placeholder="$t('footer.emailPlaceholder')"`
- 订阅按钮：`{{ $t('footer.subscribe') }}`

#### 公司信息区域
- 公司名称：`{{ $t('footer.companyName') }}`
- 公司地址：`{{ $t('footer.companyAddress') }}`
- 税号：`{{ $t('footer.taxId') }}`
- 版权信息：`{{ $t('footer.copyright') }}`

### 2. 脚本国际化

#### 语言切换支持
- 导入语言store和切换功能
- 监听语言变化并自动切换
- 支持实时语言更新

```typescript
import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言store
const languageStore = useLanguageStore()

// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
}, { immediate: true })
```

### 3. 翻译键更新

#### 新增的翻译键
- `footer.newsletter` - 新闻订阅标题
- `footer.stayUpdated` - 订阅提示文本
- `footer.emailPlaceholder` - 邮箱输入框占位符
- `footer.subscribe` - 订阅按钮文本
- `footer.companyName` - 公司名称
- `footer.companyAddress` - 公司地址
- `footer.taxId` - 税号
- `footer.copyright` - 版权信息

#### 支持的语言
- 中文 (cn.json)
- 英文 (en.json)
- 意大利语 (it.json)

## 翻译内容

### 中文翻译
```json
{
  "footer": {
    "newsletter": "新闻订阅",
    "stayUpdated": "及时了解最新的MLK+系列产品：",
    "emailPlaceholder": "邮箱地址",
    "subscribe": "订阅",
    "companyName": "MLK+ OUSAND S.R.L.",
    "companyAddress": "Viale Bruno Buozzi, 47 - 00197 Roma, ITALIA",
    "taxId": "P. I. 13085581000",
    "copyright": "版权所有 © 2025 MLK+。保留所有权利。"
  }
}
```

### 英文翻译
```json
{
  "footer": {
    "newsletter": "NEWSLETTER",
    "stayUpdated": "Stay updated on the new MLK+ collections:",
    "emailPlaceholder": "E-mail",
    "subscribe": "SUBSCRIBE",
    "companyName": "MLK+ OUSAND S.R.L.",
    "companyAddress": "Viale Bruno Buozzi, 47 - 00197 Roma, ITALIA",
    "taxId": "P. I. 13085581000",
    "copyright": "Copyright © 2025 MLK+. All Rights Reserved."
  }
}
```

### 意大利语翻译
```json
{
  "footer": {
    "newsletter": "NEWSLETTER",
    "stayUpdated": "Rimani aggiornato sulle nuove collezioni MLK+:",
    "emailPlaceholder": "E-mail",
    "subscribe": "ISCRIVITI",
    "companyName": "MLK+ OUSAND S.R.L.",
    "companyAddress": "Viale Bruno Buozzi, 47 - 00197 Roma, ITALIA",
    "taxId": "P. I. 13085581000",
    "copyright": "Copyright © 2025 MLK+. Tutti i diritti riservati."
  }
}
```

## 使用方法

### 1. 语言切换
用户可以通过现有的语言切换器来切换页面语言，Footer组件的所有文本将自动更新。

### 2. 动态内容
- 新闻订阅功能支持多语言
- 公司信息在不同语言下保持一致
- 版权信息根据语言环境显示

### 3. 社交媒体链接
社交媒体图标保持不变，但alt属性可以根据需要添加国际化支持。

## 技术实现

### 1. 导入国际化
```typescript
import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";
```

### 2. 模板中使用
```vue
{{ $t('footer.newsletter') }}
```

### 3. 语言监听
```typescript
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
}, { immediate: true })
```

## 注意事项

1. 所有硬编码的文本都已替换为国际化键
2. 公司地址和税号在所有语言中保持一致（意大利语原文）
3. 版权信息根据语言环境进行了本地化
4. 新闻订阅功能完全支持多语言
5. 社交媒体图标保持原有功能

## 测试建议

1. 测试不同语言环境下的显示效果
2. 验证新闻订阅区域的文本正确翻译
3. 检查公司信息在不同语言下的显示
4. 测试语言切换时的实时更新
5. 验证版权信息的本地化显示

## 后续优化

1. 可以考虑为社交媒体图标的alt属性添加国际化
2. 可以添加更多的交互提示文本
3. 可以考虑添加订阅成功/失败的提示信息国际化 
import httpRequest from "~/utils/request";
import noTokenRequest from "~/utils/noTokenRequest";


interface dataType{
  email?: string,
  password?: string,
  first_name?: string,
  last_name?: string,
  phone?: string,
  user_type?: string,
  password_confirmation?: string,
} 

// 帮助表单数据类型
interface helpFormType {
  email?: string,
  first_name?: string,
  phone?: string,
  message?: string,
}

// 登录
export const login = async (data:dataType) => {
    return noTokenRequest.post('/api/mlk/customer/login',data)
}

// 注册
export const register = async (data:dataType) => {
    return noTokenRequest.post('/api/mlk/customer/register',data)
}

// token刷新
export const registerToken = async () => {
  return httpRequest.post('/api/mlk/customer/refresh-token')
}


// 获取帮助页面的翻译
export const getHelpTranslations = async () => {
  return httpRequest.get('/api/mlk/customer/contact-us/translations')
}

// 发送帮助信息
export const sendHelpInfo = async (data:helpFormType) => {
  return httpRequest.post('/api/mlk/customer/contact-us',data)
}

// 修改密码
export const changePassword = async (data:dataType) => {
  return httpRequest.post('/api/mlk/customer/reset-password',data)
}

// 退出登录
export const logout = async () => {
  return httpRequest.post('/api/mlk/customer/logout')
}

// 获取优惠券列表
export const getCoupons = async () => {
  return httpRequest.get('/api/mlk/coupons')
}

// 验证邮箱
export const verifyAccount = async (data:dataType) => {
  return httpRequest.post('/api/mlk/customer/verify-account',data)
}

// 发送验证邮件
export const resendVerification = async (data:dataType) => {
  return httpRequest.post('/api/mlk/customer/resend-verification',data)
}

// 忘记密码
export const forgotPassword = async (data:dataType) => {
  return httpRequest.post('/api/mlk/customer/forgot-password',data)
}





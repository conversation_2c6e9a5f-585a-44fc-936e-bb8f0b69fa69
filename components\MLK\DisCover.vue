<template>
  <div class="discover">
    <div class="left">
      <div class="left-title">
        {{ aboutData?.title }}
      </div>
      <div class="left-content" :title="aboutData?.data?.content">
        {{ aboutData?.data?.content }}
      </div>
      <div class="btn" @click="$router.push('/news')">
        {{ $t('discover.showAll') }}
      </div>
    </div>
    <div class="right">
      <div class="right-title">
        {{ newsData?.title }}
      </div>
      <div class="right-content">
        <div v-for="item in newsData?.data" :key="item.id" class="right-content-item" @click="$router.push(`/news/detail/${item.url_key}.html`)">
          <div class="img">
            <img :src="item.thumbnail">
          </div>
          <div class="fgx"/>
          <div class="item-title" :title="item.title">
            {{ item.title }}
          </div>
          <div class="item-content" :title="item.description">
            {{ item.description }}
          </div>
          <div class="item-bottom">
            {{ $t('discover.more') }}
          </div> 
        </div>
        
      </div>
      <div class="mask"/>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  aboutData: {
    type: Object,
    required: true
  },
  newsData: {
    type: Object,
    required: true
  }
})
const { aboutData, newsData } = toRefs(props)
</script>

<style lang="scss" scoped>
.discover{
  width: 100%;
  display: flex;
  justify-content:space-between;
  .left{
    box-sizing: border-box;
    padding: 7rem 2.8125rem 0 9.3125rem;
    width: 45.4375rem;
    flex-shrink: 0;
    .left-title{
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 2.5rem;
      color: #000000;
      text-align: left;
      font-style: normal;
    }
    .left-content{
      margin-top: 2.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 1rem;
      color: #000000;
      line-height: 1.5rem;
      text-align: left;
      font-style: normal;
      display: -webkit-box;
      -webkit-box-orient:vertical;
      -webkit-line-clamp:5;
      line-clamp:5;
      overflow:hidden;
    }
    .btn{
      cursor: pointer;
      margin-top: 2.1875rem;
      width: 9.375rem;
      height: 3rem;
      line-height: 3rem;
      border-radius: .5625rem;
      border: 1px solid #000000;
      text-align: center;
      font-family: AppleSystemUIFont;
      font-size: 1rem;
      color: #000000;
      font-style: normal;
      text-transform: none;
      &:hover{
        background-color: #000000;
        color: #fff;
      }
    }
  }
  .right{
    width: 71.1875rem;
    position: relative;
    .right-title{
      height: 3.0625rem;
      font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 2.5rem;
      color: #000000;
      line-height: 3.0625rem;
      letter-spacing: .0625rem;
      text-align: left;
      font-style: normal;
    }
    .right-content{
      margin-top: 3.6875rem;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 2.8125rem;
      height: 31.5rem;
      box-sizing: border-box;
      overflow-x: auto;
      overflow-y: hidden;
      scrollbar-width: none;
      -ms-overflow-style: none;
              .right-content-item{
          flex-shrink: 0;
          width: 17.125rem;
          height: 31.5rem;
          cursor: pointer;
        img{
          width: 17.125rem;
          height: 22.875rem;
          .img{
            width: 100%;
            height: 100%;
          }
        }
        .fgx{
          margin-top: 1.3125rem;
          width: 2.125rem;
          height: .375rem;
          background: #FFBA60;
        }
        .item-title{
          margin-top: .875rem;
          height: 1.5625rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1.125rem;
          color: #000000;
          line-height: 1.5625rem;
          text-align: left;
          font-style: normal;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
        }
        .item-content{
          width: 17.125rem;
          height: 2.375rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: .875rem;
          color: #919191;
          line-height: 1.1875rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
          display: -webkit-box;
          -webkit-box-orient:vertical;
          -webkit-line-clamp:2;
          line-clamp:2;
          overflow:hidden;
        }
        .item-bottom{
          height: 1.3125rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: .9375rem;
          color: #000000;
          line-height: 1.3125rem;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .mask{
      z-index: 10;
      position: absolute;
      top: 0;
      right: 0;
      width: 14.6875rem;
      height: 100%;
      background: linear-gradient( 270deg, #FFFFFF 0%, rgba(255,255,255,0) 100%);
    }
  }
}

.right-content::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

</style>
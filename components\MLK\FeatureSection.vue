<template>
  <div class="home-section">
    <h2 class="home-section-title">Feature Collection</h2>
    <div class="container">
      <div v-for="i in 4" :key="i" class="featured-card">
        <img src="~/assets/images/product-placeholder.png" alt="Product">
        <div class="featured-bottom">
          <div class="featured-card-title">Popular Products</div>
          <button >
            <span class="font-bold">Buy Now</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">

</script>

<style scoped>
.container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 14px;
}

/* 特色系列样式 */
.featured-card {
  background: #E5DDD3;
  padding: 2rem;
  width: 242px;
  aspect-ratio: 26.88 / 40.13;
  border-radius: 0.5rem;
  text-align: center;
}

.featured-card img {
  width: 8rem;
  height: 8rem;
  margin: 0 auto 1rem;
  border-radius: 5px;
  background-color: white;
}

.featured-card-title {
  font-weight: bold;
  font-size: 14px;
  margin: 15px 0px;
}

.featured-card button {

  border: 1px #000000 solid;
  border-radius: 5px;
  background-color: transparent;
  width: 85px;
  height: 38px;
}
.featured-bottom {
  display: flex;
  flex-direction: column;
  align-items: start;
}
</style>

body {
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}

/* 其他样式保持不变 */
.container {
  margin: 0 0;
  padding: 0 0;
}

nav {
  background: white;
}

nav .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  max-width: 1920px;
  margin: 0 auto;
}

.nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-links a {
  text-decoration: none;
  color: #333;
}

.nav-links a:hover {
  color: #666;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-primary {
  background: black;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
}

.home-section {
  padding:  40px 0px;
}

.home-section-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 19px;
}

.btn-outline {
  background: #E5DDD3;
  border: 1px solid black;
  padding: 0.5rem 1.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

a{
  text-decoration: none;
}

.card{
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.07), 0 6px 20px 0 rgba(0, 0, 0, 0.07);
  border-radius: 1.25rem;
}

.font-helve-bold {
  font-family: "Helvetica Bold", Helvetica, Arial, sans-serif;
}

.font-helve {
  font-family: Helvetica, Arial, sans-serif;
}



::v-deep.el-select-dropdown {
  position: absolute !important;
  left: 0px !important;
  top:0px !important;
}



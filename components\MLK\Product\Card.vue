<template>
  <div class="Card_page">
    <div class="Card_content">
      <div class="Card_box" @click="handleCardClick">
        <div v-if="productData?.is_new" class="new-product">
          <span>{{ $t('productCard.new') }}</span>
        </div>
        <div v-if="productData?.discount?.has_discount" class="special-price">
          <span>
            {{productData?.discount?.has_discount ? productData?.discount?.discount_percentage + '%OFF' :''}}
          </span>
        </div>
        <div v-if="productData?.is_featured" class="hot">
          <img :src="useAssetsImage('images/hot.png')" alt="hot">
        </div>
        <div class="Card_image">
          <div class="Card_collect" :class="{ 'on-sale': productData?.on_sale }" @click.stop="handleWishlistToggle(productData)">
            <img 
              :src="isInWishlist ? useAssetsImage('images/love-active.png') : useAssetsImage('images/love.png')" 
              :alt="isInWishlist ? $t('productCard.removeFromWishlist') : $t('productCard.addToWishlist')"
              :class="{ 'wishlist-active': isInWishlist }"
            >
          </div>
          <div class="Card_img">
            <img :src="getProductImage(productData)" :alt="productData?.name">
          </div>
        </div>
        <div v-if="productData?.on_sale" class="discount">
          <span>
            {{ productData?.discount?.discount_percentage + '%'}}
          </span>
          <span>OFF</span>
        </div>
        <div class="Card_color">
           <div 
                v-for="(color,index) in productData?.color" 
                :key="index" 
                class="bg-color" 
                :class="{ 'active': productData.id && selectedColors[productData.id] === index }"
                :style="{ backgroundColor: color.swatch_value, '--hover-color': color.swatch_value }"
                @click.stop="productData.id && selectColor(productData.id, index, color)"
              />
        </div>
        <div class="Card_title" :title="productData?.name">{{productData?.name}}</div>
        <div class="Card_tip" :title="productData?.short_description">{{ productData?.short_description }}</div>
        <div class="Card_amount">{{productData?.formatted_price}}</div>
        <div class="Card_LoginTips">{{ $t('productCard.wholesaleLogin') }} <a href="http://baidu.com">{{ $t('productCard.login') }}</a> <span>{{ $t('productCard.toView') }}</span></div>
        <div class="add" :class="{ 'in-cart': isInCart, 'disabled': !isColorSelected }" @click.stop="addShoppingCart">
          <div class="img">
            <img :src="useAssetsImage('images/add-cart.png')" :alt="$t('productCard.addToCart')">
          </div>
          <div class="text">
            {{ isInCart ? `${$t('productCard.inCart')} (${cartQuantity})` : (isColorSelected ? $t('productCard.addToCart') : $t('productCard.selectColor')) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useWishlistStore } from '~/stores/wishlist'
import { useUserStore } from '~/stores/user'
import { ElMessage } from 'element-plus'

import { useLanguageStore } from "~/stores/language";
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言store
const languageStore = useLanguageStore()

// 监听语言变化
watch(() => languageStore.language, (newLanguage) => {
  switchLanguage(newLanguage)
}, { immediate: true })


// 使用 useAssets composable
const { useAssetsImage } = useAssets()

// 定义产品数据类型
interface ProductData {
  id: number
  name: string
  url_key: string
  base_image: string
  formatted_price: string
  short_description: string
  product_id?: string
  price?: number
  is_new?: boolean
  is_featured?: boolean
  on_sale?: boolean
  discount?: {
    has_discount: boolean
    discount_percentage: number
  }
  special_price?:string
  color?: { option_label: string; swatch_value?: string }[];
  selectedColor?: string
}

const props = defineProps<{
  productData: ProductData
}>()

const emit = defineEmits<{
  'add-to-cart': [product: ProductData]
}>()

// 获取购物车store
const cartStore = useShoppingCartStore()

// 心愿单 store
const wishlistStore = useWishlistStore()

// 用户store
const userStore = useUserStore()

// 路由
const router = useRouter()

// 计算属性：检查商品是否在心愿单中
const isInWishlist = computed(() => {
  return wishlistStore.isInWishlist(props.productData.id)
})

// 检查商品是否已在购物车中
const isInCart = computed(() => {
  const selectedColorIndex = selectedColors.value[props.productData.id]
  const selectedColor = props.productData.color && props.productData.color[selectedColorIndex]
  const colorValue = selectedColor ? selectedColor.option_label : undefined
  
  return cartStore.isInCart(
    Number(props.productData.product_id) || props.productData.id,
    colorValue
  )
})

// 获取购物车中该商品的数量
const cartQuantity = computed(() => {
  const selectedColorIndex = selectedColors.value[props.productData.id]
  const selectedColor = props.productData.color && props.productData.color[selectedColorIndex]
  const colorValue = selectedColor ? selectedColor.option_label : undefined
  
  return cartStore.getItemQuantity(
    Number(props.productData.product_id) || props.productData.id,
    colorValue
  )
})

// 存储每个产品选中的颜色索引
const selectedColors = ref<Record<number, number>>({})

// 检查是否已选择颜色
const isColorSelected = computed(() => {
  if (!props.productData.id || !props.productData.color || props.productData.color.length === 0) {
    return true // 如果没有颜色选项，则允许添加
  }
  return selectedColors.value[props.productData.id] !== undefined
})

// 获取产品图片的方法
const getProductImage = (product: ProductData) => {
    if (!product.id) return product.base_image || ''
    const selectedColorIndex = selectedColors.value[product.id]
    if (selectedColorIndex !== undefined && product.color && product.color[selectedColorIndex]) {
        const selectedColor = product.color[selectedColorIndex]
        // 如果颜色选项有swatch_value（图片路径），则使用它
        if (selectedColor.swatch_value) {
            return selectedColor.swatch_value
        }
    }
    
    // 默认返回base_image
    return product.base_image || ''
}

// 选择颜色的方法
const selectColor = (productId: number, colorIndex: number, _color: { option_label: string; swatch_value?: string }) => {
  selectedColors.value[productId] = colorIndex
}

const addShoppingCart = () => {
  console.warn('MLKProductCard: 点击添加购物车按钮')
  
  // 检查是否已选择颜色
  if (!isColorSelected.value) {
    ElMessage.warning($t('productCard.selectColorFirst'))
    return
  }
  
  if (props.productData?.id) {
    // 获取选中的颜色信息
    const selectedColorIndex = selectedColors.value[props.productData.id]
    const selectedColor = props.productData.color && props.productData.color[selectedColorIndex]
    
    // 创建包含颜色信息的产品数据
    const productWithColor = {
      ...props.productData,
      selectedColor: selectedColor ? selectedColor.option_label : undefined
    }
    
    console.warn('MLKProductCard: 发射 add-to-cart 事件，包含颜色信息:', productWithColor)
    emit('add-to-cart', productWithColor)
  } else {
    console.error('MLKProductCard: productData.id 不存在', props.productData)
  }
}

// 处理卡片点击跳转到详情页
const handleCardClick = () => {
  if (!props.productData?.url_key) {
    return
  }

  // 检查用户是否是批发用户
  const isWholesaleUser = userStore.isWholesaleUser()
  
  // 根据用户类型跳转到不同的详情页
  if (isWholesaleUser) {
    // 批发用户跳转到批发详情页
    router.push(`/product-wholesale-details/${props.productData?.url_key}.html`)
  } else {
    // 普通用户跳转到零售详情页
    router.push(`/product-details/${props.productData?.url_key}.html`)
  }
}

const handleWishlistToggle = async (product: ProductData) => {
  try {
    if (isInWishlist.value) {
      // 如果已收藏，则取消收藏
      const wishlistItem = wishlistStore.getWishlistItem(product.id)
      if (wishlistItem) {
        const result = await wishlistStore.removeFromWishlist(wishlistItem.id)
        if (result.success) {
          ElMessage.success($t('productCard.wishlistRemoved'))
        } else {
          ElMessage.error(result.message || $t('productCard.wishlistRemoveFailed'))
        }
      }
    } else {
      // 如果未收藏，则添加收藏
      const result = await wishlistStore.addToWishlist({
        id: product.id,
        url_key: product.url_key,
        name: product.name,
        formatted_base_price: product.formatted_price,
        formatted_price: product.formatted_price,
        base_image: product.base_image,
        discount: {
          has_discount: false,
          regular_price: 0,
          is_special_price_active: false,
          special_price: 0,
          special_price_from: '',
          special_price_to: ''
        },
        in_stock: true
      })
      
      if (result.success) {
        // 添加成功提示
        ElMessage.success($t('productCard.wishlistAdded'))
      } else {
        // 添加失败提示
        ElMessage.error(result.message || $t('productCard.wishlistAddFailed'))
      }
    }
  } catch (error) {
    console.error('收藏操作出错:', error)
    ElMessage.error($t('productCard.wishlistError'))
  }
}

// 组件挂载时初始化心愿单数据
onMounted(async () => {
  await wishlistStore.initWishlist()
})
</script>

<style scoped lang="scss">
.Card_page {
  width: 19.375rem;
  height: 30.625rem;
  .Card_content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;

      .Card_box {
        width: 19.375rem;
        height: 100%;
        border-radius: .5625rem;
        border: .0625rem solid rgba(222, 222, 222, 1);
        position: relative;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          border-color: #FE8C5F;
        }
        
        .new-product {
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 3.5rem solid #FFBA60;
          border-right: 3.5rem solid transparent;
          z-index: 100;
          border-top-left-radius: .5625rem;
          
          span {
            display: inline-block;
            width: 2.5rem;
            position: absolute;
            bottom: 1rem;
            left: 0;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: .75rem;
            color: #FFFFFF;
            text-align: center;
            font-style: normal;
            line-height: .75rem;
            white-space: nowrap;
            transform: rotate(-45deg);
            transform-origin: 0 0;
            z-index: 101;
          }
        }
        .hot{
          position: absolute;
          top: 1.5rem;
          left: 1.5rem;
          width: 2rem;
          height: 2rem;
          z-index: 100;
          img{
            width: 100%;
            height: 100%;
          }
        }
        .special-price{
          width: 3.25rem;
          height: 3.25rem;
          background: url(~/assets/images/special-price.png);
          background-size: 100% 100%;
          position: absolute;
          right: .9375rem;
          top: .9375rem;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 99;

          span{
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 1rem;
            color: #FFFFFF;
            text-align: center;
            font-style: normal;
            line-height: 1rem;
            white-space: normal;
          }
        }
        .Card_image {
          width: 100%;
          height: 13.125rem;
          position: relative;

          .Card_img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 13.0625rem;
            position: absolute;
            img{
              height: 100%;
            }
          }

          .Card_collect {
            width: 2rem;
            height: 2rem;
            cursor: pointer;
            position: absolute;
            right: 1.75rem;
            top: 1.25rem;
            z-index: 10;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            &.on-sale{
              right: 1.75rem;
              top: 4.375rem;
            }
        
            
            &:hover{
              transform: scale(1.1);
            }
            
            img{
              width: 100%;
              height: 100%;
              transition: all 0.3s ease;
              
              &.wishlist-active{
                animation: heartBeat 0.6s ease-in-out;
              }
            }
          }
        }
        .discount{
          position: absolute;
          top: .9375rem;
          right: .9375rem;
          width: 3.25rem;
          height: 3.25rem;
          background: url(~/assets/images/special-price.png);
          background-size: 100% 100%;
          z-index: 100;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          span{
            display: inline-block;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 600;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 16px;
            text-align: left;
            font-style: normal;
          }
        }

      .Card_color {
        margin: 0 auto;
        margin-top: .375rem;
        box-sizing: border-box;
        width: 100%;
        height: 1.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0 .5rem;

        .bg-color {
          width: 1.3125rem;
          height: 1.3125rem;
          border-radius: 50%;
          cursor: pointer;
          box-shadow: inset .0625rem .125rem .125rem 0rem rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
          }
          
          &.active {
            box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
            transform: scale(1.1);
          }
        }
      }

      .Card_title {
        margin-top: 1.875rem;
        padding: 0 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 1.125rem;
        color: #000000;
        text-shadow: -0.125rem .1875rem .75rem rgba(202, 202, 202, 0.5);
        text-align: left;
        font-style: normal;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
      }

      .Card_tip {
        margin: .3125rem 0;
        padding: 0 1.5rem;
        font-family: SFNS, SFNS;
        font-weight: 400;
        font-size: .8125rem;
        color: #979797;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: -webkit-box;
        -webkit-box-orient:vertical;
        -webkit-line-clamp:2;
        line-clamp:2;
        overflow:hidden;
      }

      .Card_amount {
        margin-top: .625rem;
        padding: 0 1.5rem;
        height: 1.5rem;
        font-family: AppleSystemUIFont;
        font-size: 1.5rem;
        color: #000000;
        line-height: 1.5rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .Card_LoginTips {
        margin-top: .625rem;
        padding: 0 1.5rem;
        height: 1.1875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: .875rem;
        color: #3D3D3D;
        line-height: 1.1875rem;
        text-align: left;
        font-style: normal;
        text-transform: none;

        a {
          color: #FE8C5F;
          text-decoration: underline;
        }

        span {
          color: #919191;
        }
      }

      .add {
          cursor: pointer;
          width: 16.4375rem;
          height: 2.5rem;
          background: #3D3D3D;
          border-radius: .5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          transition: all 0.3s ease;
          position: absolute;
          left: calc(50% - 8.21875rem);
          bottom: 1.25rem;
          
          &.in-cart {
            background: #4CAF50;
            
            &:hover {
              background: #45a049;
            }
            
            &:active {
              background: #3d8b40;
            }
          }
          
          &.disabled {
            background: #cccccc;
            cursor: not-allowed;
            opacity: 0.6;
            
            &:hover {
              background: #cccccc;
              transform: none;
              box-shadow: none;
            }
            
            &:active {
              background: #cccccc;
              transform: none;
              box-shadow: none;
            }
          }
          
          &:not(.disabled):hover {
            background: #555555;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
          
          &:not(.disabled):active {
            background: #2a2a2a;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          }

        .img {
          width: 1.5rem;
          height: 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 80%;
            height: 80%;
          }
        }

        .text {
          height: 18px;
          font-family: Helvetica;
          font-size: 15px;
          color: #FFFFFF;
          line-height: 18px;
          text-align: right;
          font-style: normal;
        }
      }

    }
  }
}

// 心跳动画
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}
</style>
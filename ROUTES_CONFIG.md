# My-Info 路由配置

## 概述
根据 `pages/index/my-info/` 目录下的文件结构，已配置以下路由：

## 路由列表

### 主路由
- **路径**: `/index/my-info`
- **文件**: `pages/index/my-info/index.vue`
- **功能**: 个人中心主页面，包含侧边栏导航

### 子路由

1. **心愿单页面**
   - **路径**: `/index/my-info/wishlist`
   - **文件**: `pages/index/my-info/wishlist.vue`
   - **功能**: 用户心愿单管理

2. **地址管理页面**
   - **路径**: `/index/my-info/addressManagement`
   - **文件**: `pages/index/my-info/addressManagement.vue`
   - **功能**: 用户地址管理

3. **优惠券页面**
   - **路径**: `/index/my-info/coupons`
   - **文件**: `pages/index/my-info/coupons.vue`
   - **功能**: 用户优惠券管理

4. **联系我们页面**
   - **路径**: `/index/my-info/contactUs`
   - **文件**: `pages/index/my-info/contactUs.vue`
   - **功能**: 联系客服

5. **物流跟踪页面**
   - **路径**: `/index/my-info/shipmentTracking`
   - **文件**: `pages/index/my-info/shipmentTracking.vue`
   - **功能**: 订单物流跟踪

6. **修改密码页面**
   - **路径**: `/index/my-info/changePassword`
   - **文件**: `pages/index/my-info/changePassword.vue`
   - **功能**: 用户密码修改

7. **订单详情页面**
   - **路径**: `/index/my-info/orderDeteils`
   - **文件**: `pages/index/my-info/orderDeteils.vue`
   - **功能**: 订单详细信息

8. **发票详情页面**
   - **路径**: `/index/my-info/invoiceDetail`
   - **文件**: `pages/index/my-info/invoiceDetail.vue`
   - **功能**: 发票详细信息

9. **登录页面**
   - **路径**: `/index/my-info/login`
   - **文件**: `pages/index/my-info/login.vue`
   - **功能**: 用户登录信息

10. **表格页面**
    - **路径**: `/index/my-info/table`
    - **文件**: `pages/index/my-info/table.vue`
    - **功能**: 发票列表表格

11. **订单页面**
    - **路径**: `/index/my-info/older`
    - **文件**: `pages/index/my-info/older.vue`
    - **功能**: 订单管理

## 中间件配置
所有页面都配置了 `user-auth` 中间件，确保用户身份验证。

## 路由特点
- 基于 Nuxt.js 文件系统路由
- 使用 `definePageMeta` 配置页面元数据
- 支持嵌套路由结构
- 统一的用户认证中间件

## 导航配置
在 `pages/index/my-info/index.vue` 中配置了侧边栏导航，包含所有子页面的链接。 
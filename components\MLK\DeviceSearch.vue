<template>
  <div class="device-search-wrapper"/>
    <div class="device-search">
      <span>FIND YOUR<br >DEVICE</span>
      <br >
      <select>
        <option>Select brand</option>
      </select>
      <select>
        <option>Select device</option>
      </select>
  </div>
</template>

<script setup>

</script>

<style scoped>

.device-search-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100vw;
}

/* 产品搜索样式 */
.device-search {
  display: flex;
  align-items: center;
  justify-content: center;

  width: 410px;
  border-radius: 10px;
  background: black;
  color: #FFBA60;
  gap: 15px;
  padding: 15px 27px 15px 30px;

  span {
    font-size: 11px;
    font-weight: bold;
    line-height: 15px;
  }
}

.device-search select {
  width: 104px;
  height: 27px;
  padding: 0px 6px;
  background: black;
  font-size: 9px;
  color: white;
  border: 1px solid white;
  border-radius: 5px;
}
</style>
import type { UseFetchOptions } from "nuxt/app";
import { ElMessage } from 'element-plus'
import { useLanguageStore } from '~/stores/language';
import { useLanguageEventBus } from '~/composables/useLanguageEventBus';


// HTTP 请求的方法类型
type Methods = "GET" | "POST" | "DELETE" | "PUT";


// 请求结果数据格式
export interface IResultData<T> {
  locale: string;
  data: T;
  message: string;
  success: boolean
}

/**
 * api请求封装，使用useFetch函数或$fetch函数
 * @param { string } url 请求地址
 * @param { string } method 请求方法
 * @param { object } data 请求数据
 * @param { UseFetchOptions } options 请求选项
 */
class HttpRequest {
  request<T>(url: string, method: Methods, data, options?: UseFetchOptions<T>) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return new Promise<IResultData<any>>((resolve, reject) => {
      const config = useRuntimeConfig();
      const BASE_URL = config.public.apiBase || 'http://mlk.shiyus.com';
      // 继承UseFetchOptions类型，包含了baseURL和method两个属性
      const newOptions: UseFetchOptions<T> = {
        baseURL: BASE_URL,
        method,
        ...options,
      };
      console.warn('newOptions', newOptions);
      const languageStore = useLanguageStore()
      const currentLanguage = computed(() => languageStore.language)
      const { onLanguageChange } = useLanguageEventBus()
      
      newOptions.headers = {
        'Accept': 'application/json',
        'X-Locale': currentLanguage.value
      }
      
      // 使用事件总线监听语言变化，确保及时更新
      onLanguageChange((newLanguage: string) => {
        if (newOptions.headers) {
          newOptions.headers['X-Locale'] = newLanguage
          console.warn('🌐 noTokenRequest 语言已更新:', newLanguage)
        }
      })
      
      // 保留原有的 watch 监听作为备用
      watch(currentLanguage, (newVal) => {
        if (newOptions.headers) {
          newOptions.headers['X-Locale'] = newVal
        }
      })



      // 根据请求方法处理请求的数据
      if (method === "GET" || method === "DELETE") {
        // 将数据设置为newOptions的params属性
        newOptions.params = data;
      }
      if (method === "POST" || method === "PUT") {
        // 将数据设置为newOptions的body属性
        newOptions.body = data;
      }

      // 选择合适的请求方法
      const fetchMethod = useFetch;
      
             // 发送请求
      fetchMethod(url, newOptions)
         .then((res) => {
           console.warn('=== Fetch response 开始 ===');
           console.warn('res:', res);
           console.warn('res.error:', res.error);
           console.warn('res.error.value:', res.error?.value);
           console.warn('res.error.value?.data:', res.error?.value?.data);
           console.warn('res.data:', res.data);
           console.warn('res.data.value:', res.data?.value);
           
                       // 检查是否有错误信息
            if (res.error?.value?.data) {
              console.warn('✅ 发现错误信息，返回错误数据:', res.error.value.data);
              resolve(res.error.value.data as unknown as IResultData<unknown>);
              return;
            }
            
            // 检查正常响应
            if (res.data?.value) {
              console.warn('✅ 正常响应，返回数据:', res.data.value);
              resolve(res.data.value as unknown as IResultData<unknown>);
              return;
            }
            
            // 检查其他可能的响应格式
            if (res.data) {
              console.warn('✅ 其他响应格式，返回数据:', res.data);
              resolve(res.data as unknown as IResultData<unknown>);
              return;
            }
            
            // 如果都没有，返回空对象
            console.warn('❌ 没有找到有效响应数据，返回空对象');
            resolve({
              success: false,
              message: '请求响应为空',
              data: null,
              locale: 'en'
            } as IResultData<null>);
            console.warn('=== Fetch response 结束 ===');
         })
        .catch((error) => {
          let errorMessage = "服务端错误";
          if (error.response && error.response._data) {
            let data = error.response._data;
            if (typeof error.response._data === 'string') {
              try {
                data = JSON.parse(error.response._data);
              } catch {
                errorMessage = error.response._data;
              }
            }
            if (data.errors) {
              const errorMessages: string[] = [];
              for (const key in data.errors) {
                errorMessages.push(`${data.errors[key].join(', ')}`);
              }
              errorMessage = errorMessages.join('; ') || errorMessage;
            } else {
              errorMessage = data.message || errorMessage;
            }
          }
          if (import.meta.client) {
            ElMessage.error(errorMessage)
          }

          if (error.response &&
            (error.response._data.code === 40001 || error.response._data.code === 40002 || error.response._data.code === 40003)) {
            resolve(error.response._data);
          }
          reject(error.response ? error.response._data : errorMessage);
        });
    });
  }

  // 封装常用方法
  get<T>(url: string, params?: T, options?: UseFetchOptions<T>) {
    return this.request(url, "GET", params, options);
  }

  post<T>(url: string, data?: T, options?: UseFetchOptions<T>) {
    return this.request(url, "POST", data, options);
  }

  put<T>(url: string, data: T, options?: UseFetchOptions<T>) {
    return this.request(url, "PUT", data, options);
  }

  delete<T>(url: string, params: T, options?: UseFetchOptions<T>) {
    return this.request(url, "DELETE", params, options);
  }
}

// 实例化 HttpRequest 并导出
const httpRequest = new HttpRequest();
export default httpRequest;
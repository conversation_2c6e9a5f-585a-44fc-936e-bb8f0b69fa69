<template>
  <div class="new-arrival">
    <ClientOnly>
      <MLKSwiper 
        v-if="!isLoading && bannerList.length > 0" 
        ref="swiperRef"
        :key="`swiper-${bannerList.length}-${swiperKey}`"
        class="swiper-img"  
        :data-list="bannerList" 
      />
      <template #fallback>
        <div class="swiper-img" style="background-color: #f5f5f5; display: flex; align-items: center; justify-content: center;">
          <div>{{ $t('newArrival.loading') }}</div>
        </div>
      </template>
    </ClientOnly>
     <div class="filter">
        <div class="select-left">
          <div class="filter-text">
            {{ $t('newArrival.filter') }}
          </div>
          <el-select
            v-model="selectedBrand"
            :placeholder="$t('newArrival.brand')"
            size="large"
            style="width: 9.375rem"
            @change="handleBrandChange"
          >
          <el-option
            v-for="item in brandOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
          </el-select>
          <el-select
            v-model="selectedDevice"
            :placeholder="$t('newArrival.device')"
            size="large"
            style="width: 9.375rem"
            @change="handleDeviceChange"
          >
            <el-option
              v-for="item in deviceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
     </div>
     <div class="content">
      <div v-if="loading" class="loading-container">
        <el-loading-spinner />
        <p>{{ $t('newArrival.loadingNewProducts') }}</p>
      </div>
      
      <div v-else-if="productList.length === 0" class="empty-container">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3>{{ $t('newArrival.noNewProducts') }}</h3>
        <p>{{ $t('newArrival.noNewProductsDesc') }}</p>
      </div>
      
      <MLKNewArrivalCard 
        v-for="item in productList"
        v-else 
        :key="item.id" 
        style="width: 16.75rem;height: 30.625rem;"
        :product-data="item"
        @add-to-cart="handleAddToCart"
      />
     </div>
      <div v-if="!loading && productList.length > 0" class="pagination">
        <el-pagination 
          background 
          layout="prev, pager, next" 
          size="small" 
          :total="total" 
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
        />
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getHomePageData } from "~/api/channel"
import { getProductSearch, getBrandList } from '~/api/public'
import { ElMessage } from 'element-plus'
import { useShoppingCartStore } from '~/stores/shopping-cart'

// 获取国际化函数
const { t } = useI18n()

// 产品类型定义
interface Product {
  id: number
  name: string
  base_image: string
  formatted_price: string
  short_description: string
  color: { option_label: string; swatch_value?: string }[]
}

interface DeviceType {
  id: number;
  label: string;
}

interface BrandListType {
  id: number;
  label: string;
  devices?: DeviceType[];
  swatch_value_url?: string;
}

interface OptionType {
  value: number | string;
  label: string;
}

interface RootObject {
  id: number;
  title: string;
  url: string;
  content?: string;
  image?: string;
  link?: string;
}

interface HomepageType {
  index_banner?: {
    data?: {
      images?: RootObject[];
    };
  };
}

interface homeDataType{
  homepage: HomepageType,
  is_wholesale: boolean
}

// 搜索参数
const searchParams = ref<Record<string, string>>({
  query: '',
  category_id: '',
  price: '',
  sort: '',
  order: '',
  limit: '12',
  page: '1',
  new: '1', // 新品标识
  brand: '',
  device: ''
})

// 品牌和设备选择
const selectedBrand = ref<number | string>('')
const selectedDevice = ref<number | string>('')

// 监听品牌选择变化，更新设备选项
watch(selectedBrand, (newBrandId) => {
  if (newBrandId && brandData.value.length > 0) {
    const selectedBrandData = brandData.value.find(brand => brand.id === newBrandId)
    if (selectedBrandData && selectedBrandData.devices) {
      deviceOptions.value = [
        { value: '', label: t('newArrival.allDevices') },
        ...selectedBrandData.devices.map(device => ({
          value: device.id,
          label: device.label
        }))
      ]
      // 重置设备选择
      selectedDevice.value = ''
      searchParams.value.device = ''
    } else {
      deviceOptions.value = [
        { value: '', label: t('newArrival.allDevices') }
      ]
      selectedDevice.value = ''
      searchParams.value.device = ''
    }
  } else {
    deviceOptions.value = [
      { value: '', label: t('newArrival.allDevices') }
    ]
    selectedDevice.value = ''
    searchParams.value.device = ''
  }
})

// 产品数据
const productList = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)
const loading = ref(false)

// 筛选选项
const brandOptions = ref<OptionType[]>([])
const deviceOptions = ref<OptionType[]>([])
const brandData = ref<BrandListType[]>([]) // 存储完整的品牌数据

// 获取产品和品牌下拉框数据列表
const getBrandListData = async() => {
  try {
    const res: IResultData<BrandListType[]> = await getBrandList()
    if (res && res.success && Array.isArray(res.data)) {
      // 存储完整的品牌数据
      brandData.value = res.data
      // 设置品牌选项，添加"全部"选项
      brandOptions.value = [
        { value: '', label: t('newArrival.allBrands') },
        ...res.data.map((item:BrandListType) => ({
          value: item.id,
          label: item.label
        }))
      ]
    } else {
      // 如果数据不是数组，尝试其他可能的数据结构
      if (res?.data && typeof res.data === 'object' && !Array.isArray(res.data)) {
        // 可能是 { brands: [...] } 这样的结构
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const brands = (res.data as any).brands || (res.data as any).data || [];
        if (Array.isArray(brands)) {
          brandData.value = brands
          brandOptions.value = [
            { value: '', label: t('newArrival.allBrands') },
            ...brands.map((item:BrandListType) => ({
              value: item.id,
              label: item.label
            }))
          ]
        }
      }
    }
  } catch (error) {
    console.error('获取品牌列表失败:', error)
  }
}


// 获取首页数据方法
const getHomeData = async ():Promise<homeDataType> => {
  try {
    const res = await getHomePageData()
    
    // 处理不同的数据结构
    if (res && typeof res === 'object') {
      // 如果 res 本身就是数据
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((res as any).homepage || (res as any).is_wholesale !== undefined) {
        return res as unknown as homeDataType
      }
      // 如果 res 有 data 属性
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((res as any).data) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return (res as any).data as homeDataType
      }
    }
    
    // 如果都不匹配，返回空数据
    return {
      homepage: {},
      is_wholesale: false
    }
  } catch (error) {
    console.error('API 调用失败:', error)
    throw error // 重新抛出错误，让上层处理
  }
}

// 搜索产品方法
const searchProducts = async () => {
  try {
    loading.value = true
    
    // 过滤掉空值参数
    const filteredParams: Record<string, string> = {}
    Object.entries(searchParams.value).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        filteredParams[key] = value.trim()
      }
    })
    
    console.warn('调用新品接口参数:', filteredParams)
    
    const res = await getProductSearch(filteredParams)
    
    if (res && res.data) {
      productList.value = res.data.products || []
      total.value = res.data.total || 0
      console.warn('新品接口返回数据:', res.data)
    } else {
      productList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('搜索新品失败:', error)
    productList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理品牌变化
const handleBrandChange = () => {
  searchParams.value.brand = selectedBrand.value ? selectedBrand.value.toString() : ''
  currentPage.value = 1
  searchParams.value.page = '1'
  searchProducts()
}

// 处理设备变化
const handleDeviceChange = () => {
  searchParams.value.device = selectedDevice.value ? selectedDevice.value.toString() : ''
  currentPage.value = 1
  searchParams.value.page = '1'
  searchProducts()
}


// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  searchParams.value.page = page.toString()
  searchProducts()
}

const bannerList = ref<RootObject[]>([])
const isLoading = ref(true)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperRef = ref<{ manualInitSwiper: () => void; isInitialized: any } | null>(null)
const swiperKey = ref(0)

// 使用 async/await 确保数据加载完成
const loadData = async () => {
  try {
    const data = await getHomeData()
    const { homepage } = data

    // 轮播图数据
    const rawImages = homepage?.index_banner?.data?.images || []
    
    // 转换数据结构以匹配Swiper组件的要求
    bannerList.value = rawImages.map((item: { id: number; title?: string; image?: string; url?: string; link?: string }) => {
      const imageUrl = item.image || item.url || ''
      // 确保图片URL是完整的
      const fullImageUrl = imageUrl.startsWith('http') ? imageUrl : `http://mlk.shiyus.com${imageUrl}`
      return {
        id: item.id,
        title: item.title || '',
        url: item.url || item.image || '',
        image: fullImageUrl,
        link: item.link || item.url || ''
      }
    })
    
    isLoading.value = false
  } catch (error) {
    console.error('加载数据失败:', error)
    isLoading.value = false
  }
}

// 获取购物车store实例
const cartStore = useShoppingCartStore()

// 处理添加购物车
const handleAddToCart = async (product: {
    id: number
    name: string
    base_image: string
    formatted_price: string
    short_description: string
    product_id?: string
    price?: number
    selectedColor?: string
}) => {
    try {
        console.warn('添加商品到购物车:', product)
        // 调用购物车store的添加方法
        const result = await cartStore.addToCart({
            id: product.id.toString(),
            product_id: product.product_id || product.id.toString(),
            name: product.name,
            price: product.price || parseFloat(product.formatted_price.replace(/[^0-9.]/g, '')),
            base_image: product.base_image,
            formatted_price: product.formatted_price,
            short_description: product.short_description,
            selectedColor: product.selectedColor
        }, 1)
        
        if (result.success) {
            // 显示成功提示
            ElMessage.success(t('newArrival.addToCartSuccess'))
        } else {
            // 显示错误提示
            ElMessage.error(result.message || t('newArrival.addToCartFailed'))
        }
    } catch (error) {
        console.error('添加购物车失败:', error)
        ElMessage.error(t('newArrival.addToCartError'))
    }
}

// 在组件挂载时加载数据
onMounted(async() => {
  if (import.meta.client) {
    await nextTick()
  }
  await loadData()
  await searchProducts()
  await getBrandListData()
})
</script>

<style lang="scss" scoped>
.new-arrival{
  width: 100%;
  .swiper-img {
    width: 100%;
    height: 43.75rem;
    background-color: #f5f5f5;
  }
  .img {
      width: 100%;
      aspect-ratio: 1920/790;
      background-color: #000;

      &.img-active {
        height: 7.5625rem;
      }
    }
    .filter{
      width: 71.875rem;
      margin: 2.625rem auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
        .select-left{
          display: flex;
          align-items: center;
          .filter-text{
            padding-right: .3125rem;
            width: 2.375rem;
            height: 3rem;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 500;
            font-size: 1.125rem;
            color: #000000;
            line-height: 3rem;
            text-align: left;
            font-style: normal;
          }
          .el-select{
            margin-left: 1.25rem;
            ::v-deep.el-select--large .el-select__wrapper{
              font-size: 14px;
              height: 48px;
              gap: 6px;
              line-height: 48px;
              min-height: 48px;
              padding: 8px 16px;
              span{
                color: #3D3D3D;
              }
              
            }
          }
        }
    
      
    }
    .content{
      width: 1150px;
      margin: 2.3125rem auto;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      gap: 1.625rem;
      
      .loading-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 0;
        
        p {
          margin-top: 1rem;
          color: #666;
          font-size: 1rem;
        }
      }
      
      .empty-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 0;
        
        .empty-icon {
          margin-bottom: 1rem;
        }
        
        h3 {
          margin: 0 0 0.5rem 0;
          color: #333;
          font-size: 1.5rem;
        }
        
        p {
          margin: 0.25rem 0;
          color: #666;
          font-size: 1rem;
        }
      }
    }
     .pagination{
        width: 100%;
        margin: 5rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        ::v-deep .el-pagination.is-background .el-pager li.is-active{
          background-color: #000 !important;
          color: #fff;
        }
      }
}
</style>
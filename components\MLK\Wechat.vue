<template>
  <div class="help">
    <div class="top">
      <div class="top-logo">
        <img src="~/assets/images/logo-white.png" alt="logo">
      </div>
      <div class="text">
        <PERSON><PERSON><PERSON> is your trusted B2B partner. Enjoy fast global shipping (4–7 days), secure 
        payment, no MOQ, and factory prices. Reliable, affordable.
      </div>
    </div>
    
    <div class="content">
      <div class="dialogue">
        <div class="customer">
          <div class="customer-img">
            <el-icon color="#fff" size="30"><Service /></el-icon>
          </div>
          <div class="message">
            Hi there! How can we help you?
          </div>
        </div>
        <div class="client">
          <div class="message">
            Hi there! How can we help you?
          </div>
          <div class="client-img">
            <el-icon color="#fff" size="30"><Avatar /></el-icon>
          </div>
        
        </div>

      </div>
      <div class="dialogue-input">
        <input type="text" placeholder="Hi！">
        <div class="send-btn">Chat now</div>
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
.help{
  width: 48rem;
  height: 48.75rem;
  border-radius: 1.25rem;
  .top{
    width: 48rem;
    height: 10.625rem;
    background: #565656;
    border-radius: 1.25rem 1.25rem 0rem 0rem;

    .top-logo{
      width: 5.5rem;
      height: 2.375rem;
      padding: 1.8125rem 0 0 2.375rem;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .text{
      padding: 1.125rem 0 0 2rem;
      width: 41.5rem;
      height: 3.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 1.125rem;
      color: #FFFFFF;
      line-height: 1.5625rem;
      text-align: left;
      font-style: normal;
    }
  }
  
  .content{
    width: 100%;
    height: 609px;
    .dialogue{
      width: 100%;
      height: 31.875rem;
      padding: 2rem 2.1875rem;
      box-sizing: border-box;
      .customer{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .customer-img{
          width: 3rem;
          height: 3rem;
          background: #000;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .message{
          margin-left: .75rem;
        }
      }
      .client{
        margin-top: 2.5625rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .client-img{
          width: 3rem;
          height: 3rem;
          background: #000;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .message{
          margin-right: .75rem;
        }
      }
    }  
    .dialogue-input{
      width: 100%;
      height: 3.625rem;
      padding: 0 2.1875rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      input{
        width: 33.1875rem;
        height: 3.625rem;
        background: #FFFFFF;
        border-radius: .5rem;
        border: .0625rem solid #979797;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 1rem;
        color: #BABABA;
        text-align: left;
        font-style: normal;
        padding-left: 1.25rem;
        color: #000;
        box-sizing: border-box;
      }
      .send-btn{
        width: 9.3125rem;
        height: 3.625rem;
        line-height: 3.625rem;
        background: #3D3D3D;
        border-radius: .5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 1.125rem;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
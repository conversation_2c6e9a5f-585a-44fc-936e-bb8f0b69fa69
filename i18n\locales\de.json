{"hello": "Hall<PERSON>", "test": {"title": "i18n Testseite", "description": "Diese Seite wird verwendet, um die Internationalisierungsfunktionalität zu testen", "languageSwitcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basicTranslations": "Grundlegende Übersetzungen", "currentInfo": "Aktuelle Informationen", "urlInfo": "URL-Informationen", "currentLocale": "Aktuelle Sprache", "defaultLocale": "Standardsprache", "availableLocales": "Verfügbare <PERSON>chen", "currentUrl": "Aktuelle URL", "baseUrl": "Basis-URL", "footer": "Dies ist eine Testseite für i18n-Funktionalität", "pageTitle": "i18n Testseite", "hello": "Hall<PERSON>", "welcome": "<PERSON><PERSON><PERSON><PERSON>", "goodbye": "<PERSON><PERSON>"}, "retailCart": {"selectAll": "Alle auswählen", "delete": "Löschen", "itemsTotal": "Artikel {count} gesamt", "orderSummary": "Bestellübersicht", "subtotal": "Zwischensumme", "saved": "Gespart", "tax": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON>ers<PERSON>", "total": "Gesamt", "freeShipping": "Kostenloser Versand ab 79,00 US$", "promotionalCode": "Aktionscode", "enterCouponCode": "Gutscheincode eingeben", "selectMyCoupons": "Meine Gutscheine auswählen", "checkoutNow": "JETZT KAUFEN", "estimateShipping": "Versandkosten schätzen", "shippingWeight": "Versandgewicht: {weight} kg", "weAccept": "Wir akzeptieren", "pleaseEnterCouponCode": "Bitte geben Sie einen Gutscheincode ein", "couponAppliedSuccessfully": "Gutschein erfolgreich angewendet", "couponApplicationFailed": "Gutschein-Anwendung fehlgeschlagen", "cartIsEmpty": "Warenko<PERSON> ist leer, kann nicht zur Kasse", "pleaseLoginFirst": "Bitte zuerst anmelden", "removeConfirmText": "Sind <PERSON> sicher, dass Sie alle ausgewählten Artikel löschen möchten?", "removeConfirm": "Löschen bestätigen", "remove": "Entfernen"}, "wholesaleCart": {"selectAll": "Alle auswählen", "delete": "Löschen", "itemsTotal": "Artikel {count} gesamt", "orderSummary": "Bestellübersicht", "subtotal": "Zwischensumme", "saved": "Gespart", "total": "Gesamt", "freeShipping": "Kostenloser Versand ab 79,00 US$", "promotionalCode": "Aktionscode", "enterCouponCode": "Gutscheincode eingeben", "selectMyCoupons": "Meine Gutscheine auswählen", "checkoutNow": "JETZT KAUFEN", "estimateShipping": "Versandkosten schätzen", "shippingWeight": "Versandgewicht: {weight} kg", "weAccept": "Wir akzeptieren", "pleaseEnterCouponCode": "Bitte geben Sie einen Gutscheincode ein", "couponAppliedSuccessfully": "Gutschein erfolgreich angewendet", "couponApplicationFailed": "Gutschein-Anwendung fehlgeschlagen", "cartIsEmpty": "Warenko<PERSON> ist leer, kann nicht zur Kasse", "pleaseLoginFirst": "Bitte zuerst anmelden", "searchProducts": "Produkte suchen...", "noSearchResults": "<PERSON>ine Produkte gefunden", "searchTip": "Versuchen Sie andere Schlüsselwörter oder überprüfen Sie Ihre Schreibweise", "emptyCart": "Warenkorb ist leer", "goShopping": "Einkaufen gehen"}, "header": {"search": "<PERSON><PERSON>", "wishlist": "Wunschliste", "wholesale": "GROSSHANDEL", "cart": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "selectLanguage": "Auswählen"}, "personalCenter": {"secureCheckout": "SICHERER CHECKOUT", "user": "<PERSON><PERSON><PERSON>", "continueShopping": "WEITER EINKAUFEN"}, "home": {"findDevice": "GERÄT FINDEN", "selectBrand": "<PERSON>e auswählen", "selectDevice": "Gerät auswählen", "shopByBrand": "NACH MARKE EINKAUFEN", "inspirationText": "Betreten Sie unsere Inspirationszone und spüren Sie den Puls des schönen Lebens.", "help": "<PERSON><PERSON><PERSON>"}, "productCard": {"addToCart": "Zum Warenkorb hinzufügen", "inCart": "<PERSON><PERSON>", "selectColor": "Bitte Farbe auswählen", "wholesaleLogin": "Großhandel:", "login": "Accedi", "toView": "per visual<PERSON><PERSON>e", "removeFromWishlist": "Rimuovi dalla Lista dei Desideri", "addToWishlist": "Aggiungi alla Lista dei Desideri", "wishlistAdded": "Prodotto aggiunto alla lista dei desideri", "wishlistRemoved": "<PERSON><PERSON><PERSON> dalla lista dei desideri", "wishlistAddFailed": "Impossibile aggiungere alla lista dei desideri", "wishlistRemoveFailed": "Impossibile rimuovere dalla lista dei desideri", "wishlistError": "Errore durante l'operazione della lista dei desideri", "selectColorFirst": "Seleziona prima il colore del prodotto", "new": "NUOVO"}, "newArrival": {"filter": "Filter", "brand": "<PERSON><PERSON>", "device": "G<PERSON><PERSON>", "allBrands": "<PERSON>e Marken", "allDevices": "Alle Geräte", "loading": "Laden...", "loadingNewProducts": "Neue Produkte werden geladen...", "noNewProducts": "<PERSON><PERSON> neuen Produkte", "noNewProductsDesc": "Entschuldigung, derzeit sind keine neuen Produkte verfügbar", "addToCartSuccess": "Das Produkt wurde zum Warenkorb hinzugefügt.", "addToCartFailed": "Hinzufügen fehlgeschlagen", "addToCartError": "Hinzufügen zum Warenkorb fehlgeschlagen"}, "swiper": {"shopNow": "JETZT KAUFEN"}, "shoppingCartRetail": {"emptyCart": "Warenkorb ist leer", "goShopping": "Einkaufen gehen", "freeShipping": "KOSTENLOSER VERSAND", "color": "Farbe", "weight": "Gewicht", "subtotal": "Zwischensumme", "subtotalWeight": "Zwischensumme Gewicht", "delete": "Löschen", "brand": "<PERSON><PERSON>", "device": "G<PERSON><PERSON>", "price": "Pre<PERSON>", "quantity": "<PERSON><PERSON>", "minus": "Minus", "add": "Hinzufügen", "remove": "Entfernen", "updateQuantity": "Menge aktualisieren", "quantityError": "<PERSON><PERSON><PERSON><PERSON>", "removeConfirm": "Löschen bestätigen", "removeConfirmText": "Sind <PERSON> sicher, dass Sie diesen Artikel löschen möchten?", "removeSuccess": "Entfernen erfolgreich", "removeFailed": "Entfernen fehlgeschlagen", "updateSuccess": "Aktualisierung erfolgreich", "updateFailed": "Aktualisierung fehlgeschlagen", "brandEmpty": "<PERSON><PERSON> ist leer", "deviceEmpty": "Ger<PERSON> ist leer", "colorEmpty": "Farbe ist leer"}, "wishlist": {"loginConfirmTitle": "<PERSON><PERSON><PERSON><PERSON>", "loginConfirmMessage": "Sie sind nicht angemeldet. Möchten Sie zur Anmeldeseite gehen?", "confirm": "Bestätigen", "cancel": "Abbrechen", "pleaseLoginFirst": "Bitte zuerst anmelden", "addSuccess": "Erfolgreich zur Wunschliste hinzugefügt", "addFailed": "Hinzufügen zur Wunschliste fehlgeschlagen", "removeSuccess": "Erfolgreich entfernt", "removeFailed": "Entfernen fehlgeschlagen", "moveToCartSuccess": "Erfolgreich zum Warenkorb verschoben", "moveToCartFailed": "Verschieben zum Warenkorb fehlgeschlagen", "clearSuccess": "Wunschliste erfolgreich gel<PERSON>t", "clearFailed": "<PERSON>ren der Wunschliste fehlgeschlagen", "fetchDataFailed": "Abrufen der Wunschliste-Daten fehlgeschlagen"}, "wishlistPage": {"title": "<PERSON><PERSON>", "emptyTitle": "<PERSON><PERSON><PERSON> Wunschliste ist leer", "emptyDescription": "Noch keine Artikel zu Ihrer Wunschliste hinzugefügt", "goShopping": "Einkaufen gehen", "selectAll": "Alle auswählen", "addToCart": "Zum Warenkorb hinzufügen", "viewDetails": "Details anzeigen", "inStock": "<PERSON><PERSON>", "outOfStock": "<PERSON>cht auf Lager", "save": "<PERSON><PERSON>", "addSelectedToCart": "Ausgewählte zum Warenkorb hinzufügen", "removeSelected": "Ausgewählte entfernen", "removeConfirmTitle": "Entfernen bestätigen", "removeConfirmMessage": "Sind <PERSON><PERSON> sicher, dass Sie die ausgewählten {count} Artikel aus Ihrer Wunschliste entfernen möchten?", "removeSuccess": "<PERSON><PERSON><PERSON> <PERSON> entfernt", "removeFailed": "Entfernen fehlgeschlagen", "addToCartSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {count} Art<PERSON><PERSON> zum Warenkorb hinzugefügt", "addToCartFailed": "Hinzufügen zum Warenkorb fehlgeschlagen", "batchAddToCartError": "Fehler beim Batch-Hinzufügen zum Warenkorb aufgetreten", "batchRemoveError": "Fehler beim Batch-Entfernen aufgetreten", "removeSelectedSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {count} <PERSON><PERSON><PERSON> entfernt", "addToCartTitle": "Zum Warenkorb hinzufügen", "selectVariant": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "selectColor": "Farbe auswählen", "selectBrand": "<PERSON>e auswählen", "selectDevice": "Gerät auswählen", "quantity": "<PERSON><PERSON>", "cancel": "Abbrechen", "confirm": "Bestätigen", "delete": "Löschen"}, "product": {"addToCart": "Zum Warenkorb hinzufügen", "removeFromWishlist": "<PERSON> entfernen", "addToWishlist": "Zur Wunschliste hinzufügen", "addToCartSuccess": "Produkt zum Warenkorb hinzugefügt", "addToCartFailed": "Hinzufügen zum Warenkorb fehlgeschlagen", "addToCartError": "Fehler beim Hinzufügen zum Warenkorb aufgetreten", "invalidProduct": "Ungültige Produktdaten", "wishlistToggleError": "Fehler bei Wunschliste-Operation aufgetreten", "shoppingCart": "<PERSON><PERSON><PERSON>"}, "productDetails": {"loading": "Laden...", "productNotFound": "Produkt nicht gefunden oder Laden fehlgeschlagen", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkError": "Netzwerkverbindungsfehler, bitte überprüfen Sie Ihr Netzwerk und versuchen Sie es erneut", "description": "Beschreibung", "specification": "Spezifikation", "dimensions": "Abmessungen", "profile": "Profil", "dropRating": "Fallbewertung", "device": "G<PERSON><PERSON>", "selectBrand": "<PERSON>e auswählen", "selectDevice": "Gerät auswählen", "color": "Farbe", "quantity": "<PERSON><PERSON>", "addToCart": "Zum Warenkorb hinzufügen", "alreadyInCart": "Bereits im Warenkorb ({count})", "buyNow": "Jetzt kaufen", "checkoutSecurely": "SICHER MIT CHECKOUT", "acceptedPayments": "Akzeptierte Zahlungen", "interestFreePayments": "4 zinsfreie Zahlungen von 16,00 $ mit Klarna", "learnMore": "<PERSON><PERSON> er<PERSON>", "shippingUpTo": "Versand bis zu {percent}% RABATT", "pleaseSelectColor": "Bitte wählen Sie zuerst die Produktfarbe aus", "productInCart": "Produkt ist bereits im Warenkorb", "relatedProducts": "Das könnte Ihnen auch gefallen", "brands": "<PERSON><PERSON>", "colors": "<PERSON><PERSON>", "mediaPreview": {"close": "Schließen", "previous": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste"}}, "news": {"loadingTags": "Tags werden geladen", "loadTagsFailed": "Laden der Tags fehlgeschlagen, bitte aktualisieren Sie die Seite und versuchen Sie es erneut", "unknownError": "Unbekannter Fehler", "getTagsFailed": "Abrufen der Tag-Liste fehlgeschlagen", "getNewsFailed": "Abrufen der Nachrichten-Liste fehlgeschlagen", "detail": {"home": "Startseite", "news": "Nachrichten", "relatedArticles": "Verwandte Artikel", "youMayAlsoLike": "Das könnte Ihnen auch gefallen", "addToCartSuccess": "Das Produkt wurde zum Warenkorb hinzugefügt.", "addToCartFailed": "Hinzufügen fehlgeschlagen", "addToCartError": "Hinzufügen zum Warenkorb fehlgeschlagen"}}, "discover": {"showAll": "ALLE ANZEIGEN", "more": "mehr"}, "footer": {"newsletter": "NEWSLETTER", "stayUpdated": "Bleiben Sie über die neuen MLK+ Kollektionen auf dem Laufenden:", "emailPlaceholder": "E-Mail", "subscribe": "ABONNIEREN", "companyName": "MLK+ OUSAND S.R.L.", "companyAddress": "<PERSON><PERSON>, 47 - 00197 Roma, ITALIA", "taxId": "P. I. 13085581000", "copyright": "Copyright © 2025 MLK+. Alle Rechte vorbehalten."}, "subscribe": {"title": "Treten Sie der MLK+ Community bei", "description": "Newsletter abonnieren", "policy": "Wenn <PERSON> ein Liebhaber von Technologie und Design sind, melden Si<PERSON> sich für unseren Newsletter an und bleiben Sie über die Neuigkeiten auf dem Laufenden.", "emailPlaceholder": "Geben Sie Ihre E-Mail-Adresse ein", "signUp": "ANMELDEN", "emailValidation": "<PERSON>te geben Si<PERSON> eine korrekte E-Mail-Adresse ein"}, "search": {"home": "Startseite", "showHideFilters": "Filter anzeigen/verstecken", "byRating": "<PERSON><PERSON>", "hideAll": "ALLE VERSTECKEN", "showAll": "ALLE ANZEIGEN", "addToCartSuccess": "Das Produkt wurde zum Warenkorb hinzugefügt.", "addToCartFailed": "Hinzufügen fehlgeschlagen", "addToCartError": "Hinzufügen zum Warenkorb fehlgeschlagen"}}
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from '../user'
import { getWishList, wishlistAdd, wishlisDelete, wishlistClear } from '~/api/wishlist'
import { getShoppingCartAdd } from '~/api/shop'
import { navigateTo } from '#app'
import { ElMessageBox } from 'element-plus'

// 产品数据接口
interface ProductData {
  id: number
  url_key: string
  formatted_base_price: string
  formatted_price: string
  name: string
  base_image: string
  discount: {
    has_discount: boolean
    regular_price: number
    is_special_price_active: boolean
    special_price: number
    special_price_from: string
    special_price_to: string
    discount_percentage?: number
  }
  in_stock: boolean
  [key: string]: unknown // 支持任意其他产品字段
}

// 心愿单商品接口 - 简化结构
interface WishlistItem {
  id: number
  product: ProductData
}

export const useWishlistStore = defineStore('wishlist', () => {
  // 心愿单商品列表
  const wishlistItems = ref<WishlistItem[]>([])
  
  // 获取用户store
  const userStore = useUserStore()
  
  // 计算属性：心愿单商品总数
  const totalItems = computed(() => {
    return wishlistItems.value.length
  })
  
  // 检查用户是否已登录
  const isLoggedIn = computed(() => {
    return !!userStore.token
  })
  
  // 获取翻译文本的函数
  const getTranslation = (key: string) => {
    // 这里返回默认的英文文本，实际使用时会在组件中通过useI18n获取
    const translations: Record<string, string> = {
      'wishlist.loginConfirmMessage': 'You are not logged in. Would you like to go to the login page?',
      'wishlist.loginConfirmTitle': 'Notice',
      'wishlist.confirm': 'Confirm',
      'wishlist.cancel': 'Cancel',
      'wishlist.pleaseLoginFirst': 'Please login first',
      'wishlist.addSuccess': 'Successfully added to wishlist',
      'wishlist.addFailed': 'Failed to add to wishlist',
      'wishlist.removeSuccess': 'Successfully removed',
      'wishlist.removeFailed': 'Failed to remove',
      'wishlist.moveToCartSuccess': 'Successfully moved to cart',
      'wishlist.moveToCartFailed': 'Failed to move to cart',
      'wishlist.clearSuccess': 'Successfully cleared wishlist',
      'wishlist.clearFailed': 'Failed to clear wishlist',
      'wishlist.fetchDataFailed': 'Failed to fetch wishlist data'
    }
    return translations[key] || key
  }
  
  // 显示确认提示框
  const showLoginConfirm = async () => {
    try {
      await ElMessageBox.confirm(
        getTranslation('wishlist.loginConfirmMessage'),
        getTranslation('wishlist.loginConfirmTitle'),
        {
          confirmButtonText: getTranslation('wishlist.confirm'),
          cancelButtonText: getTranslation('wishlist.cancel'),
          type: 'warning',
        }
      )
      return true
    } catch {
      return false
    }
  }
  
  // 添加商品到心愿单
  const addToWishlist = async (product: ProductData) => {
    try {
      if (!isLoggedIn.value) {
        // 未登录状态：显示确认提示框
        const shouldNavigate = await showLoginConfirm()
        if (shouldNavigate) {
          await navigateTo('/login')
        }
        return { success: false, message: getTranslation('wishlist.pleaseLoginFirst') }
      }
      
      // 已登录状态：调用接口
      const response = await wishlistAdd({
        product_id: product.id
      })
      
      if (response.success) {
        await fetchWishlistData()
        return { success: true, message: getTranslation('wishlist.addSuccess') }
      } else {
        return { success: false, message: response.message || getTranslation('wishlist.addFailed') }
      }
    } catch (error) {
      console.error(getTranslation('wishlist.addFailed'), error)
      return { success: false, message: getTranslation('wishlist.addFailed') }
    }
  }
  
  // 从心愿单移除商品
  const removeFromWishlist = async (itemId: number) => {
    try {
      if (!isLoggedIn.value) {
        return { success: false, message: getTranslation('wishlist.pleaseLoginFirst') }
      }
      
      // 已登录状态：调用接口
      const response = await wishlisDelete({ id: itemId })
      if (response.success) {
        await fetchWishlistData()
        return { success: true, message: getTranslation('wishlist.removeSuccess') }
      } else {
        return { success: false, message: response.message || getTranslation('wishlist.removeFailed') }
      }
    } catch (error) {
      console.error('移除心愿单商品失败:', error)
      return { success: false, message: getTranslation('wishlist.removeFailed') }
    }
  }
  
  // 将心愿单商品移动到购物车
  const moveToShoppingCart = async (item: WishlistItem) => {
    try {
      if (!isLoggedIn.value) {
        return { success: false, message: getTranslation('wishlist.pleaseLoginFirst') }
      }
      
      // 已登录状态：调用接口
      const response = await getShoppingCartAdd({
        product_id: item.product.id,
        quantity: 1,
        selected_configurable_option: 1,
        super_attribute: {
        }
      })
      
      if (response.success) {
        await fetchWishlistData()
        return { success: true, message: getTranslation('wishlist.moveToCartSuccess') }
      } else {
        return { success: false, message: response.message || getTranslation('wishlist.moveToCartFailed') }
      }
    } catch (error) {
      console.error(getTranslation('wishlist.moveToCartFailed'), error)
      return { success: false, message: getTranslation('wishlist.moveToCartFailed') }
    }
  }
  
  // 获取心愿单数据
  const fetchWishlistData = async () => {
    try {
      if (!isLoggedIn.value) {
        // 未登录状态：清空数据
        wishlistItems.value = []
        return
      }
      
      // 已登录状态：从接口获取
      const response = await getWishList()
      if (response.data && response.success) {
        // 转换接口返回的数据格式为本地格式
        wishlistItems.value = response.data.wishlist.map((item: { id: string; product: ProductData }) => ({
          id: item.id,
          product: item.product
        })) 
      }
    } catch (error) {
      console.error('获取心愿单数据失败:', error)
      console.error(getTranslation('wishlist.fetchDataFailed'), error)
    }
  }
  
  // 清空心愿单
  const clearWishlist = async () => {
    try {
      if (!isLoggedIn.value) {
        return { success: false, message: getTranslation('wishlist.pleaseLoginFirst') }
      }
      
      // 已登录状态：调用接口
      const response = await wishlistClear()
      if (response.success) {
        await fetchWishlistData()
        return { success: true, message: getTranslation('wishlist.clearSuccess') }
      } else {
        return { success: false, message: response.message || getTranslation('wishlist.clearFailed') }
      }
    } catch (error) {
      console.error(getTranslation('wishlist.clearFailed'), error)
      return { success: false, message: getTranslation('wishlist.clearFailed') }
    }
  }
  
  // 检查商品是否在心愿单中
  const isInWishlist = (productId: number) => {
    if (!isLoggedIn.value) {
      return false
    }
    
    if (wishlistItems.value.length > 0) {
      return wishlistItems.value.some(item => 
        item.product.id == productId
      )
    }
    return false
  }
  
  // 获取心愿单中指定商品
  const getWishlistItem = (productId: number) => {
    if (!isLoggedIn.value) {
      return null
    }
    
    return wishlistItems.value.find(item => 
      item.product.id == productId
    )
  }
  
  // 获取心愿单中指定商品的完整产品数据
  const getWishlistItemProduct = (productId: number) => {
    const item = getWishlistItem(productId)
    return item?.product || null
  }
  
  // 初始化：获取心愿单数据
  const initWishlist = async () => {
    await fetchWishlistData()
  }
  
  return {
    wishlistItems,
    totalItems,
    isLoggedIn,
    addToWishlist,
    removeFromWishlist,
    moveToShoppingCart,
    fetchWishlistData,
    clearWishlist,
    isInWishlist,
    getWishlistItem,
    getWishlistItemProduct,
    initWishlist
  }
})

<template>
  <div class="swiper-wrapper">
    <!-- 使用 ClientOnly 确保只在客户端渲染 -->
    <ClientOnly>
      <!-- 使用 v-if 确保数据准备好后再渲染 -->
      <swiper
        v-if="dataList && dataList.length > 0"
        :key="`swiper-${swiperKey}`"
        class="swiper-container"
        :slides-per-view="1"
        :space-between="0"
        :modules="modules"
        :autoplay="{
          delay: 5000,
          disableOnInteraction: false,
        }"
        :loop="true"
        @swiper="onSwiper"
        @slide-change="onSlideChange"
        @mouseenter="pauseSwiper"
        @mouseleave="resumeSwiper"
      >
        <swiper-slide v-for="item in dataList" :key="item.image">
          <div class="item">
            <img :src="item.image" alt="slide">
            <div class="mask">
              <transition name="el-fade-in-linear">
                <div v-show="isShowNavigation" class="left" @click="onclickLeft">
                  <el-icon size="60"><ArrowLeft /></el-icon>
                </div>
              </transition>
              
              <div class="mask-title">
                {{ item.title }}
              </div>
              <div v-if="item.intro" class="mask-content">
                {{ item.intro }}
              </div>
              <transition name="el-fade-in-linear">
                <div v-show="isShowNavigation" class="right" @click="onclickRight">
                  <el-icon size="60"><ArrowRight /></el-icon>
                </div>
              </transition>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup name="SwiperMuse">
import { ref, onMounted, nextTick, watch, computed } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination, A11y, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/autoplay";

interface RootObject {
  title: string;
  image?: string;
  link?: string;
  intro?: string;
}

interface Props {
  dataList: RootObject[];
}

const props = defineProps<Props>();

// 响应式数据
const isShowNavigation = ref(false);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperInstance = ref<any>(null);
const swiperKey = ref(0);
const isInitialized = ref(false);

// Swiper 模块
const modules = [Navigation, Pagination, A11y, Autoplay];

// 计算属性：确保数据有效
const validDataList = computed(() => {
  return props.dataList && Array.isArray(props.dataList) && props.dataList.length > 0;
});

// Swiper 初始化回调
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onSwiper = (swiper: any) => {
  // console.warn('🎉 Swiper 实例已成功初始化:', swiper);
  swiperInstance.value = swiper;
  isInitialized.value = true;
  
  // 确保 autoplay 正常工作
  if (swiper.autoplay) {
    swiper.autoplay.start();
  }
};

// 幻灯片切换回调
const onSlideChange = () => {
  // console.warn('幻灯片已切换');
};

// 手动初始化方法
const manualInitSwiper = () => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // console.warn('🔄 开始手动初始化 Swiper');
  
  if (!validDataList.value) {
    // console.warn('❌ 数据无效，无法初始化');
    return;
  }
  
  // 销毁现有实例
  if (swiperInstance.value) {
    try {
      swiperInstance.value.destroy(true, true);
      // console.warn('✅ 现有实例已销毁');
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // console.warn('⚠️ 销毁实例时出错:', error);
    }
    swiperInstance.value = null;
    isInitialized.value = false;
  }
  
  // 强制重新渲染
  swiperKey.value++;
  // console.warn('🔄 组件已重新渲染，key:', swiperKey.value);
  
  // 延迟检查初始化状态
  nextTick(() => {
    setTimeout(() => {
      if (!isInitialized.value) {
        // console.warn('⚠️ 初始化超时，再次尝试');
        swiperKey.value++;
      }
    }, 500);
  });
};

// 强制重新渲染
const forceRerender = () => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // console.warn('🔄 强制重新渲染');
  swiperKey.value++;
};

// 获取实例状态
const getSwiperInstance = () => {
  return {
    instance: swiperInstance.value,
    isInitialized: isInitialized.value,
    hasData: validDataList.value
  };
};

// 自定义导航按钮
const onclickLeft = () => {
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.slidePrev();
  }
};

const onclickRight = () => {
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.slideNext();
  }
};

// 暂停/恢复自动播放
const pauseSwiper = () => {
  isShowNavigation.value = true;
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.autoplay?.stop();
  }
};

const resumeSwiper = () => {
  isShowNavigation.value = false;
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.autoplay?.start();
  }
};

// 监听数据变化
watch(() => props.dataList, () => {
  //    console.warn('📊 数据已更新:', newDataList?.length);
  
  if (validDataList.value && !isInitialized.value) {
    // console.warn('🔄 数据更新，触发初始化');
    nextTick(() => {
      manualInitSwiper();
    });
  }
}, { immediate: true, deep: true });

// 组件挂载
onMounted(() => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // console.warn('🚀 SwiperMuse 组件已挂载');
  // console.warn('📊 数据状态:', {
  //   hasData: validDataList.value,
  //   dataLength: props.dataList?.length
  // });
  
  // 确保 DOM 完全渲染
  nextTick(() => {
    if (validDataList.value && !isInitialized.value) {
      // console.warn('🔄 组件挂载后触发初始化');
      setTimeout(() => {
        manualInitSwiper();
      }, 100);
    }
  });
});

// 暴露方法给父组件
defineExpose({
  manualInitSwiper,
  forceRerender,
  getSwiperInstance
});

</script>

<style scoped lang="scss">
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}



.swiper-container {
  background-color: white;
  width: 100%;
  height: 100%;
}

.swiper {
  --swiper-pagination-color: #fff;
  --swiper-theme-color: #fff;
  width: 100%;
  height: 100%;
}

::v-deep .swiper-button-prev {
  position: absolute;
  left: 7.6875rem;
}

::v-deep .swiper-button-next {
  position: absolute;
  right: 7.6875rem;
}

::v-deep .swiper-pagination-bullet {
  background: var(--swiper-pagination-bullet-inactive-color, #666);
  border: 1px solid #fff;
}

::v-deep .swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}

.item {
  width: 100%;
  height: 43.75rem;
  position: relative;
  
  img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }
  
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    
    .left {
      position: absolute;
      top: 16.4375rem;
      left: 7.6875rem;
      width: 3.75rem;
      height: 3.75rem;
      z-index: 15;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .right {
      position: absolute;
      top: 16.4375rem;
      right: 7.6875rem;
      width: 3.75rem;
      height: 3.75rem;
      z-index: 15;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .mask-title {
      width: 33.4375rem;
      text-align: left;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 2.6875rem;
      color: #FFFFFF;
      padding: .625rem 0;
      font-style: normal;
    }
    
    .mask-content {
      width: 33.4375rem;
      height: 6.1875rem;
      line-height: 2.0625rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 1.5rem;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>

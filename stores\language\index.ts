import { useI18n } from 'vue-i18n'

export const useLanguageStore = defineStore("language", () => {
    const language = ref("en");
    
    // 在store顶层获取i18n实例
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let i18nInstance: any = null
    
    // 初始化i18n实例的函数
    const initI18n = () => {
      if (import.meta.client && !i18nInstance) {
        try {
          i18nInstance = useI18n()
        } catch (error) {
          console.error('🌐 初始化i18n失败:', error)
        }
      }
    }
    
    function setLanguage(val: string) {
      language.value = val;
      
      // 同时更新i18n语言
      if (import.meta.client) {
        try {
          // 确保i18n实例已初始化
          initI18n()
          
          if (i18nInstance) {
            const { locale } = i18nInstance
            // 将后端语言代码映射到前端语言代码
            const languageMapping: Record<string, 'en' | 'cn' | 'it' | 'de' | 'fr' | 'gr'> = {
              'en': 'en',
              'zh': 'cn', // 中文
              'it': 'it', // 意大利语
              'cn': 'cn', // 兼容性处理
              'de': 'de', // 德语
              'fr': 'fr', // 法语
              'gr': 'gr' // 希腊语
            }
            
            const mappedLanguage = languageMapping[val] || 'it'
            
            // 确保语言确实发生了变化才更新
            if (locale.value !== mappedLanguage) {
              locale.value = mappedLanguage
            }
          }
        } catch (error) {
          console.error('🌐 语言切换失败:', error)
        }
      }
    }

    return {
      language,
      setLanguage,
      initI18n
    }
  }, 
   {
    persist: import.meta.client && {
      storage: localStorage,
    },
  }
)
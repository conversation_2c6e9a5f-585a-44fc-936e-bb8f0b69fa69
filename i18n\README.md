# Nuxt i18n 使用指南

## 概述

本项目使用 `@nuxtjs/i18n` 模块实现国际化功能，支持英语、中文和意大利语。

## 配置

### 1. 语言配置

在 `nuxt.config.ts` 中配置了三种语言：

```typescript
i18n: {
  defaultLocale: 'en',
  locales: [
    { code: 'en', name: 'English', file: 'en.json' },
    { code: 'cn', name: '中文', file: 'cn.json' },
    { code: 'it', name: 'Italiano', file: 'it.json' }
  ],
  langDir: 'i18n/locales',
  strategy: 'prefix_except_default',
  detectBrowserLanguage: {
    useCookie: true,
    cookieKey: 'i18n_redirected',
    redirectOn: 'root',
  },
}
```

### 2. 语言文件结构

```
i18n/
├── i18n.config.ts          # i18n 配置文件
├── locales/
│   ├── en.json            # 英语翻译
│   ├── cn.json            # 中文翻译
│   └── it.json            # 意大利语翻译
└── README.md              # 本文件
```

## 使用方法

### 1. 在组件中使用翻译

```vue
<template>
  <div>
    <h1>{{ $t("hello") }}</h1>
    <p>{{ $t("welcome") }}</p>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 在 JavaScript 中使用翻译
const message = t("hello");
</script>
```

### 2. 语言切换

```vue
<template>
  <div>
    <button @click="switchLanguage('en')">English</button>
    <button @click="switchLanguage('cn')">中文</button>
    <button @click="switchLanguage('it')">Italiano</button>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()

const switchLanguage = (lang: 'en' | 'cn' | 'it') => {
  locale.value = lang
}
</script>
```

### 3. 使用语言切换组件

```vue
<template>
  <div>
    <LanguageSwitcher />
  </div>
</template>
```

## 测试页面

访问 `/test-i18n` 页面可以测试语言切换功能：

- 查看当前语言状态
- 测试基本翻译功能
- 切换不同语言
- 查看 URL 变化

## 添加新翻译

### 1. 在语言文件中添加翻译

```json
// en.json
{
  "hello": "Hello",
  "welcome": "Welcome",
  "newKey": "New Translation"
}
```

### 2. 在其他语言文件中添加对应翻译

```json
// cn.json
{
  "hello": "你好",
  "welcome": "欢迎",
  "newKey": "新翻译"
}
```

## 最佳实践

1. **使用嵌套结构**：将相关翻译组织在对象中

   ```json
   {
     "common": {
       "hello": "Hello",
       "welcome": "Welcome"
     },
     "errors": {
       "notFound": "Not Found"
     }
   }
   ```

2. **使用有意义的键名**：避免使用数字或随机字符串作为键名

3. **保持翻译文件同步**：确保所有语言文件都有相同的键

4. **测试翻译**：定期测试所有语言的翻译是否正确显示

## 常见问题

### 1. 翻译不显示

- 检查键名是否正确
- 确认语言文件路径正确
- 验证语言文件格式是否为有效 JSON

### 2. 语言切换不生效

- 检查 `locale.value` 是否正确设置
- 确认语言代码是否在配置的语言列表中

### 3. URL 前缀问题

- 默认语言不会显示前缀
- 其他语言会显示语言代码前缀（如 `/cn/`）

## 文件说明

- `test-i18n.vue`: 测试页面，用于验证 i18n 功能
- `LanguageSwitcher.vue`: 可复用的语言切换组件
- `en.json`, `cn.json`, `it.json`: 各语言的翻译文件

{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@joyday/vue-loop-scroll": "^1.1.4", "@nuxt/content": "^3.5.1", "@nuxt/eslint": "^1.4.1", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.8", "@nuxt/ui": "^3.1.3", "@nuxtjs/i18n": "^10.0.3", "@pinia/nuxt": "^0.11.1", "@unhead/vue": "^2.0.10", "better-sqlite3": "^12.2.0", "ipx": "^3.0.3", "nuxt": "^3.17.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "swiper": "^11.2.8", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@element-plus/nuxt": "^1.1.3", "element-plus": "^2.10.2", "sass-embedded": "^1.89.2"}}
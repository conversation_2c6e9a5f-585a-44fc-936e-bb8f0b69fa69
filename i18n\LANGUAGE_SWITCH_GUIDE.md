# 全局语言切换功能使用指南

## 概述

本项目实现了基于 LanguageSelector 组件的全局 i18n 语言切换功能，支持英文、中文和意大利语的动态切换。

## 核心组件

### 1. 语言 Store (`stores/language/index.ts`)

负责管理全局语言状态，并提供语言切换功能。

```typescript
import { useLanguageStore } from "~/stores/language";

const languageStore = useLanguageStore();

// 切换语言
languageStore.setLanguage("zh"); // 切换到中文
```

### 2. 语言切换 Composable (`composables/useLanguageSwitch.ts`)

提供更高级的语言切换功能，包括语言映射、验证等。

```typescript
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

const {
  switchLanguage,
  getCurrentLanguage,
  getCurrentI18nLanguage,
  isLanguageSupported,
  getSupportedLanguages,
  initializeLanguage,
} = useLanguageSwitch();

// 切换语言
switchLanguage("zh");

// 获取当前语言
const currentLang = getCurrentLanguage();

// 检查语言是否支持
const isSupported = isLanguageSupported("zh");
```

### 3. LanguageSelector 组件 (`components/MLK/LanguageSelector.vue`)

用户界面组件，提供语言选择下拉菜单。

```vue
<template>
  <LanguageSelector :languagelist="languageList" />
</template>
```

## 语言代码映射

| 后端语言代码 | 前端 i18n 代码 | 说明             |
| ------------ | -------------- | ---------------- |
| `en`         | `en`           | 英文             |
| `zh`         | `cn`           | 中文             |
| `it`         | `it`           | 意大利语         |
| `zh_CN`      | `cn`           | 中文（兼容）     |
| `zh-CN`      | `cn`           | 中文（兼容）     |
| `it_IT`      | `it`           | 意大利语（兼容） |
| `it-IT`      | `it`           | 意大利语（兼容） |

## 翻译键说明

### 零售购物车 (retailCart)

- `retailCart.selectAll` - 全选按钮
- `retailCart.delete` - 删除按钮
- `retailCart.itemsTotal` - 商品数量显示
- `retailCart.orderSummary` - 订单摘要
- `retailCart.subtotal` - 小计
- `retailCart.saved` - 已节省
- `retailCart.tax` - 税费
- `retailCart.shipping` - 运费
- `retailCart.total` - 总计
- `retailCart.freeShipping` - 免运费提示
- `retailCart.promotionalCode` - 优惠券代码
- `retailCart.enterCouponCode` - 输入优惠券代码占位符
- `retailCart.selectMyCoupons` - 选择我的优惠券按钮
- `retailCart.checkoutNow` - 立即结账按钮
- `retailCart.estimateShipping` - 预估运费
- `retailCart.shippingWeight` - 商品重量
- `retailCart.weAccept` - 我们接受
- `retailCart.pleaseEnterCouponCode` - 请输入优惠券代码提示
- `retailCart.couponAppliedSuccessfully` - 优惠券应用成功提示
- `retailCart.couponApplicationFailed` - 优惠券应用失败提示
- `retailCart.cartIsEmpty` - 购物车为空提示
- `retailCart.pleaseLoginFirst` - 请先登录提示

### 批发购物车 (wholesaleCart)

- `wholesaleCart.selectAll` - 全选按钮
- `wholesaleCart.delete` - 删除按钮
- `wholesaleCart.itemsTotal` - 商品数量显示
- `wholesaleCart.orderSummary` - 订单摘要
- `wholesaleCart.subtotal` - 小计
- `wholesaleCart.saved` - 已节省
- `wholesaleCart.total` - 总计
- `wholesaleCart.freeShipping` - 免运费提示
- `wholesaleCart.promotionalCode` - 优惠券代码
- `wholesaleCart.enterCouponCode` - 输入优惠券代码占位符
- `wholesaleCart.selectMyCoupons` - 选择我的优惠券按钮
- `wholesaleCart.checkoutNow` - 立即结账按钮
- `wholesaleCart.estimateShipping` - 预估运费
- `wholesaleCart.shippingWeight` - 商品重量
- `wholesaleCart.weAccept` - 我们接受
- `wholesaleCart.pleaseEnterCouponCode` - 请输入优惠券代码提示
- `wholesaleCart.couponAppliedSuccessfully` - 优惠券应用成功提示
- `wholesaleCart.couponApplicationFailed` - 优惠券应用失败提示
- `wholesaleCart.cartIsEmpty` - 购物车为空提示
- `wholesaleCart.pleaseLoginFirst` - 请先登录提示

## 使用方法

### 1. 在组件中使用

```vue
<template>
  <div>
    <h1>{{ $t("retailCart.orderSummary") }}</h1>
    <h1>{{ $t("wholesaleCart.orderSummary") }}</h1>
    <button @click="switchToChinese">切换到中文</button>
  </div>
</template>

<script setup lang="ts">
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";

const { switchLanguage } = useLanguageSwitch();

const switchToChinese = () => {
  switchLanguage("zh");
};
</script>
```

### 2. 在页面中使用

```vue
<template>
  <div>
    <LanguageSelector :languagelist="languageList" />
    <p>{{ $t("test.hello") }}</p>
    <p>{{ $t("retailCart.selectAll") }}</p>
    <p>{{ $t("wholesaleCart.selectAll") }}</p>
  </div>
</template>

<script setup lang="ts">
// 页面会自动响应 LanguageSelector 的语言切换
</script>
```

### 3. 在 Store 中使用

```typescript
import { useLanguageStore } from "~/stores/language";

const languageStore = useLanguageStore();

// 直接切换语言
languageStore.setLanguage("it");
```

## 功能特性

### ✅ 自动语言同步

- LanguageSelector 组件会自动同步 i18n 语言
- 页面刷新后会保持用户选择的语言

### ✅ 语言代码映射

- 支持多种语言代码格式的兼容性处理
- 自动将后端语言代码映射到前端 i18n 代码

### ✅ 持久化存储

- 用户选择的语言会保存在 localStorage 中
- 页面刷新后自动恢复用户的语言选择

### ✅ 调试支持

- 提供详细的控制台日志
- 支持语言切换状态的实时监控

## 演示页面

访问 `/language-switch-demo` 页面可以查看完整的功能演示，包括：

- 实时语言切换
- 当前语言状态显示
- 支持的语言列表
- 零售购物车翻译演示
- 调试信息

## 注意事项

1. **客户端限制**: 语言切换功能仅在客户端环境下工作
2. **语言代码**: 确保使用正确的语言代码格式
3. **翻译文件**: 确保所有语言代码都有对应的翻译文件
4. **组件初始化**: 在组件挂载后会自动初始化语言设置

## 故障排除

### 语言切换不生效

1. 检查控制台是否有错误信息
2. 确认语言代码是否正确
3. 验证翻译文件是否存在

### 翻译显示不正确

1. 检查翻译键是否正确
2. 确认翻译文件中是否包含对应的翻译
3. 验证 i18n 配置是否正确

### 页面刷新后语言重置

1. 检查 localStorage 是否正常工作
2. 确认 store 的持久化配置是否正确
3. 验证初始化逻辑是否在正确的时机执行

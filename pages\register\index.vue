<template>
    <NuxtLayout>
        <div class="register-container">
            <div class="register-box">
                <div class="register-title">
                    <span>Create an Account with <PERSON>LK+</span>
                </div>
                <div class="register-content">
                    <el-form ref="ruleFormRef" :model="form" label-width="auto" :rules="rules" style="display: flex;">
                        <div class="register-content-left">
                            <div class="register-name">
                                <span>Name</span>
                                <div class="register-name-box">
                                    <el-form-item prop="first_name">
                                        <el-input 
                                            v-model="(form as any).first_name"
                                            style="width: 10.625rem;height: 3.5rem;" placeholder="First Name" />
                                    </el-form-item>
                                    <el-form-item prop="first_name">
                                        <el-input 
                                            v-model="(form as any).last_name"
                                            style="width: 10.625rem;height: 3.5rem;" placeholder="Last Name" />
                                    </el-form-item>
                                </div>
                            </div>
                            <div class="register-social">
                                <span>Social Title</span>
                                <div style="margin-top: 1.25rem;margin-left: 1.125rem;">
                                    <el-form-item prop="social_title">
                                        <el-radio-group v-model="(form as any).social_title">
                                            <el-radio value="1" size="large">Mr.</el-radio>
                                            <el-radio value="2" size="large">Mrs.</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </div>
                            </div>
                            <div class="register-email">
                                <span>Email</span>
                                <div style="margin-top: .75rem;">
                                    <el-form-item prop="email">
                                        <el-input v-model="(form as any).email" style="height: 3.5rem;" />
                                    </el-form-item>
                                </div>
                            </div>
                            <div class="register-password">
                                <span>Password</span>
                                <div style="margin-top: .75rem;">
                                    <el-form-item prop="password">
                                        <el-input 
                                        v-model="(form as any).password" 
                                        type="password"
                                            style="height: 3.5rem;" 
                                            show-password 
                                            placeholder="password"
                                        />
                                    </el-form-item>
                                </div>
                            </div>
                            <div class="register-Birthday">
                                <span>Birthday</span>
                                <div style="margin-top: .75rem;">
                                    <el-form-item prop="date_of_birth">
                                        <el-date-picker 
                                            v-model="(form as any).date_of_birth" type="date"
                                            placeholder="MM/DD/YYYY" style="width: 100%;height: 3.5rem;" size="large" />
                                    </el-form-item>
                                </div>
                            </div>
                        </div>
                        <div class="register-content-center">
                            <div style="width: 100%;display: flex;justify-content: space-between;">
                                <div class="register-Country">
                                    <span>Country</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="country">
                                            <el-input v-model="(form as any).country" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                                <div class="register-City">
                                    <span>City</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="city">
                                            <el-input v-model="(form as any).city" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 100%;display: flex;justify-content: space-between;margin-top: .625rem;">
                                <div class="register-Address">
                                    <span>Address</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="address">
                                            <el-input v-model="(form as any).address" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                                <div class="register-State">
                                    <span>State</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="state">
                                            <el-input v-model="(form as any).state" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 100%;display: flex;justify-content: space-between;margin-top: 1.875rem">
                                <div class="register-zip">
                                    <span>Zip/Postal Code</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="zip_postal_code">
                                            <el-input v-model="(form as any).zip_postal_code" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                                  <div class="register-Phone">
                                    <span>Phone</span>
                                    <div style="margin-top: .75rem;">
                                        <el-form-item prop="phone">
                                            <el-input v-model="(form as any).phone" style=" height: 3.5rem;" />
                                        </el-form-item>
                                    </div>
                                </div>
                            </div>
                            <div class="register-button">
                                <div style="margin-left: 2rem;">
                                    <el-checkbox 
                                        v-model="isAgree"
                                        label="I have read and accept the privacy policy  and I give my consent to receive communications"
                                        size="large" />
                                    <el-checkbox 
                                        v-model="isAgree2"
                                        size="large">
                                        <template #default>
                                            <div class="register-button-checkbox-title">Sign up for our newsletter</div>
                                            <div class="register-button-checkbox">
                                                You may unsubscribe at any moment,For that purpose,please find our contact info in the legal notice
                                            </div>
                                        </template>
                                    </el-checkbox>    
                                </div>
                                <div class="register-button-submit">
                                    <div class="register-button-style" @click="handleSubmit(ruleFormRef)">
                                        Sign Up
                                    </div>
                                    <div class="register-tip">
                                        <span>Already have an account? <a href="/login">Login instead！</a></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="register-content-right">
                            <div class="register-understand">
                                <div class="register-checkbox-two">
                                    <el-checkbox 
                                        v-model="isAgree3"
                                        size="large" >
                                        <template #default>
                                            <div class="register-checkbox-two-title">
                                                Enim quis fugiat consequat elit minim nisi 
                                                occaecat occaecat deserunt aliquip nisi ex 
                                                deserunt
                                            </div>
                                        </template>
                                    </el-checkbox>
                                </div>
                                 <div class="register-checkbox-three">
                                    <el-checkbox 
                                        v-model="isAgree4"
                                        size="large">
                                        <template #default>
                                            <div class="register-checkbox-three-title">Customer data privacy</div>
                                            <div class="register-checkbox-three-content">
                                                The personal data vou orovide to us is used tol answer your questions, process orders or provide access to soecinc intormation. You nave the nant to change and delele any personal miormation located on your "My Account" page.
                                            </div>
                                        </template>
                                    </el-checkbox> 
                                 </div>  
                            </div>
                        </div>
                    </el-form>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user'
import type { FormRules,FormInstance } from 'element-plus'
definePageMeta({
    layout: "personal-center"
})
interface formType {
    email: string,
    password: string,
    first_name: string,
    last_name: string,
    social_title: string,
    invoice_type: string,
    date_of_birth: string,
    country: string,
    address: string,
    zip_postal_code: string,
    city: string,
    state: string,
    phone: string,
    user_type: string
}

const form = ref<formType>({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    social_title: '',
    invoice_type: '',
    date_of_birth: '',
    country: '',
    address: '',
    zip_postal_code: '',
    city: '',
    state: '',
    phone: '',
    user_type: 'general' //零售标识
})

const rules = ref<FormRules<formType>>({
    email: [
        { required: true, message: 'Please input email', trigger: 'blur' },
        { type: 'email', message: 'Please enter the correct email address', trigger: 'blur' }
    ],
    password: [
        { required: true, message: 'Please input password', trigger: 'blur' },
        { min: 6, max: 16, message: 'password length must be 6-16 characters', trigger: 'blur' }
    ],
    first_name: [
        { required: true, message: 'Please input first_name', trigger: 'blur' },
    ],
    last_name: [
        { required: true, message: 'Please input last_name', trigger: 'blur' },
    ],
    social_title: [
        { required: true, message: 'Please selected social_title', trigger: 'blur' },
    ],
    phone: [
        { required: true, message: 'Please input phone', trigger: 'blur' },
        // { pattern: /^1[3-9]\d{9}$/, message: 'Please enter the correct phone number', trigger: 'blur' }
    ],
    country: [
        { required: true, message: 'Please input country', trigger: 'blur' },
    ],
    address: [
        { required: true, message: 'Please input address', trigger: 'blur' },
    ],
    zip_postal_code: [
        { required: true, message: 'Please input zip_postal_code', trigger: 'blur' },
    ],
    city: [
        { required: true, message: 'Please input city', trigger: 'blur' },
    ],
    state: [
        { required: true, message: 'Please input state', trigger: 'blur' },
    ],
})
const isAgree = ref<boolean>(false)
const isAgree2 = ref<boolean>(false)
const isAgree3 = ref<boolean>(false)
const isAgree4 = ref<boolean>(false)
const ruleFormRef = ref<FormInstance>()
const userStore = useUserStore()
const router = useRouter()

const handleSubmit = async (formEl: FormInstance | undefined) => { 
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            // console.log('submit')
            const res = await userStore.userRegister(form.value)
            console.warn('注册响应', res);
            if (res.success) {
                ElMessage.success('注册成功')
                router.push('/login')
            } else {
                ElMessage.error(res.message)
            }
            
        } else {
            console.warn('error submit!', fields)
        }
    })
}
</script>

<style scoped lang="scss">
// 输入框内容样式
::v-deep .el-input__wrapper {
    background-color: #F6F6F6;
    border-radius: .5rem;
    border: .0625rem solid #F1F1F1;
    box-shadow: 0 .25rem .5rem 0 rgba(0,0,0,0.1);
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label{
    color:#262626 ;
}
 ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #262626;
    border-color: #262626;
}
::v-deep .el-radio__input.is-checked+.el-radio__label {
    color: #262626;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
    background-color: #262626;
    border-color: #262626;
}
.register-container {
    width: 100%;
    height: 89.375rem;
    background-color: #F1F1F1;
    display: grid;
    place-items: center;

    .register-box {
        width: 113.625rem;
        height: 79.625rem;
        background: #FFFFFF;
        box-shadow: 0px .25rem .5rem 0px rgba(0, 0, 0, 0.1);
        border-radius: .5rem;

        .register-title {
            width: 58.375rem;
            height: 3.5rem;
            margin: 2.0625rem 50.4375rem 0 4.8125rem;
            font-family: Arial, Arial;
            font-weight: 900;
            font-size: 2.5rem;
            color: #262626;
            line-height: 3.5rem;
            text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
            text-align: left;
            font-style: normal;
        }

        .register-content {
            width: 100%;
            height: 72.75rem;

            // background-color: chartreuse;
            .register-content-left {
                margin: 3.0625rem 0 0 4.8125rem;
                width: 23.75rem;
                height: 31.25rem;

                // background-color: red;
                .register-name {
                    width: 100%;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }

                    .register-name-box {
                        // width: 10.75rem;
                        margin-top: .75rem;
                        display: flex;
                        justify-content: space-between;
                        height: 3.5rem;

                        ::v-deep .el-input__wrapper {
                            background-color: #FFFFFF;
                        }
                    }
                }

                .register-social {
                    margin-top: 1.75rem;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }
                }

                .register-email {
                    margin-top: 3.5625rem;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }
                }

                .register-password {
                    margin-top: 1.75rem;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }
                }

                .register-Birthday {
                    margin-top: 1.75rem;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }

                    ::v-deep .el-input__wrapper {
                        background-color: #FFFFFF;
                    }
                }
            }

            .register-content-center {
                margin: 3.0625rem 0 0 4.0625rem;
                width: 50.9375rem;
                // height: 31.25rem;

                div {
                    width: 23.75rem;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: .875rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                    }
                }

                // background-color: red;
                .register-button {
                    margin-top: 18.75rem;
                    width: 100%;
                    height: 12.5rem;

                    // background-color: #262626;
                    :v-deep .el-checkbox.el-checkbox--large .el-checkbox__label {
                        font-family: Helvetica;
                        font-size: 1rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                    }

                    ::v-deep .el-checkbox.el-checkbox--large .el-checkbox__inner {
                        // width: .9375rem;
                        height: .9375rem;
                        // border: 1px solid black;
                        border-radius: .1875rem;
                    }
                    .register-button-submit {
                        margin: 4.0625rem auto 0 auto;
                        width: 20.0625rem;
                        height: 6.25rem;
                        .register-button-style {
                            width: 20.0625rem;
                            height: 3.5rem;
                            background: #121212;
                            box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                            border-radius: .5rem; 
                            text-align: center;
                            line-height: 3.5rem;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 600;
                            font-size: 1.125rem;
                            color: #FFFFFF;
                            letter-spacing: .125rem;
                            text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                            font-style: normal;
                            cursor: pointer;
                            transition: all 0.15s ease-in-out;
                            
                            &:hover {
                                background: #2a2a2a;
                                box-shadow: 0px .375rem .75rem 0px rgba(0,0,0,0.15);
                                transform: translateY(-1px);
                            }
                            
                            &:active {
                                background: #0a0a0a;
                                box-shadow: 0px .125rem .25rem 0px rgba(0,0,0,0.2);
                                transform: translateY(1px);
                            }
                            
                            &:disabled {
                                background: #666666;
                                cursor: not-allowed;
                                opacity: 0.6;
                                
                                &:hover {
                                    background: #666666;
                                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                                    transform: none;
                                }
                                
                                &:active {
                                    background: #666666;
                                    box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                                    transform: none;
                                }
                            }
                        }
                        .register-tip {
                            margin-top: .3125rem;
                            span{
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 1rem;
                                color: #999999;
                                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                                text-align: left;
                                font-style: normal;
                            }
                            a {
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 1rem;
                                color: #262626;
                                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                                text-align: left;
                                font-style: normal;
                                text-decoration-line: underline;
                            }
                        }
                        // background: #121212;
                        // box-shadow: 0px .25rem .5rem 0px rgba(0,0,0,0.1);
                        // border-radius: .5rem;
                    }
                    .register-button-checkbox-title {
                        margin-top: .625rem;
                        font-family: Helvetica;
                        font-size: 1rem;
                        color: #262626;
                        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                        text-align: left;
                        font-style: normal;
                        text-transform: none
                    }
                    .register-button-checkbox {
                        margin-top: .1875rem;
                        font-family: Helvetica;
                        font-size: .875rem;
                        color: #999999;
                        text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                    }
                }
            }
        }
        .register-content-right {
            margin: 3.0625rem 0 0 4.0625rem;
            width: 28.125rem;
            height: 31.25rem;
            .register-checkbox {
                    // width: 23.75rem;
                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: .875rem;
                    color: #262626;
                    text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                    text-align: left;
                    font-style: normal;
                }
                div {
                    margin: .625rem 0 0 0;
                }
                // :v-deep 
            }
            .register-understand {
                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: .875rem;
                    color: #262626;
                    text-shadow: 0px .25rem .5rem rgba(0, 0, 0, 0.1);
                    text-align: left;
                    font-style: normal;
                }
            }
            .register-checkbox-two {
                .register-checkbox-two-title {
                    width: 19.0625rem;
                    height: 1.25rem;
                    font-family: Helvetica;
                    font-size: 1rem;
                    color: #262626;
                    line-height: 1.25rem;
                    text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    white-space: break-spaces;
                }
            }
            .register-checkbox-three {
                margin-top: 3.125rem;
                text-shadow: 0px .25rem .5rem rgba(0,0,0,0.1);
                text-align: left;
                font-style: normal;
                .register-checkbox-three-title {
                    margin-top: .625rem;
                    font-family: Helvetica;
                    font-size: 1rem;
                    color: #262626;
                    // line-height: 18px;
                    text-transform: none
                }
                .register-checkbox-three-content {
                    margin-top: .625rem;
                    font-family: Helvetica;
                    width: 19.75rem;
                    height: .0187rem;
                    font-size: .875rem;
                    color: #999999;
                    line-height: 1.25rem;
                    // text-shadow: 0px 4px 8px rgba(0,0,0,0.1);
                    // text-align: left;
                    // font-style: normal;
                    text-transform: none;
                    white-space: break-spaces;
                }
            }
            // background-color: #121212;
        }
    }
}
</style>
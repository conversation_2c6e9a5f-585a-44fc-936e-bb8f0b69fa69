<template>
  <div class="my-page">
     <div class="my-box">
         <div class="box-left">
             <div
 v-for="(item,index) in items" :key="index" 
                  :class="[
                    item.selected ? 'selected' : 'left-selected',
                    { 'loading': isNavigating && route.path !== item.route }
                  ]" 
                  @click="handleNavigation(item.route)">
                 <div>
                     {{ item.name }}
                     <span v-if="isNavigating && route.path !== item.route" class="loading-dot">...</span>
                 </div>
             </div>
         </div>
         <div class="box-right">
             <ClientOnly>
               <NuxtPage />
             </ClientOnly>
         </div>
     </div>
  </div>
 </template>
 
 <script setup lang="ts">
 import { ref, watch } from 'vue';
 import { useRoute } from 'vue-router';
 
 const route = useRoute();
 
 const items = ref([
     {
         name: 'Account Dashboard',
         route: '/my-info/subpage/account-dashboard',
         selected: false
     },
     {
         name: 'My Orders',
         route: '/my-info/subpage/orders',
         selected: false
     },
     {
         name: 'My Wishlist',
         route: '/my-info/subpage/wishlist',
         selected: false
     },
     {
         name: 'Address Management',
         route: '/my-info/subpage/address-management',
         selected: false
     },
     {
         name: 'My Invoices',
         route: '/my-info/subpage/invoices',
         selected: false
     },
     {
         name: 'My Coupons',
         route: '/my-info/subpage/coupons',
         selected: false
     },
     {
         name: 'Contact Us',
         route: '/my-info/subpage/contact-us',
         selected: false
     },
     {
         name: 'Shipment Tracking',
         route: '/my-info/subpage/shipment-tracking',
         selected: false
     },
     {
         name: 'Change Password',
         route: '/my-info/subpage/change-password',
         selected: false
     },
     {
       name: 'Payment',
       route: '/my-info/subpage/payment',
       selected: false
     },
     {
       name: 'Order Details',
       route: '/my-info/subpage/order-details',
       selected: false
     },
     {
       name: 'Invoice Detail',
       route: '/my-info/subpage/invoice-details',
       selected: false
     }
 ])
 
 // 防抖标志
 const isNavigating = ref(false);
 
 // 根据当前路由设置选中状态
 const updateSelectedState = () => {
     const currentPath = route.path;
     items.value.forEach(item => {
         item.selected = currentPath === item.route;
     });
 }
 
 // 监听路由变化
 watch(() => route.path, updateSelectedState, { immediate: true });
 
 /**
  * 处理导航点击事件
  * @param {string} targetRoute 目标路由
  */
 const handleNavigation = async (targetRoute: string) => {
     // 防抖处理，避免快速点击
     if (isNavigating.value) {
         return;
     }
     
     // 如果当前已经在目标路由，不需要重复导航
     if (route.path === targetRoute) {
         return;
     }
     
     try {
         isNavigating.value = true;
         
         // 使用 Nuxt 的导航
         await navigateTo(targetRoute);
         
         // 导航成功后更新选中状态
         updateSelectedState();
         
     } catch (error) {
         console.error('导航失败:', error);
         // 可以在这里添加用户提示
         // 例如：显示错误消息或回退到默认页面
     } finally {
         // 延迟重置防抖标志，确保导航完成
         setTimeout(() => {
             isNavigating.value = false;
         }, 300);
     }
 }
 </script>
 
 <style scoped lang="scss">
     .my-page {
         width: 100%;
         min-height: 75rem;
         background: #F5F5F5;
         .my-box {
             margin: 0 auto;
             padding-top: 3.75rem;
             width: 87.5rem;
             display: flex;
             justify-content: space-between;
             .box-left {
                 width: 15rem;
                 min-height: 30rem;
                 border-radius: .5rem;
                 .left-selected {
                     background: #FFFFFF;
                     div {
                         cursor: pointer;
                         margin: 0 auto;
                         width: 12.75rem;
                         height: 4.1875rem;
                         // background: #FFFFFF;
                         line-height:4.1875rem ;
                         border-bottom: 1px solid #E0E0E0;
                         font-family: SFProDisplay, SFProDisplay;
                         font-weight: 400;
                         font-size: 1rem;
                         color: #3D3D3D;
                         text-align: left;
                         font-style: normal;
                         transition: all 0.3s ease;
                         
                         &:hover {
                             background-color: #f8f8f8;
                         }
                     }
                 }
                 .selected{
                     background: #EBEBEB;
                     div {
                         margin: 0 auto;
                         width: 12.75rem;
                         height: 4.1875rem;
                         line-height:4.1875rem ;
                         border-bottom: 1px solid #E0E0E0;
                         font-family: SFProDisplay, SFProDisplay;
                         font-weight: bold;
                         font-size: 1rem;
                         color: #3D3D3D;
                         text-align: left;
                         font-style: normal;
                     }
                 }
                 .loading {
                     opacity: 0.7;
                     pointer-events: none;
                     
                     .loading-dot {
                         animation: loading 1.5s infinite;
                         color: #666;
                     }
                 }
                 .left-selected:last-child {
                     div {
                         border-bottom: 0px solid #E0E0E0;
                     }
                 }
             }
             .box-right {
                 width: 70.3125rem;
                 // height: 56.25rem;
                 // background: #FFFFFF;
                 border-radius: .5rem .5rem 0 0;
             }
         }
     }
     
     @keyframes loading {
         0%, 20% { opacity: 0; }
         50% { opacity: 1; }
         100% { opacity: 0; }
     }
 </style>
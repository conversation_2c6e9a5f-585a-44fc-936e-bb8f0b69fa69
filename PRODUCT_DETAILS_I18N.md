# 产品详情页面国际化实现

## 概述

本文档描述了为产品详情页面 (`pages/index/product-wholesale-details/[url_key].html.vue`) 添加的国际化支持。

## 实现的功能

### 1. 模板国际化

#### 加载和错误状态
- 加载状态文本：`{{ $t('productDetails.loading') }}`
- 错误状态文本：`{{ $t('productDetails.productNotFound') }}`
- 网络错误提示：`{{ $t('productDetails.networkError') }}`
- 重试按钮：`{{ $t('productDetails.retry') }}`

#### 产品信息区域
- 描述标签：`{{ $t('productDetails.description') }}`
- 规格标签：`{{ $t('productDetails.specification') }}`
- 尺寸标签：`{{ $t('productDetails.dimensions') }}`
- 配置标签：`{{ $t('productDetails.profile') }}`
- 跌落等级标签：`{{ $t('productDetails.dropRating') }}`

#### 产品选择区域
- 品牌标签：`{{ $t('productDetails.brands') }}`
- 颜色标签：`{{ $t('productDetails.colors') }}`
- 运费优惠：`{{ $t('productDetails.shippingUpTo', { percent: specialPriceData.discount_percent }) }}`

#### 操作按钮
- 加入购物车：`{{ $t('productDetails.addToCart') }}`
- 立即购买：`{{ $t('productDetails.buyNow') }}`
- 安全结账：`{{ $t('productDetails.checkoutSecurely') }}`
- 接受的支付方式：`{{ $t('productDetails.acceptedPayments') }}`
- 免息付款：`{{ $t('productDetails.interestFreePayments') }}`
- 了解更多：`{{ $t('productDetails.learnMore') }}`

#### 相关产品
- 相关产品标题：`{{ $t('productDetails.relatedProducts') }}`

#### 媒体预览
- 关闭按钮：`:title="$t('productDetails.mediaPreview.close')"`
- 上一张按钮：`:title="$t('productDetails.mediaPreview.previous')"`
- 下一张按钮：`:title="$t('productDetails.mediaPreview.next')"`

### 2. 脚本国际化

#### 错误处理
- 产品不存在错误：`t('productDetails.productNotFound')`
- 相关产品添加成功：`t('product.addToCartSuccess')`
- 相关产品添加失败：`t('product.addToCartFailed')`
- 相关产品添加错误：`t('product.addToCartError')`

### 3. 翻译键更新

#### 新增的翻译键
- `productDetails.retry` - 重试按钮
- `productDetails.networkError` - 网络错误提示
- `productDetails.brands` - 品牌标签
- `productDetails.colors` - 颜色标签

#### 支持的语言
- 中文 (cn.json)
- 英文 (en.json)
- 意大利语 (it.json)

## 使用方法

### 1. 语言切换
用户可以通过现有的语言切换器来切换页面语言，所有文本将自动更新。

### 2. 动态内容
- 运费优惠信息支持动态参数：`{ percent: specialPriceData.discount_percent }`
- 错误消息支持动态内容显示

### 3. 媒体预览
媒体预览功能支持键盘导航和国际化提示文本。

## 技术实现

### 1. 导入国际化
```typescript
const { t } = useI18n()
```

### 2. 模板中使用
```vue
{{ $t('productDetails.loading') }}
```

### 3. 脚本中使用
```typescript
ElMessage.success(t('product.addToCartSuccess'))
```

## 注意事项

1. 所有硬编码的文本都已替换为国际化键
2. 错误处理和用户反馈消息都已国际化
3. 支持动态参数传递
4. 媒体预览功能包含无障碍支持（title属性）
5. 加载和错误状态提供完整的用户体验

## 测试建议

1. 测试不同语言环境下的显示效果
2. 验证动态参数的正确传递
3. 检查错误状态下的用户体验
4. 测试媒体预览功能的国际化支持 
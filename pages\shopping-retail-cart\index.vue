<template>
  <NuxtLayout>
    <div class="retail-cart">
      <div class="select-all">
        <div style="display: flex;align-items: center;justify-content: space-between;width: 53.125rem;">
          <div class="all-left">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              :disabled="cartItems.length === 0"
              @change="handleCheckAllChange"
            >
              {{ t('retailCart.selectAll') }}
            </el-checkbox>
            <div class="detail" @click="batchRemoveCart">
              {{ t('retailCart.delete') }}
            </div>
          </div>
          <div class="all-right">
            {{ t('retailCart.itemsTotal', { count: checkData.length }) }}
          </div>
        </div>
      </div>
      <div class="content">
        <div class="left">
          <MLKShoppingCartRetail 
            ref="retailCartRef"
            :check-all="checkAll"
            :cart-items="cartItems"
            :cart-data="cartData || {}"
            :checked-item-info="checkedItems"
            @update-check="checkAlls"
            @update-checked-items="checkedData"
            @update-is-indeterminate="isIndeterminates"
            @update-cart="handleCartUpdate"
          />
        </div>
        <div class="right">
          <div class="right-top">
            <div class="order">
              {{ t('retailCart.orderSummary') }}
            </div>
            <div class="total-box">
              <div class="total-left">
                {{ t('retailCart.subtotal') }}
              </div>
              <div class="total-right">
                 {{ priceObtain || '€' }} {{ Subtotal.toFixed(2) || '0.00' }}
              </div>
            </div>
            <div v-if="(cartData?.discount_amount || 0) > 0" class="saved">
              <div class="saved-left">
                {{ t('retailCart.saved') }}
              </div>
              <div class="saved-right">
                -{{ cartData?.formatted_prices?.discount_amount || '0.00' }}
              </div>
            </div>
            <div v-if="(cartData?.tax_total || 0) > 0" class="total-box">
              <div class="total-left">
                {{ t('retailCart.tax') }}
              </div>
              <div class="total-right">
                {{ cartData?.formatted_prices?.tax_total || '0.00' }}
              </div>
            </div>
            <div v-if="(cartData?.shipping_amount || 0) > 0" class="total-box">
              <div class="total-left">
                {{ t('retailCart.shipping') }}
              </div>
              <div class="total-right">
                {{ cartData?.formatted_prices?.shipping_amount || '0.00' }}
              </div>
            </div>
            <div class="total-box">
              <div class="total-left">
                {{ t('retailCart.total') }}
              </div>
              <div class="total-right">
                {{ priceObtain || '€'}} {{ totalAmount.toFixed(2) || '0.00' }}
              </div>
            </div>
            <div class="xian"/>
            <div class="free">
              {{ t('retailCart.freeShipping') }}
            </div>
            <div class="promotional">
              {{ t('retailCart.promotionalCode') }}
            </div>
            <div class="code">
              <el-input 
                v-model="couponCode" 
                type="text" 
                :placeholder="t('retailCart.enterCouponCode')"
                @keyup.enter="applyCoupons"
              />
              <el-button @click="applyCoupons">{{ t('retailCart.selectMyCoupons') }}</el-button>
            </div>
            <div class="checkout" @click="goToCheckout">
              {{ t('retailCart.checkoutNow') }}
            </div>
          </div>
          <div class="right-bottom">
            <div class="text">
              {{ t('retailCart.estimateShipping') }}
            </div>
            <div class="text">
              {{ t('retailCart.shippingWeight', { weight: calculateTotalWeight() }) }}
            </div>
            <div class="title">
              {{ t('retailCart.weAccept') }}
            </div>
            <div class="imgs">
              <div
                v-for="(item, index) in shippingMethod?.images"
                :key="index"
                class="img"
              >
                <img :src="item?.image" alt="">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { storeToRefs } from 'pinia'
import type { CheckboxValueType } from 'element-plus'
import type { MLKShoppingCartRetail } from '#components'
import { useLanguageSwitch } from "~/composables/useLanguageSwitch";
import { useLanguageStore } from "~/stores/language";

// i18n
const { t } = useI18n()

definePageMeta({
    layout: "personal-center"
})
// 初始化语言
const { switchLanguage } = useLanguageSwitch();
// 获取当前语言
const { language } = useLanguageStore()
// 切换语言
switchLanguage(language)


// 购物车store
const cartStore = useShoppingCartStore()

// 使用storeToRefs来正确解构响应式数据
const { 
  cartItems, 
  cartData, 
  isLoggedIn
} = storeToRefs(cartStore)

// 获取非响应式的方法
const { fetchCartData,applyCoupon } = cartStore

// 路由
const router = useRouter()

// 响应式数据
const checkAll = ref(false)
const isIndeterminate = ref(false)
const couponCode = ref('')

// 确保在DOM挂载后再获取数据，避免与布局中的调用冲突
onMounted(async () => {
  try {
    console.warn('📄 购物车页面: 开始初始化')
    console.warn('📄 购物车页面: 当前登录状态:', isLoggedIn.value)
    console.warn('📄 购物车页面: 当前购物车商品数量:', cartItems.value.length)
    
    // 等待一小段时间确保用户状态和布局初始化完成
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 强制重新获取最新数据
    console.warn('📄 购物车页面: 强制重新获取购物车数据')
    await fetchCartData()
    // 保存配送方式
    await saveShippingMethod()
    console.warn('📄 购物车页面: 数据获取完成，最终商品数量:', cartItems.value.length)
  } catch (error) {
    console.error('📄 购物车页面: 获取购物车数据失败:', error)
  }
})
// 价格标识
const priceObtain = computed(() => {
  return cartData.value?.formatted_prices?.grand_total.slice(0,1)
})

const checkData = ref<string[]>([])
// 监听选中数据变化
const checkedData = (val: string[]) => { 
  checkData.value = val
}
const checkAlls = (val: boolean) => {
  checkAll.value = val
}

const isIndeterminates = (val: boolean) => {
  isIndeterminate.value = val
}
const checkedItems = ref<string[]>([])
// 监听全选框变化
const handleCheckAllChange = (val: CheckboxValueType) => {
  checkedItems.value = val ? cartItems.value.map(item => item.cart_item_id) : []
  // console.warn('📄 购物车页面: 全选框变化:', val)
  isIndeterminate.value = false
}

//计算选中商品价格
const Subtotal = computed(() => {
  if(checkData.value.length > 0){
    return cartItems.value.filter(item => checkData.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.total))
    }, 0)
  }
  return 0
})
// 计算打折后的总金额
const totalAmount = computed(() => {
  if(checkData.value.length > 0){
    const total = cartItems.value.filter(item => checkData.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.total))
    }, 0)
    return (total - (Number(cartData.value?.discount_amount) || 0) < 0 ? 0 : total - (Number(cartData.value?.discount_amount) || 0))
  }
  return 0
})
interface shippingInterface{
  images: Array<{
    image: string,
    title: string,
    link: string,
    intro: string
  }>
}
const shippingMethod = ref<shippingInterface>()
// 获取支持的支付方式图标
const saveShippingMethod = async () => {
  const result = await cartStore.getPaymentMethod()
  // console.warn('📄 购物车页面: 保存配送方式结果:', result)
  if(result.success){
    shippingMethod.value = result.data
    console.warn('📄 购物车页面: 保存配送方式结果:', shippingMethod.value);
    
  }else{
    ElMessage.error(result.message)
  }
}

// 监听购物车数据变化，更新选中状态
watch(cartItems, (newItems) => {
  if (newItems.length === 0) {
    checkAll.value = false
    isIndeterminate.value = false
  }
}, { deep: true })

// 计算选中商品的总重量（假设每个商品0.003kg）
const calculateTotalWeight = () => {
  if (checkData.value.length > 0) {
   const totalWeight = cartItems.value.filter(item => checkData.value.includes(item.cart_item_id)).reduce((total, item) => {
      return  total + (Number(item.weight) * item.quantity)
    }, 0)
    return totalWeight.toFixed(3)
  }
  return '0.000'
}

// 处理购物车更新
const handleCartUpdate = async () => {
  await fetchCartData()
}

// 应用优惠券
const applyCoupons = async () => {
  if (!couponCode.value.trim()) {
    ElMessage.warning(t('retailCart.pleaseEnterCouponCode'))
    return
  }
  
  try {
    const result = await applyCoupon(couponCode.value)
    console.warn('📄 购物车页面: 应用优惠券结果:', result)
    // 这里可以调用应用优惠券的API
    if(result.success){
      ElMessage.success(t('retailCart.couponAppliedSuccessfully'))
      await fetchCartData() // 重新获取购物车数据
    }else{
      ElMessage.error(result.message)
    }
  } catch(error) {
    console.error('📄 购物车页面: 应用优惠券失败:', error)
    ElMessage.error(t('retailCart.couponApplicationFailed'))
  }
}
const retailCartRef = ref<InstanceType<typeof MLKShoppingCartRetail> | null>(null)

// 批量删除购物车商品
const batchRemoveCart = () => {
  ElMessageBox.confirm(t('retailCart.removeConfirmText'), t('retailCart.removeConfirm'), {
    confirmButtonText: t('retailCart.delete'),
    cancelButtonText: t('retailCart.remove'),
  }).then(() => {
    retailCartRef.value?.batchRemoveCart()
  })
  // retailCartRef.value?.batchRemoveCart()
}

// 跳转到结账页面
const goToCheckout = () => {
  if (cartItems.value.length === 0) {
    ElMessage.warning(t('retailCart.cartIsEmpty'))
    return
  }
  
  if (!isLoggedIn.value) {
    ElMessage.warning(t('retailCart.pleaseLoginFirst'))
    router.push('/login')
    return
  }
  
  router.push('/payment')
}

</script>

<style lang="scss" scoped>
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
  background-color: #3D3D3D;
  border-color: #3D3D3D;
}
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #3D3D3D;
  border-color: #3D3D3D;
}
.retail-cart{
  width: 100%;
  padding-bottom:7.125rem;
  background: #F5F5F5;
   .select-all{
    padding-top: 2.9375rem;
    margin:0 auto;
    width: 87.5625rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .all-left{
      display: flex;
      align-items: center;
      .el-checkbox{
        ::v-deep .el-checkbox__label{
          font-size: 1rem;
          color: #000000;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 600;
        }
      }
      .detail{
        margin-left: 1.5rem;
        height: 1.5rem;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: 600;
        font-size: 1rem;
        color: #000000;
        line-height: 1.5rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .content{
    box-sizing: border-box;
    margin: 1.25rem auto;
    width: 87.5625rem;
    display: flex;
    justify-content: space-between;
    .left{
      width: 54.875rem;
      border-radius: .9375rem;
      box-sizing: border-box;
     
    }
    .right{
      width: 31.125rem;
      .right-top{
        width: 31.125rem;
        height: 34.8125rem;
        background: #FFFFFF;
        border-radius: .9375rem;
        padding: 3.5625rem 2.5rem 2.75rem 2.5rem;
        box-sizing: border-box;
        .order{
          font-family: SFProDisplay, SFProDisplay;
          font-weight: bold;
          font-size: 1.25rem;
          color: #111111;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .total-box{
          margin-top: 2.125rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #000000;
          font-style: normal;
          text-transform: none;
        }
        .saved{
          margin-top: 2.125rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: 1rem;
          color: #979797;
          font-style: normal;
          text-transform: none;
        }
        .xian{
          margin-top: 1.8125rem;
          width: 26.1875rem;
          height: .0625rem;
          border-bottom: .0625rem solid #E0E0E0;
        }
        .free{
          margin-top: 1.5625rem;
          width: 25.5rem;
          height: 1.5rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 400;
          font-size: 1.125rem;
          color: #3D3D3D;
          line-height: 1.5rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .promotional{
          margin-top: .625rem;
          height: 1.6875rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .875rem;
          color: #3D3D3D;
          line-height: 1.6875rem;
          font-style: normal;
          text-transform: none;
        }
        .code{
          margin-top: .625rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .el-input{
            width: 13.75rem;
            height: 3.5rem;
            background: #FFFFFF;
            border-radius: .375rem;
            border: .0625rem solid #BABABA;
          }
          .el-button{
            height: 3.5rem;
            margin-left: .3125rem;
            background: #F7F7F7;
            border-radius: 6px;
            border: 1px solid #3D3D3D;
            font-family: SFProDisplay, SFProDisplay;
            font-weight: 500;
            font-size: 1rem;
            color: #3D3D3D;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }

        }
        .checkout{
          margin-top: 2.25rem;
          width: 100%;
          height: 3.5rem;
          background: #000000;
          border-radius: .375rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1rem;
          color: #FFFFFF;
          line-height: 3.5rem;
          text-align: center;
          font-style: normal;
        }
      }
      .right-bottom{
        width: 31.125rem;
        height: 23.5625rem;
        margin-top: 1.9375rem;
        background: #FFFFFF;
        border-radius: .9375rem;
        padding: 2rem 2.5rem 0 2.4375rem;
        box-sizing: border-box;
        .text{
          margin-bottom: .5625rem;
          width: 25.5rem;
          height: 1rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 500;
          font-size: .9375rem;
          color: #3D3D3D;
          line-height: 1rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .title{
          margin-top: 1.3125rem;
          height: 1rem;
          font-family: SFProDisplay, SFProDisplay;
          font-weight: 800;
          font-size: 1.125rem;
          color: #545454;
          line-height: 1rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .imgs{
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 1.0625rem;
          gap: .9375rem;
          .img{
            flex-shrink: 0;
            width: 5.5rem;
            height: 3rem;
            border-radius: .25rem;
            img{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
  
}
</style>
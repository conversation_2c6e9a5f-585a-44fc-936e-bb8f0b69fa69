<template>
    <div class="Carousel_page">
        <div class="Carousel_content">
            <div class="Carousel_box">
                <ClientOnly>
                    <swiper
                      :key="`carousel-${swiperKey}`"
                      :slides-per-view="6"
                      :space-between="20" 
                      :allow-touch-move="false"
                      :breakpoints="breakpoints" 
                      :modules="modules"
                      class="mySwiper" 
                      @swiper="onSwiper">
                        <swiper-slide v-for="item in dataList " :key="item.id" class="Carousel_goods" >
                            <div class="Carousel_photo" style="cursor: pointer;" @click="goToProductDetail(item?.url_key || '')">
                              <div class="img">
                                <img style="width: 12.5rem;" :src="getProductImage(item)" :alt="item.name">
                              </div>
                              <div class="name" :title="item.name">
                                  {{ item.name }}
                              </div>
                              <div v-if="item.color && item.color.length > 0" class="Carousel_goods_colors">
                                  <div 
                                    v-for="(color,index) in item.color" 
                                    :key="index" 
                                    class="bg-color" 
                                    :class="{ 'active': item.id && selectedColors[item.id] === index }"
                                    :style="{ backgroundColor: color.swatch_value, '--hover-color': color.swatch_value }"
                                    @click.stop="item.id && selectColor(item.id, index, color)"
                                  />
                              </div>
                              <div class="Carousel_goods_amounts">{{ item.formatted_price || '' }}</div>
                            </div>
                        </swiper-slide>
                    </swiper>
                </ClientOnly>
            </div>
            <div class="Carousel_arrow">
                <div :class="['btn',isPrevDisabled ? 'disabled' : '']" @click="!isPrevDisabled && prevSlide()">
                    <el-icon :size="27" :color="isPrevDisabled?'#ccc':'#000'">
                        <Back />
                    </el-icon>
                </div>
                <div :class="['btn',isNextDisabled ? 'disabled' : '']" @click="!isNextDisabled && nextPage()">
                    <el-icon :size="27" :color="isNextDisabled?'#ccc':'#000'">
                        <Right />
                    </el-icon>
                </div>
            </div>
        </div>
        
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { useRouter } from 'vue-router';
import 'swiper/css';

import 'swiper/css/free-mode';
import 'swiper/css/pagination';

import { FreeMode, Pagination, Navigation } from 'swiper/modules';
interface CarouselItem {
    id?: number;
    name?: string;
    formatted_price?: string;
    base_image?: string;
    url_key:string;
    color?: { option_label: string; swatch_value?: string }[];
}
const props = defineProps<{
    dataList: CarouselItem[] | undefined;
}>()
const router = useRouter();

// 跳转到产品详情页
const goToProductDetail = (url_key: string) => {
    if (url_key) {
        router.push(`/product-details/${url_key}.html`);
    }
}

// 存储每个产品选中的颜色索引
const selectedColors = ref<Record<number, number>>({})

// 获取产品图片的方法
const getProductImage = (item: CarouselItem) => {
    if (!item.id) return item.base_image || ''
    
    const selectedColorIndex = selectedColors.value[item.id]
    if (selectedColorIndex !== undefined && item.color && item.color[selectedColorIndex]) {
        const selectedColor = item.color[selectedColorIndex]
        // 如果颜色选项有swatch_value（图片路径），则使用它
        if (selectedColor.swatch_value) {
            return selectedColor.swatch_value
        }
    }
    
    // 默认返回base_image
    return item.base_image || ''
}

// 选择颜色的方法
const selectColor = (productId: number, colorIndex: number, _color: { option_label: string; swatch_value?: string }) => {
    selectedColors.value[productId] = colorIndex
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperInstance = ref<any>(null);
const swiperKey = ref(0);
const isInitialized = ref(false);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onSwiper = (swiper: any) => {
  swiperInstance.value = swiper;
  isInitialized.value = true;
  
  // 添加滑动事件监听器
  swiper.on('slideChange', () => {
    updateButtonStates();
  });
  
  // 添加更多事件监听器确保状态正确更新
  swiper.on('init', () => {
    updateButtonStates();
  });
  
  swiper.on('afterInit', () => {
    updateButtonStates();
  });
  
  swiper.on('resize', () => {
    updateButtonStates();
  });
  
  // 初始化状态
  updateButtonStates();
};

// 更新按钮状态的函数
const updateButtonStates = () => {
  if (swiperInstance.value) {
    isBeginning.value = swiperInstance.value.isBeginning;
    isEnd.value = swiperInstance.value.isEnd;
    
    // 强制触发响应式更新
    nextTick(() => {
      // 确保状态更新
    });
  }
};
const breakpoints = ref({
    '640': {
        slidesPerView: 6,
        spaceBetween: 20,
    },
    '768': {
        slidesPerView: 6,
        spaceBetween: 40,
    },
    '1024': {
        slidesPerView: 6,
        spaceBetween: 50,
    },
    '1920': {
        slidesPerView: 6,
        spaceBetween: 20,
    },
})
const modules = [Navigation, FreeMode, Pagination];

// 上一页
const prevSlide = () => {
    if (swiperInstance.value && !isBeginning.value) {
        swiperInstance.value.slidePrev();
    }
};
// 下一页
const nextPage = () => {
    if (swiperInstance.value && !isEnd.value) {
        swiperInstance.value.slideNext();
    }
};

// 手动初始化方法
const manualInitSwiper = () => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // 销毁现有实例
  if (swiperInstance.value) {
    try {
      swiperInstance.value.destroy(true, true);
    } catch (error) {
      console.warn('⚠️ 销毁实例时出错:', error);
    }
    swiperInstance.value = null;
    isInitialized.value = false;
  }
  
  // 强制重新渲染
  swiperKey.value++;
};
const isBeginning = ref(true)
const isEnd = ref(false)

// 使用计算属性确保状态响应性
const isPrevDisabled = computed(() => isBeginning.value)
const isNextDisabled = computed(() => isEnd.value)

// 根据数据长度初始化结束状态
const initializeButtonStates = () => {
  if (Array.isArray(props.dataList)) {
    // 如果数据项少于等于6个，禁用下一页按钮
    if (props.dataList.length <= 6) {
      isEnd.value = true;
    } else {
      isEnd.value = false;
    }
    // 初始状态，禁用上一页按钮
    isBeginning.value = true;
    
  } else {
    // 如果没有数据，两个按钮都禁用
    isBeginning.value = true;
    isEnd.value = true;
  }
};

// 初始化按钮状态
initializeButtonStates();

// 监听数据变化，更新状态
watch(() => props.dataList, (newDataList) => {
  if (Array.isArray(newDataList)) {
    // 重新初始化按钮状态
    initializeButtonStates();
    
    // 延迟更新状态，确保swiper已经初始化
    nextTick(() => {
      setTimeout(() => {
        updateButtonStates();
      }, 100);
    });
  }
}, { immediate: true });

// 监听按钮状态变化
watch([isBeginning, isEnd], ([_newIsBeginning, _newIsEnd]) => {
  // 强制更新计算属性
  nextTick(() => {
    // 触发响应式更新
  });
});

// 组件挂载
onMounted(() => {
    // 只在客户端执行
    if (!import.meta.client) return;

  // 确保 DOM 完全渲染
  nextTick(() => {
    if (!isInitialized.value) {
      setTimeout(() => {
        manualInitSwiper();
      }, 100);
    }
    
    // 强制更新按钮状态
    setTimeout(() => {
      updateButtonStates();
    }, 200);
  });
});

</script>

<style scoped lang="scss">
.swiper {
    width: 100%;
    height: 100%;
}


.Carousel_page {
    width: 100%;
    height: 32.375rem;
    .Carousel_content {
        position: relative;
        height: 100%;
        width: 82.0625rem;
        margin: 0 auto;
        .Carousel_box {
            width: 100%;
            height: 27.4375rem;
            font-size: 1.5rem;
            text-align: center;
            box-sizing: border-box;

                            .Carousel_goods {
                    width: 13.125rem;
                    height: 100%;
                    border-radius: .625rem;
                    background-color: #EFEFEF;
                    box-sizing: border-box;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-0.25rem);
                        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
                    }
                    
                    .Carousel_photo{
                      width: 100%;
                      height: 18.125rem;
                      transition: all 0.3s ease;
                      
                      &:hover {
                          opacity: 0.9;
                      }
                      
                      .img{
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        img{
                          width: 80%;
                          transition: transform 0.3s ease;
                        }
                      }
                  .name{
                    padding: 0rem .625rem;
                    height: 3.25rem;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 1.5rem;
                    color: #000000;
                    line-height: 3.25rem;
                    text-align: center;
                    font-style: normal;
                    text-transform: none;
                    overflow:hidden;
                    text-overflow:ellipsis;
                    white-space:nowrap;
                    box-sizing: border-box;
                  }
                }

                .Carousel_goods_colors {
                  margin: 0 auto;
                  margin-top: .375rem;
                  box-sizing: border-box;
                  width: 100%;
                  height: 1.875rem;
                  display: flex;
                  justify-content: center;
                  gap: 0 .375rem;

                  .bg-color {
                      width: 1.3125rem;
                      height: 1.3125rem;
                      border-radius: 50%;
                      cursor: pointer;
                      box-shadow: inset .0625rem .125rem .125rem 0rem rgba(0, 0, 0, 0.1);
                      transition: all 0.3s ease;
                      
                      &:hover {
                        box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
                      }
                      
                      &.active {
                        box-shadow: 0 0 0 .2188rem #fff, 0 0 0 .4375rem var(--hover-color);
                      }
                  }
                }


                .Carousel_goods_amounts {
                    height: 3rem;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 1.5rem;
                    color: #18141E;
                    line-height: 3rem;
                    text-align: center;
                    font-style: normal;
                    text-transform: none;
                    box-sizing: border-box;
                }
            }
        }
        .Carousel_arrow {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 3.125rem;
          height: 1.875rem;
          display: flex;
          justify-content: space-between;
          
          .btn {
              cursor: pointer;
              transition: all 0.3s ease;
              user-select: none;
              
              &.disabled{
                cursor: not-allowed;
                opacity: 0.5;
            }
          }
          
          
        }
    }
    
    // 加载动画样式
    .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        
        .spinner {
            width: 3rem;
            height: 3rem;
            border: 0.25rem solid #f3f3f3;
            border-top: 0.25rem solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading-text {
            font-size: 1rem;
            color: #666;
            font-family: PingFangSC, PingFang SC;
        }
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
}
</style>
.sliding-element-right {
  /* position: relative;
  width: 100%; */
  opacity: 0;
  animation: slideInFromRight 1s ease-out forwards;
}


@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.sliding-element-left {
  /* position: relative;
  width: 100%; */
  opacity: 0;
  animation: slideInFromLeft 1s ease-out forwards;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
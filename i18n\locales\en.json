{"hello": "Hello", "test": {"title": "i18n Test Page", "description": "This page is used to test internationalization functionality", "languageSwitcher": "Language Switcher", "basicTranslations": "Basic Translations", "currentInfo": "Current Information", "urlInfo": "URL Information", "currentLocale": "Current Locale", "defaultLocale": "Default Locale", "availableLocales": "Available Locales", "currentUrl": "Current URL", "baseUrl": "Base URL", "footer": "This is a test page for i18n functionality", "pageTitle": "i18n Test Page", "hello": "Hello", "welcome": "Welcome", "goodbye": "Goodbye"}, "retailCart": {"selectAll": "Select All", "delete": "Delete", "itemsTotal": "Items {count} total", "orderSummary": "Order Summary", "subtotal": "Subtotal", "saved": "Saved", "tax": "Tax", "shipping": "Shipping", "total": "Total", "freeShipping": "Free shipping over US$ 79.00", "promotionalCode": "Promotional Code", "enterCouponCode": "Enter coupon code", "selectMyCoupons": "Select My Coupons", "checkoutNow": "CHECKOUT NOW", "estimateShipping": "Estimate Shipping", "shippingWeight": "Shipping Weight: {weight} kg", "weAccept": "We Accept", "pleaseEnterCouponCode": "Please enter coupon code", "couponAppliedSuccessfully": "Coupon applied successfully", "couponApplicationFailed": "Coupon application failed", "cartIsEmpty": "<PERSON><PERSON> is empty, cannot checkout", "pleaseLoginFirst": "Please login first", "removeConfirmText": "Are you sure you want to delete all selected items?", "removeConfirm": "Confirm Delete", "remove": "Remove"}, "wholesaleCart": {"selectAll": "Select All", "delete": "Delete", "itemsTotal": "Items {count} total", "orderSummary": "Order Summary", "subtotal": "Subtotal", "saved": "Saved", "total": "Total", "freeShipping": "Free shipping over US$ 79.00", "promotionalCode": "Promotional Code", "enterCouponCode": "Enter coupon code", "selectMyCoupons": "Select My Coupons", "checkoutNow": "CHECKOUT NOW", "estimateShipping": "Estimate Shipping", "shippingWeight": "Shipping Weight: {weight} kg", "weAccept": "We Accept", "pleaseEnterCouponCode": "Please enter coupon code", "couponAppliedSuccessfully": "Coupon applied successfully", "couponApplicationFailed": "Coupon application failed", "cartIsEmpty": "<PERSON><PERSON> is empty, cannot checkout", "pleaseLoginFirst": "Please login first", "searchProducts": "Search products...", "noSearchResults": "No products found", "searchTip": "Try different keywords or check your spelling", "emptyCart": "Cart is empty", "goShopping": "Go Shopping"}, "header": {"search": "Search", "wishlist": "Wishlist", "wholesale": "WHOLESALE", "cart": "<PERSON><PERSON>", "user": "User", "selectLanguage": "Select"}, "personalCenter": {"secureCheckout": "SECURE CHECKOUT", "user": "User", "continueShopping": "CONTINUE SHOPPING"}, "home": {"findDevice": "FIND YOUR DEVICE", "selectBrand": "Select brand", "selectDevice": "Select device", "shopByBrand": "SHOP BY BRAND", "inspirationText": "Step into our inspiration zone and feel the pulse of beautiful living.", "help": "Help"}, "productCard": {"addToCart": "Add to Cart", "inCart": "In Cart", "selectColor": "Please select color", "wholesaleLogin": "wholesale:", "login": "<PERSON><PERSON>", "toView": "to view", "removeFromWishlist": "Remove from Wishlist", "addToWishlist": "Add to Wishlist", "wishlistAdded": "Product added to wishlist", "wishlistRemoved": "Removed from wishlist", "wishlistAddFailed": "Failed to add to wishlist", "wishlistRemoveFailed": "Failed to remove from wishlist", "wishlistError": "Error during wishlist operation", "selectColorFirst": "Please select product color first", "new": "NEW"}, "newArrival": {"filter": "Filter", "brand": "Brand", "device": "<PERSON><PERSON>", "allBrands": "All Brands", "allDevices": "All Devices", "loading": "Loading...", "loadingNewProducts": "Loading new products...", "noNewProducts": "No new products", "noNewProductsDesc": "Sorry, there are no new products available at the moment", "addToCartSuccess": "The product has been added to the shopping cart.", "addToCartFailed": "Failed to add", "addToCartError": "Failed to add cart"}, "swiper": {"shopNow": "SHOP NOW"}, "shoppingCartRetail": {"emptyCart": "Cart is empty", "goShopping": "Go Shopping", "freeShipping": "FREE SHIPPING", "color": "Color", "weight": "Weight", "subtotal": "Subtotal", "subtotalWeight": "Subtotal Weight", "delete": "Delete", "brand": "Brand", "device": "<PERSON><PERSON>", "price": "Price", "quantity": "Quantity", "minus": "Minus", "add": "Add", "remove": "Remove", "updateQuantity": "Update Quantity", "quantityError": "Quantity Error", "removeConfirm": "Confirm Delete", "removeConfirmText": "Are you sure you want to delete this item?", "removeSuccess": "Remove Success", "removeFailed": "Remove Failed", "updateSuccess": "Update Success", "updateFailed": "Update Failed", "brandEmpty": "Brand is empty", "deviceEmpty": "Device is empty", "colorEmpty": "Color is empty"}, "wishlist": {"loginConfirmTitle": "Notice", "loginConfirmMessage": "You are not logged in. Would you like to go to the login page?", "confirm": "Confirm", "cancel": "Cancel", "pleaseLoginFirst": "Please login first", "addSuccess": "Successfully added to wishlist", "addFailed": "Failed to add to wishlist", "removeSuccess": "Successfully removed", "removeFailed": "Failed to remove", "moveToCartSuccess": "Successfully moved to cart", "moveToCartFailed": "Failed to move to cart", "clearSuccess": "Successfully cleared wishlist", "clearFailed": "Failed to clear wishlist", "fetchDataFailed": "Failed to fetch wishlist data"}, "wishlistPage": {"title": "My Wishlist", "emptyTitle": "Your wishlist is empty", "emptyDescription": "No items have been added to your wishlist yet", "goShopping": "Go Shopping", "selectAll": "Select All", "addToCart": "Add to Cart", "viewDetails": "View Details", "inStock": "In Stock", "outOfStock": "Out of Stock", "save": "Save", "addSelectedToCart": "Add Selected to Cart", "removeSelected": "Remove Selected", "removeConfirmTitle": "Confirm Remove", "removeConfirmMessage": "Are you sure you want to remove the selected {count} items from your wishlist?", "removeSuccess": "Item removed from wishlist", "removeFailed": "Remove failed", "addToCartSuccess": "Successfully added {count} items to cart", "addToCartFailed": "Failed to add to cart", "batchAddToCartError": "Error occurred while batch adding to cart", "batchRemoveError": "Error occurred while batch removing", "removeSelectedSuccess": "Successfully removed {count} items", "addToCartTitle": "Add to Cart", "selectVariant": "Select a variant", "selectColor": "Select a color", "selectBrand": "Select a brand", "selectDevice": "Select a device", "quantity": "Quantity", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete"}, "product": {"addToCart": "Add to Cart", "removeFromWishlist": "Remove from Wishlist", "addToWishlist": "Add to Wishlist", "addToCartSuccess": "Product added to cart", "addToCartFailed": "Failed to add to cart", "addToCartError": "Error occurred while adding to cart", "invalidProduct": "Invalid product data", "wishlistToggleError": "Error occurred during wishlist operation", "shoppingCart": "Shopping Cart"}, "productDetails": {"loading": "Loading...", "productNotFound": "Product not found or failed to load", "retry": "Retry", "networkError": "Network connection error, please check your network and try again", "description": "Description", "specification": "Specification", "dimensions": "Dimensions", "profile": "Profile", "dropRating": "Drop Rating", "device": "<PERSON><PERSON>", "selectBrand": "Select brand", "selectDevice": "Select device", "color": "Color", "quantity": "Quantity", "addToCart": "Add to Cart", "alreadyInCart": "Already in the shopping cart ({count})", "buyNow": "Buy it now", "checkoutSecurely": "CHECKOUT SECURELY WITH", "acceptedPayments": "Accepted Payments", "interestFreePayments": "4 interest-free payments of $16.00 with Klarna", "learnMore": "Learn More", "shippingUpTo": "Shipping up to {percent}% OFF", "pleaseSelectColor": "Please select product color first", "productInCart": "Product is already in cart", "relatedProducts": "You May Also Like", "brands": "Brands", "colors": "Colors", "mediaPreview": {"close": "Close", "previous": "Previous", "next": "Next"}}, "news": {"loadingTags": "Loading Tags", "loadTagsFailed": "Failed to load tags, please refresh the page and try again", "unknownError": "Unknown error", "getTagsFailed": "Failed to get tags list", "getNewsFailed": "Failed to get news list", "detail": {"home": "Home", "news": "News", "relatedArticles": "Related Articles", "youMayAlsoLike": "You May Also Like", "addToCartSuccess": "The product has been added to the shopping cart.", "addToCartFailed": "Failed to add", "addToCartError": "Failed to add cart"}}, "discover": {"showAll": "SHOW ALL", "more": "more"}, "footer": {"newsletter": "NEWSLETTER", "stayUpdated": "Stay updated on the new MLK+ collections:", "emailPlaceholder": "E-mail", "subscribe": "SUBSCRIBE", "companyName": "MLK+ OUSAND S.R.L.", "companyAddress": "<PERSON><PERSON>, 47 - 00197 Roma, ITALIA", "taxId": "P. I. 13085581000", "copyright": "Copyright © 2025 MLK+. All Rights Reserved."}, "subscribe": {"title": "Join the MLK+ Community", "description": "Subscribe to the Newsletter", "policy": "If you are a lover of technology and design, sign up to our newsletter and stay updated on the news.", "emailPlaceholder": "Enter your email address", "signUp": "SIGN UP", "emailValidation": "Please input correct email address"}, "search": {"home": "Home", "showHideFilters": "Show/Hide Filters", "byRating": "By rating", "hideAll": "Hide ALL", "showAll": "Show ALL", "addToCartSuccess": "The product has been added to the shopping cart.", "addToCartFailed": "Failed to add", "addToCartError": "Failed to add cart"}}
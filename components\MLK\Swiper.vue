<template>
  <div class="swiper-wrapper">
    <!-- 使用 ClientOnly 确保只在客户端渲染 -->
    <ClientOnly>
      <!-- 使用 v-if 确保数据准备好后再渲染 -->
      <swiper
        v-if="dataList && dataList.length > 0"
        :key="`swiper-${swiperKey}`"
        class="swiper-container"
        :slides-per-view="1"
        :space-between="0"
        :modules="modules"
        :pagination="{ clickable: true }"
        :autoplay="{
          delay: 5000,
          disableOnInteraction: false,
        }"
        :loop="true"
        :effect="'fade'"
        :fade-effect="{
          crossFade: true
        }"
        @swiper="onSwiper"
        @slide-change="onSlideChange"
        @mouseenter="pauseSwiper"
        @mouseleave="resumeSwiper"
      >
        <swiper-slide v-for="(item, index) in dataList" :key="item.image || index">
          <div class="item">
            <img :src="item.image" class="zoom-animation">
            <div class="lint-btn" @click="goToProductDetail(item)">
              {{ t('swiper.shopNow') }}
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </ClientOnly>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineProps, onMounted, nextTick, watch, computed } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination, A11y, Autoplay, EffectFade } from "swiper/modules";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import "swiper/css/effect-fade";

interface RootObject {
  id?: string | number;
  title: string;
  image?: string;
  link?: string;
}
interface Props {
  dataList: RootObject[];
}

const props = defineProps<Props>();
const router = useRouter();
const { t } = useI18n();

const { dataList } = props;

// 跳转到产品详情页
const goToProductDetail = (item: RootObject) => {
  // 假设 item 中有 id 字段，如果没有则使用其他唯一标识
  const productId = item.link;
  router.push(`/product-details/${productId}`);
};


const modules = [Navigation, Pagination, A11y, Autoplay, EffectFade];
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const swiperInstance = ref<any>(null);
const swiperKey = ref(0);
const isInitialized = ref(false);

// 计算属性：确保数据有效
const validDataList = computed(() => {
  return props.dataList && Array.isArray(props.dataList) && props.dataList.length > 0;
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onSwiper = (swiper: any) => {
  // console.warn('🎉 Swiper 实例已成功初始化:', swiper);
  swiperInstance.value = swiper;
  isInitialized.value = true;
  
  // 确保 autoplay 正常工作
  if (swiper.autoplay) {
    swiper.autoplay.start();
  }
};

const onSlideChange = () => {
  // console.warn('幻灯片已切换');
};



// 手动初始化方法
const manualInitSwiper = () => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // console.warn('🔄 开始手动初始化 Swiper');
  
  if (!validDataList.value) {
    // console.warn('❌ 数据无效，无法初始化');
    return;
  }
  
  // 销毁现有实例
  if (swiperInstance.value) {
    try {
      swiperInstance.value.destroy(true, true);
      // console.warn('✅ 现有实例已销毁');
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // console.warn('⚠️ 销毁实例时出错:', error);
    }
    swiperInstance.value = null;
    isInitialized.value = false;
  }
  
  // 强制重新渲染
  swiperKey.value++;
  // console.warn('🔄 组件已重新渲染，key:', swiperKey.value);
  
  // 延迟检查初始化状态
  nextTick(() => {
    setTimeout(() => {
      if (!isInitialized.value) {
        // console.warn('⚠️ 初始化超时，再次尝试');
        swiperKey.value++;
      }
    }, 500);
  });
};

const pauseSwiper = () => {
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.autoplay?.stop();
  }
};

const resumeSwiper = () => {
  if (swiperInstance.value && isInitialized.value) {
    swiperInstance.value.autoplay?.start();
  }
};

// 监听数据变化
watch(() => props.dataList, () => {
  // console.warn('📊 数据已更新:', newDataList?.length);
  
  if (validDataList.value && !isInitialized.value) {
    // console.warn('🔄 数据更新，触发初始化');
    nextTick(() => {
      manualInitSwiper();
    });
  }
}, { immediate: true, deep: true });

// 组件挂载
onMounted(() => {
  // 只在客户端执行
  if (!import.meta.client) return;
  
  // 确保 DOM 完全渲染
  nextTick(() => {
    if (validDataList.value && !isInitialized.value) {
      setTimeout(() => {
        manualInitSwiper();
      }, 100);
    }
  });
});

// 暴露方法给父组件
defineExpose({
  manualInitSwiper,
  isInitialized: computed(() => isInitialized.value)
});

</script>

<style scoped lang="scss">
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}



.swiper-container {
  background-color: white;
}
.swiper {
  width: 100%;
  --swiper-pagination-color: #fff;
  --swiper-theme-color: #fff;
  position: relative;
}

::v-deep .swiper-pagination{
  width: 18.75rem;
  position: absolute;
  left: 92.1875rem;
  bottom: 3.1875rem;
}
::v-deep .swiper-pagination-bullet {
  width: 12px;
  height: .75rem;
  border-radius: 50%;
  background: var(--swiper-pagination-bullet-inactive-color, #fff);
  border: 1px solid #fff;
}

::v-deep .swiper-pagination-bullet-active {
    width: 2.75rem;
    height: .75rem;
    border-radius: .75rem;
    opacity: var(--swiper-pagination-bullet-opacity, 1);
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
}

::v-deep .swiper-slide-active .zoom-animation {
  animation: zoomInOut 2s;
}

::v-deep .swiper-slide-prev .zoom-animation,
::v-deep .swiper-slide-next .zoom-animation {
  animation: none;
  transform: scale(1.1);
}

@keyframes zoomInOut {
  0% {
    transform: scale(1.1);
  }
  // 50% {
  //   transform: scale(1);
  // }
  100% {
    transform: scale(1);
  }
}

.item {
  width: 100%;
  height: 100%;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: transform 0.5s ease-in-out;
  }
  .lint-btn{
    width: 9.5625rem;
    height: 3.75rem;
    border-radius: .5rem;
    line-height: 3.75rem;
    border: 3px solid #FFFFFF;
    text-align: center;
    position: absolute;
    right: 13%;
    bottom: 9.3rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #FFFFFF;
    cursor: pointer;
  }
}
</style>

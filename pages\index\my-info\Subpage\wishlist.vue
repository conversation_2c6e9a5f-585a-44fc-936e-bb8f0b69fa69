<template>
    <div class="wishlist-page">
        <div class="wishlist-title">
            <span>{{ $t('wishlistPage.title') }}</span>
        </div>
        <div v-if="wishlistItems.length === 0" class="empty-wishlist">
            <div class="empty-icon">
                <img :src="useAssetsImage('images/heart.svg')" :alt="$t('wishlistPage.emptyTitle')">
            </div>
            <h3>{{ $t('wishlistPage.emptyTitle') }}</h3>
            <p>{{ $t('wishlistPage.emptyDescription') }}</p>
            <el-button type="primary" @click="goToHome">{{ $t('wishlistPage.goShopping') }}</el-button>
        </div>
        <div v-else class="wishlist-selected">
            <div class="selected-all">
                <el-checkbox v-model="checkAll" size="large" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
                    {{ $t('wishlistPage.selectAll') }}
                </el-checkbox>
            </div>
            <el-checkbox-group v-model="checkedItems">
                <div v-for="item in wishlistItems" :key="item.id" class="selected-box">
                    <div class="close" @click.stop="removeFromWishlist(item)">
                        <img :src="useAssetsImage('images/close.png')" :alt="$t('wishlistPage.delete')">
                    </div>
                    <el-checkbox style="margin: 4.3125rem .625rem 0 1.1875rem ;" :value="item.id.toString()">
                        <div class="checkbox-info">
                            <img :src="item.product.base_image" :alt="item.product.name">
                            <div class="info">
                                <div class="first">
                                    <span :title="item.product.name">{{ item.product.name }}</span>
                                </div>
                                <div class="amount">
                                  <div class="original-price">
                                    {{ item.product.formatted_base_price }}
                                  </div>
                                  <div class="price">
                                    {{ item.product.formatted_price }}
                                  </div>
                                  <div v-if="item.product.discount.has_discount" class="discount">
                                    {{ $t('wishlistPage.save') }} {{ item.product.discount.discount_percentage }} %
                                  </div>
                                </div>
                                <div class="reserve" style="color: #2694FF;">
                                    {{item.product.in_stock ? $t('wishlistPage.inStock') : $t('wishlistPage.outOfStock')}}
                                </div>
                            </div>
                            <div class="button">
                                <div class="add" @click.stop="showDialog(item)">{{ $t('wishlistPage.addToCart') }}</div>
                                <div class="detail" style="margin-top: .75rem;" @click.stop="viewDetails(item)">{{ $t('wishlistPage.viewDetails') }}</div>
                            </div>
                        </div>
                    </el-checkbox>
                </div>
            </el-checkbox-group>
            
            <div v-if="checkedItems.length > 0" class="wishlist-actions">
                <el-button type="primary" @click="addSelectedToCart">{{ $t('wishlistPage.addSelectedToCart') }}</el-button>
                <el-button type="danger" @click="removeSelected">{{ $t('wishlistPage.removeSelected') }}</el-button>
            </div>
        </div>
        <!-- 添加到购物车弹窗 -->
        <el-dialog
          v-model="dialogVisible"
          :title="$t('wishlistPage.addToCartTitle')"
          width="500"
          :before-close="handleClose"
        >
          <div v-if="variantOptions.length > 0 && productType === 'configurable'" class="select-box">
            <el-select v-model="selectedVariant" :teleported="false" :placeholder="$t('wishlistPage.selectVariant')">
              <el-option v-for="variant in variantOptions" :key="variant.id" :label="variant.name" :value="variant.id" />
            </el-select>
          </div>
          <div v-if="colorOptions.length > 0 && productType === 'configurable'" class="select-box">
            <el-select v-model="selectedColor" :teleported="false" :placeholder="$t('wishlistPage.selectColor')">
              <el-option v-for="color in colorOptions" :key="color?.variant_id" :label="color?.option_label" :value="color?.variant_id" />
            </el-select>
          </div>
          <div v-if="brandOptions.length > 0 && productType === 'configurable'" class="select-box">
            <el-select v-model="selectedBrand" :teleported="false" :placeholder="$t('wishlistPage.selectBrand')">
              <el-option v-for="brand in brandOptions" :key="brand.id" :label="brand.name" :value="brand.id" />
            </el-select>
          </div>
          <div v-if="deviceOptions.length > 0 && productType === 'configurable'" class="select-box">
            <el-select v-model="selectedDevice" :teleported="false" :placeholder="$t('wishlistPage.selectDevice')">
              <el-option v-for="device in deviceOptions" :key="device.id" :label="device.name" :value="device.id" />
            </el-select>
          </div>
          <div class="count-box">
            {{ $t('wishlistPage.quantity') }}
            <el-input-number v-model="count" :min="1" :max="100" />
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialogVisible = false">{{ $t('wishlistPage.cancel') }}</el-button>
              <el-button type="primary" @click="addToCart">
                {{ $t('wishlistPage.confirm') }}
              </el-button>
            </div>
          </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { getProductsDetail } from '~/api/channel'
import { getShoppingCartAdd } from '~/api/shop'
import type { CheckboxValueType } from 'element-plus'
import { useWishlistStore } from '~/stores/wishlist'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 心愿单商品类型定义
interface WishlistItem {
  id: number
  product: {
    id: number
    url_key: string
    name: string
    base_image: string
    formatted_base_price: string
    formatted_price: string
    discount: {
      has_discount: boolean
      discount_percentage?: number
    }
    in_stock: boolean
  }
}


// 使用 useAssets composable
const { useAssetsImage } = useAssets()


// i18n
const { t } = useI18n()

// 心愿单 store
const wishlistStore = useWishlistStore()


// 心愿单商品列表
const wishlistItems = computed(() => wishlistStore.wishlistItems)
// 选中的商品
const checkedItems = ref<string[]>([])

// 弹窗相关
const dialogVisible = ref(false)

// 选择器相关变量
const selectedVariant = ref('')
const selectedColor = ref('')
const selectedBrand = ref('')
const selectedDevice = ref('')
const count = ref(1)
const productData = ref<{ id?: number }>({})

// 选项数据
const variantOptions = ref<Array<{id: string, name: string}>>([])
const colorOptions = ref<Array<{variant_id: string, option_label: string}>>([])
const brandOptions = ref<Array<{id: string, name: string}>>([])
const deviceOptions = ref<Array<{id: string, name: string}>>([])
const allDeviceOptions = ref<Array<{ id: string, name: string, parent_id?: string }>>([])
const productType = ref('')
const superAttribute = ref()

// 监听selectedBrand变化，过滤deviceOptions
watch(selectedBrand, (newBrandId) => {
  if (newBrandId) {
    // 根据选中的品牌ID过滤设备选项
    deviceOptions.value = allDeviceOptions.value.filter(device => device.parent_id === newBrandId)
    // 重置设备选择
    selectedDevice.value = ''
  } else {
    // 如果没有选择品牌，显示所有设备
    deviceOptions.value = allDeviceOptions.value
  }
})

// 处理弹窗关闭
const handleClose = () => {
  dialogVisible.value = false
  // 重置所有选择器
  selectedVariant.value = ''
  selectedColor.value = ''
  selectedBrand.value = ''
  selectedDevice.value = ''
  count.value = 1
  productData.value = {}
  // 重置选项数据
  variantOptions.value = []
  colorOptions.value = []
  brandOptions.value = []
  allDeviceOptions.value = []
  deviceOptions.value = []
  productType.value = ''
}

// 是否全选
const checkAll = computed({
  get: () => {
    return wishlistItems.value.length > 0 && checkedItems.value.length === wishlistItems.value.length
  },
  set: (value: boolean) => {
    checkedItems.value = value ? wishlistItems.value.map(item => item.id.toString()) : []
  }
})

// 全选框是否半选状态
const isIndeterminate = computed(() => {
  return checkedItems.value.length > 0 && checkedItems.value.length < wishlistItems.value.length
})

// 处理全选变化
const handleCheckAllChange = (val: CheckboxValueType) => {
  checkedItems.value = val ? wishlistItems.value.map(item => item.id.toString()) : []
}

// 添加商品到购物车
const showDialog = async (_item: WishlistItem) => {
  dialogVisible.value = true
  const res = await getProductsDetail({ url_key: _item.product?.url_key })
  productData.value = res.data?.product
  superAttribute.value = res.data?.product.attributes
  variantOptions.value = res.data?.product?.variants || []
  colorOptions.value = res.data?.product?.color || []
  brandOptions.value = res.data?.brands || []
  allDeviceOptions.value = res.data?.devices || []
  deviceOptions.value = res.data?.devices || []
  productType.value = res.data?.product?.type
  
  // 重置选择器
  selectedVariant.value = ''
  selectedColor.value = ''
  selectedBrand.value = ''
  selectedDevice.value = ''
  count.value = 1
}

const addToCart = async () => {
  dialogVisible.value = false
  if(productType.value === 'configurable'){
    if (productData.value?.id) {
      const res = await getShoppingCartAdd({
        product_id: productData.value.id,
        quantity: count.value,
        selected_configurable_option: selectedVariant.value ? parseInt(selectedVariant.value) : undefined,
        super_attribute: {
          '23': selectedColor.value,
          '25': selectedBrand.value,
          '33': selectedDevice.value
        }
      })
      if (res.success) {
        ElMessage.success(t('wishlistPage.addToCartSuccess'))
      } else {
        ElMessage.error(res.message || t('wishlistPage.addToCartFailed'))
      }
    }
  } else {
    if (productData.value?.id) {
      const res = await getShoppingCartAdd({
        product_id: productData.value.id,
        quantity: count.value
      })
      if (res.success) {
        ElMessage.success(t('wishlistPage.addToCartSuccess'))
      } else {
        ElMessage.error(res.message || t('wishlistPage.addToCartFailed'))
      }
    }
  }

}

// 查看商品详情
const viewDetails = (item: WishlistItem) => {
  const router = useRouter()
  router.push(`/product-details/${item?.product?.url_key}.html`)
}

// 从心愿单移除商品
const removeFromWishlist = async (item: WishlistItem) => {
  try {
    const result = await wishlistStore.removeFromWishlist(item.id)
    if (result.success) {
      ElMessage.success(t('wishlistPage.removeSuccess'))
      // 从选中列表中移除
      const index = checkedItems.value.indexOf(item.id.toString())
      if (index > -1) {
        checkedItems.value.splice(index, 1)
      }
    } else {
      ElMessage.error(result.message || t('wishlistPage.removeFailed'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('wishlistPage.removeFailed'))
    }
  }
}

// 添加选中的商品到购物车
const addSelectedToCart = async () => {
  try {
    const selectedItems = wishlistItems.value.filter(item => checkedItems.value.includes(item.id.toString()))
    let successCount = 0
    
    for (const item of selectedItems) {
      const result = await wishlistStore.moveToShoppingCart(item)
      
      if (result.success) {
        successCount++
      }
    }
    
    if (successCount > 0) {
      ElMessage.success(t('wishlistPage.addToCartSuccess', { count: successCount }))
    } else {
      ElMessage.error(t('wishlistPage.addToCartFailed'))
    }
  } catch (error) {
    console.error('批量添加购物车失败:', error)
    ElMessage.error(t('wishlistPage.batchAddToCartError'))
  }
}

// 移除选中的商品
const removeSelected = async () => {
  try {
    await ElMessageBox.confirm(t('wishlistPage.removeConfirmMessage', { count: checkedItems.value.length }), t('wishlistPage.removeConfirmTitle'), {
      confirmButtonText: t('wishlist.confirm'),
      cancelButtonText: t('wishlist.cancel'),
      type: 'warning'
    })
    
    const selectedItems = wishlistItems.value.filter(item => checkedItems.value.includes(item.id.toString()))
    let successCount = 0
    
    for (const item of selectedItems) {
      const result = await wishlistStore.removeFromWishlist(item.id)
      if (result.success) {
        successCount++
      }
    }
    
    if (successCount > 0) {
      ElMessage.success(t('wishlistPage.removeSelectedSuccess', { count: successCount }))
      checkedItems.value = []
    } else {
      ElMessage.error(t('wishlistPage.removeFailed'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量移除失败:', error)
      ElMessage.error(t('wishlistPage.batchRemoveError'))
    }
  }
}

// 跳转到首页
const goToHome = () => {
  const router = useRouter()
  router.push('/home')
}

// 组件挂载时初始化心愿单数据
onMounted(async () => {
  await wishlistStore.initWishlist()
})
</script>

<style scoped lang="scss">

::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: black;
    border: .0625rem solid black;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: black;
    border-color: black;
}
::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
    color: black;
}

.wishlist-page {
    width: 100%;
    background: #FFFFFF;
    border-radius: .9375rem;

    .select-box{
      margin-top: 1.25rem;
    }

    .count-box{
      margin-top: 1.25rem;
    }

    .wishlist-title {
        padding: 1.25rem 0;
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }

    .empty-wishlist {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 2rem;
        text-align: center;

        .empty-icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;

            img {
                width: 100%;
                height: 100%;
            }
        }

        h3 {
            font-size: 1.5rem;
            color: #3D3D3D;
            margin-bottom: 0.5rem;
        }

        p {
            color: #979797;
            margin-bottom: 2rem;
        }
    }

    .wishlist-selected {
        width: 65.25rem;
        margin: 0 auto;
        padding-bottom: 2rem;

        .selected-all {
            margin: 0 1.1875rem;
        }

        .selected-box {
            margin: 1.1875rem 0 2rem 0;
            width: 100%;
            height: 11.5rem;
            background: #FFFFFF;
            border-radius: .5rem;
            border: .0625rem solid #EBEBEB;
            position: relative;
            .checkbox-info {
                width: 57.5rem;
                height: 6.25rem;
                display: flex;
                img{
                      width: 6.125rem;
                      height: 6.125rem;
                    }
                .info {
                    margin-left: 1rem;
                    width: 20.375rem;
                    height: 5.9375rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    
                    .first {
                      width: 20.375rem;
                      height: 1.1875rem;
                      font-family: SFProDisplay, SFProDisplay;
                      font-weight: 400;
                      font-size: 1rem;
                      color: #3D3D3D;
                      line-height: 1.1875rem;
                      text-align: left;
                      font-style: normal;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                        
                    }
                    ::v-deep .el-text{
                        font-weight: 400;
                        font-size: 1rem;
                    }
                    .amount {
                        width: 13.75rem;
                        height: 1.25rem;
                        display: flex;
                        justify-content: space-between;
                        .original-price{
                          height: 1.1875rem;
                          font-family: SFProDisplay, SFProDisplay;
                          font-weight: 400;
                          font-size: 1rem;
                          color: #3D3D3D;
                          line-height: 1.1875rem;
                          text-align: left;
                          font-style: normal;
                          text-decoration-line: line-through;
                        }
                        .price{
                          height: 1.1875rem;
                          font-family: SFProDisplay, SFProDisplay;
                          font-weight: 400;
                          font-size: 1rem;
                          color: #3D3D3D;
                          line-height: 1.1875rem;
                          text-align: left;
                          font-style: normal;
                        }
                        .discount{
                          height: 1.1875rem;
                          font-family: SFProDisplay, SFProDisplay;
                          font-weight: 400;
                          font-size: 1rem;
                          color: #2ABC80;
                          line-height: 1.1875rem;
                          text-align: left;
                          font-style: normal;
                        }
                    }
                    .reserve {
                      height: 1.3125rem;
                      font-family: SFProDisplay, SFProDisplay;
                      font-weight: bold;
                      font-size: 1.125rem;
                      color: #2694FF;
                      line-height: 1.3125rem;
                      text-align: left;
                      font-style: normal;
                    }
                }
                .button {
                    margin-left: auto;
                    margin-right: 0;
                    div {
                        padding: 0 1.25rem;
                        height: 2.5rem;
                        background: #FFFFFF;
                        border-radius: .5rem;
                        border: .0625rem solid #979797;
                        font-family: SFProDisplay, SFProDisplay;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #3D3D3D;
                        line-height: 2.5rem;
                        text-align: center;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover {
                            background: #3D3D3D;
                            color: #FFFFFF;
                        }

                        &.add {
                            background: #3D3D3D;
                            color: #FFFFFF;
                            border-color: #3D3D3D;

                            &:hover {
                                background: #555555;
                            }
                        }
                    }
                }
            }
            .close {
                position: absolute;
                right: -1.5rem;
                top: -1.5rem;
                width: 1.5rem;
                height: 1.5rem;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.1);
                }

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .wishlist-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding: 1rem;
            border-top: 1px solid #EBEBEB;
        }
    }
}
</style>
import { ref } from 'vue'
import { getTableData } from "~/api/table";

export const useTableStore = defineStore('table', () => {
    const tableData = ref([])
    interface dataType {
        page?: number,
        per_page?: number,
        status?: string,
        sort?: string,
        order_by?: string,
    }
    const getTableItems = async (data:dataType) => {
        try {
            const res = await getTableData(data)
            return res
        } catch (error) {
            console.error('获取订单列表失败:', error)
        }
    }

    return { tableData, getTableItems }
})
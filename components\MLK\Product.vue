<template>
  <div v-if="productData" class="product" @click="handleProductClick"  @mouseenter="handleMouseEnter"  @mouseleave="handleMouseLeave">
    <div class="top">
      <div class="off">{{productData?.discount?.has_discount ? productData?.discount?.discount_percentage + '%OFF' :''}} </div>
      <div class="wishlist-icon" @click.stop="productData && handleWishlistToggle(productData)">
        <img 
          :src="isInWishlist ? useAssetsImage('images/love-active.png') : useAssetsImage('images/love.png')" 
          :alt="isInWishlist ? $t('product.removeFromWishlist') : $t('product.addToWishlist')"
          :class="{ 'wishlist-active': isInWishlist }"
        >
      </div>
    </div>
    <div class="cp-img">
      <img :src="currentImage" :alt="productData?.name">
    </div>
    <div class="cp-name" :title="productData?.name">
      {{productData?.name}}
    </div>
    <div class="cp-content" :title="productData?.description">
      {{productData?.description}}
    </div>
    <div class="bottom">
      <div class="price">
        {{productData?.formatted_price}}
      </div>
      <div class="shop-icon" @click.stop="productData && handleAddToCart(productData)">
          <img :src="useAssetsImage('images/cart-black.png')" :alt="$t('product.shoppingCart')">
      </div>
    </div>
  </div>
  <div v-else class="product product-loading">
    <div class="loading-placeholder">
      <div class="loading-image"/>
      <div class="loading-text"/>
      <div class="loading-text short"/>
      <div class="loading-price"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useShoppingCartStore } from '~/stores/shopping-cart'
import { useWishlistStore } from '~/stores/wishlist'
import { useUserStore } from '~/stores/user'
import { ElMessage } from 'element-plus'

// 使用 useAssets composable
const { useAssetsImage } = useAssets()

interface ProductData {
  id: number
  url_key: string
  name: string
  price: number
  base_image: string
  images?: Array<{ url: string }>
  formatted_price: string
  description: string
  discount?: {
    has_discount: boolean
    discount_percentage: number
  }
}

const props = defineProps<{
  productData: ProductData | null
}>()
const { productData } = toRefs(props)


const router = useRouter()

// 购物车 store
const shoppingCartStore = useShoppingCartStore()

// 心愿单 store
const wishlistStore = useWishlistStore()

// 用户 store
const userStore = useUserStore()

// 当前显示的图片
const currentImage = ref('')

// 计算属性：检查商品是否在心愿单中
const isInWishlist = computed(() => {
  return productData.value ? wishlistStore.isInWishlist(productData.value.id) : false
})


// 鼠标悬浮事件处理
const handleMouseEnter = () => {
  if (productData.value?.images && productData.value.images.length > 1) {
    // 如果有第二张图片，显示第二张
    currentImage.value = productData.value.images[1].url
  } else {
    // 如果没有第二张图片，显示第一张
    currentImage.value = productData.value?.images?.[0]?.url || productData.value?.base_image || ''
  }
}

const handleMouseLeave = () => {
  // 鼠标离开时显示第一张图片
  currentImage.value = productData.value?.images?.[0]?.url || productData.value?.base_image || ''
}

// 初始化当前图片
const initCurrentImage = () => {
  currentImage.value = productData.value?.images?.[0]?.url || productData.value?.base_image || ''
}

// 监听productData变化，更新当前图片
watch(productData, () => {
  initCurrentImage()
}, { immediate: true })

const handleProductClick = () => {
  if (productData.value?.url_key) {
    // 根据用户是否为批发商决定跳转路径
    const isWholesale = userStore.isWholesale
    const path = isWholesale 
      ? `/product-wholesale-details/${productData.value?.url_key}.html`
      : `/product-details/${productData.value?.url_key}.html`
    router.push({
      path: path,
    })
  }
}

const handleAddToCart = async (product: ProductData) => {
  try {
    if (!product || !product.id) {
      console.error('商品数据无效:', product)
      ElMessage.error($t('product.invalidProduct'))
      return
    }

    console.warn('开始添加商品到购物车:', product)
    
    // 确保价格是数字类型
    const price = typeof product.price === 'number' ? product.price : parseFloat(product.formatted_price.replace(/[^0-9.]/g, ''))
    
    const result = await shoppingCartStore.addToCart({
      id: product.id.toString(),
      product_id: product.id.toString(),
      name: product.name,
      price: price,
      base_image: product.base_image,
      formatted_price: product.formatted_price,
      short_description: product.description
    })
    
    if (result?.success) {
      // 添加成功提示
      ElMessage.success($t('product.addToCartSuccess'))
      console.warn('添加购物车成功')
    } else {
      // 添加失败提示
      ElMessage.error(result?.message || $t('product.addToCartFailed'))
      console.error('添加购物车失败:', result?.message)
    }
  } catch (error) {
    console.error('添加购物车出错:', error)
    ElMessage.error($t('product.addToCartError'))
  }
}

const handleWishlistToggle = async (product: ProductData) => {
  if (!product || !product.id) {
    ElMessage.error($t('product.invalidProduct'))
    return
  }

  try {
    if (isInWishlist.value) {
      // 如果已收藏，则取消收藏
      const wishlistItem = wishlistStore.getWishlistItem(product.id)
      if (wishlistItem) {
        await wishlistStore.removeFromWishlist(wishlistItem.id)
      }
    } else {
      // 构造符合 ProductData 接口的数据
      const productData = {
        id: product.id,
        url_key: product.url_key,
        name: product.name,
        formatted_base_price: product.formatted_price,
        formatted_price: product.formatted_price,
        base_image: product.base_image,
        discount: {
          has_discount: product.discount?.has_discount || false,
          regular_price: 0,
          is_special_price_active: false,
          special_price: 0,
          special_price_from: '',
          special_price_to: '',
          discount_percentage: product.discount?.discount_percentage || 0
        },
        in_stock: true
      }
      await wishlistStore.addToWishlist(productData)
    }
  } catch (error) {
    console.error('收藏操作时发生错误:', error)
    ElMessage.error($t('product.wishlistToggleError'))
  }
}


// 组件挂载时初始化心愿单数据和当前图片
onMounted(async () => {
  await wishlistStore.initWishlist()
  await shoppingCartStore.fetchCartData()
  initCurrentImage()
})
</script>

<style lang="scss" scoped>
.product{
  width: 16.75rem;
  height: 23.375rem;
  box-sizing: border-box;
  padding:0 1rem;
  background: #F4F4F4;
  box-shadow: -0.125rem .1875rem .75rem 0rem rgba(202,202,202,0.5);
  border-radius: .5625rem;
  flex-shrink:0;
  cursor: pointer;
  &:hover{
     background: #fff;
  }

  .top{
    width: 100%;
    box-sizing: border-box;
    padding-top:1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .off{
      height: 1.5625rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 1.125rem;
      color: #FE8C5F;
      line-height: 1.5625rem;
      text-shadow: -0.125rem .1875rem .75rem rgba(202,202,202,0.5);
      text-align: left;
      font-style: normal;
    }
    
    .wishlist-icon{
      width: 1.5rem;
      height: 1.5rem;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover{
        transform: scale(1.1);
      }
      
      img{
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;
        
        &.wishlist-active{
          animation: heartBeat 0.6s ease-in-out;
        }
      }
    }
  }
  
  .cp-img{
    height: 9.1875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      height: 100%;
    }
  }
  .cp-name{
    width: 100%;
    margin-top: 1.4375rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 1.125rem;
    color: #000000;
    text-shadow: -2px 3px 12px rgba(202,202,202,0.5);
    text-align: left;
    font-style: normal;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
  .cp-content{
    width: 100%;
    height: 2.375rem;
    font-family: AppleSystemUIFont;
    font-size: .875rem;
    color: #919191;
    line-height: 1.1875rem;
    text-shadow: -2px 3px 12px rgba(202,202,202,0.5);
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: .125rem;
    display: -webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:2;
    line-clamp:2;
    overflow:hidden;
  }
  .bottom{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.875rem 0;
    .price{
      font-family: AppleSystemUIFont;
      font-size: 1.5rem;
      color: #000000;
      text-shadow: -2px 3px 12px rgba(202,202,202,0.5);
      font-style: normal;
      text-transform: none;
    }
    .shop-icon{
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      box-shadow: -2px 3px 12px 0px rgba(202,202,202,0.5);
      img{
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 心跳动画
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

// 加载动画
@keyframes loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.product-loading {
  .loading-placeholder {
    padding: 1.5rem 1rem;
    
    .loading-image {
      width: 100%;
      height: 9.1875rem;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: loading 1.5s infinite;
      border-radius: 0.5rem;
      margin-bottom: 1.4375rem;
    }
    
    .loading-text {
      width: 100%;
      height: 1.125rem;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: loading 1.5s infinite;
      border-radius: 0.25rem;
      margin-bottom: 0.5rem;
      
      &.short {
        width: 60%;
        height: 0.875rem;
      }
    }
    
    .loading-price {
      width: 40%;
      height: 1.5rem;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: loading 1.5s infinite;
      border-radius: 0.25rem;
    }
  }
}
</style>
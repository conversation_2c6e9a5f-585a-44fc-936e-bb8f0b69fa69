<template>
    <div class="older-page">
        <div class="older-title">
            <span>My Orders</span>
            <div style="float: right;margin-right: 25px;">
                <el-icon>
                    <ArrowRightBold />
                </el-icon>
            </div>
        </div>
        <div class="older-tab">
            <div>
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane v-for="(item, index) in catalogue" :key="index" :label="item" :name="index">
                        <template #default>
                            <div>
                                <MLKMyPurchaseDetails />
                            </div>
                        </template>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <div class="bottom-box">
            <MLKMyPurchaseDetails />
        </div>
        <div class="paging">
            <div class="paging-box">
                <el-pagination size="large" background layout="prev, pager, next" prev-icon="CaretLeft" next-icon="CaretRight" :total="1000" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'

const activeName = ref(0)
const catalogue = ref(['All Orders', 'Pending Payment', 'Processing', 'Shipped', 'Completed', 'Review', 'Payment Review', 'Cancelled'])


// 标签页切换
const handleClick = (tab: TabsPaneContext, event: Event) => {
    console.log(tab, event)
}
</script>

<style scoped lang="scss">
::v-deep .el-tabs--top>.el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 1.25rem;
}

::v-deep .el-tabs__active-bar {
    background-color: #3D3D3D;
}

::v-deep .el-tabs__item.is-active {
    color: #3D3D3D;
    font-family: SFProDisplay, SFProDisplay;
    font-weight: bold;
    font-size: 1.125rem;
    font-style: normal
}

::v-deep .el-tabs__item:hover {
    color: #3D3D3D;
}

::v-deep .el-tabs__nav-wrap:after {
    background-color: #FFFFFF;
}

.older-page {
    width: 100%;

    .older-title {
        padding: 1.25rem 0;
        height: 3.5rem;
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        font-family: SFProDisplay, SFProDisplay;
        font-weight: bold;
        font-size: 1.5rem;
        color: #3D3D3D;
        line-height: 3.5rem;
        text-align: left;
        font-style: normal;

        span {
            margin-left: 2.375rem;
        }
    }

    .older-tab {
        background-color: #FFFFFF;
        border-top: .0625rem solid #EBEBEB;
    }

    .bottom-box {
        margin-top: 1.875rem;
    }
    .paging {
        margin: 50px 0px;
        .paging-box {
            width: 25rem;
            margin: 0 auto;
            ::v-deep .el-pagination.is-background .el-pager li.is-active{
                background-color: #000000;
                color: var(--el-color-white)
            }
        }
    }
}
</style>